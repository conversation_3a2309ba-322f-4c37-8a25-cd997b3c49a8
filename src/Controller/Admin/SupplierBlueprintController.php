<?php

namespace App\Controller\Admin;

use App\Entity\Tracker\Stock;
use App\Entity\Tracker\Supplier;
use App\Entity\Tracker\SupplierBlueprint;
use App\Entity\Tracker\SupplierOrder;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Sonata\AdminBundle\Admin\AdminInterface;
use Sonata\AdminBundle\Admin\Pool;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @phpstan-extends GravitiqCrudController<SupplierBlueprint>
 */
class SupplierBlueprintController extends GravitiqCrudController
{
    /**
     * @param ProxyQueryInterface<SupplierBlueprint> $query
     * @param AdminInterface<SupplierBlueprint> $admin
     * @param ManagerRegistry $doctrine
     * @param Pool $adminPool
     * @return Response
     * @noinspection PhpUnusedParameterInspection
     * @noinspection PhpUnused - batch action defined in SupplierBlueprintAdmin
     */
    public function batchActionCreateOrder(ProxyQueryInterface $query, AdminInterface $admin, ManagerRegistry $doctrine, Pool $adminPool): Response
    {
        $selectedEntities = $query->execute();
        $supplier = null;

        $order = new SupplierOrder();
        $order->setOrderDate(new \DateTime());

        /** @var SupplierBlueprint $entity */
        foreach ($selectedEntities as $entity) {
            if (is_null($supplier)) {
                $supplier = $entity->getSupplier();
            } elseif ($supplier !== $entity->getSupplier()) {
                throw new \InvalidArgumentException("Cannot create an order for items from more than one Supplier");
            }

            $item = new Stock();
            $item->setBlueprint($entity->getBlueprint());
            $order->addItem($item);
        }

        if (empty($supplier)) {
            return new Response('No items were selected');
        }

        $order->setSupplier($supplier);

        $em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
        try {
            $em->persist($order);
            $em->flush();
        } catch (\Exception $e) {
            $this->addFlashError($e->getMessage());
            return $this->redirectToList();
        }

        $soAdmin = $adminPool->getAdminByClass(SupplierOrder::class);
        return new RedirectResponse(
            $soAdmin->generateUrl('edit', [
                'id' => $order->getId()
            ])
        );
    }

    protected function preCreate(Request $request, object $object): ?Response
    {
        if ($supplierId = $request->query->get('supplier')) {
            $pool = $this->container->get('sonata.admin.pool');
            $shipmentAdmin = $pool->getAdminByClass(Supplier::class);
            /** @var Supplier $supplier */
            $supplier = $shipmentAdmin->getObject($supplierId);

            /** @var SupplierOrder $object */
            $object->setSupplier($supplier);
        }
        return null;
    }
}