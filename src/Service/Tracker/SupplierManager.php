<?php

namespace App\Service\Tracker;

use App\Entity\Fx\Currency;
use App\Entity\MerchantSku;
use App\Entity\Tracker\Blueprint;
use App\Entity\Tracker\BoxPlan;
use App\Entity\Tracker\Stock;
use App\Entity\Tracker\Supplier;
use App\Entity\Tracker\SupplierBlueprint;
use App\Entity\Tracker\SupplierOrder;
use App\Entity\Tracker\SupplierOrderStockDoc;
use App\Entity\Tracker\SupplierPriceListDoc;
use App\Repository\MerchantSkuRepository;
use App\Repository\Tracker\BlueprintRepository;
use App\Repository\Tracker\BoxPlanRepository;
use App\Repository\Tracker\SupplierBlueprintRepository;
use App\Repository\Tracker\SupplierOrderRepository;
use App\Repository\Tracker\SupplierRepository;
use App\Service\FileSystemSwitchboard;
use App\Service\Trait\FileSystemSwitchboardTrait;
use App\Service\Trait\HasEntityManagerTrait;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Exception as ReaderException;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Log\LoggerInterface;

class SupplierManager
{
    use FileSystemSwitchboardTrait, HasEntityManagerTrait;

    private const string COUNTRY_REGEX = '/^[A-Z]{2}$/';
    private const string CURRENCY_REGEX = '/^[A-Z]{3}$/';
    private const string PRICE_REGEX = '/^\d+(\.\d{1,3})?$/'; // can have fractional price, e.g. $0.123 per unit

    public const array HEADER_SUPPLIER_PRICE_LIST = [
        'SUPPLIER_ID'   => 'SupplierID',
        'ISKU'          => 'Blueprint ISKU',
        'PRODUCT_CODE'  => 'productCode',
        'LEAD_TIME'     => 'leadTime (days)',
        'CURRENCY'      => 'currency',
        'QTY_NOTES'     => 'qty notes',
        'QTY_1'         => 'qty1',
        'PRICE_1'       => 'price1',
        'QTY_2'         => 'qty2',
        'PRICE_2'       => 'price2',
        'QTY_3'         => 'qty3',
        'PRICE_3'       => 'price3',
        'QTY_4'         => 'qty4',
        'PRICE_4'       => 'price4',
        'QTY_5'         => 'qty5',
        'PRICE_5'       => 'price5',
        'VALID_FROM'    => 'validFrom',
        'VALID_TO'      => 'validTo',
        'NOTES'         => 'notes',
    ];

    public const array HEADER_SUPPLIER_ORDER_STOCK = [
        'SUPPLIER_ORDER_ID'             => 'SupplierOrder',
        'ISKU'                          => 'iSKU',
        'BOX_PLAN'                      => 'BoxPlan',
        'UNITS_ORDERED'                 => 'UnitsOrdered',
        'UNITS_DELIVERED'               => 'UnitsDelivered',
        'PREP_NOTES'                    => 'Prep Notes',
        'PREP_COST_PER_UNIT'            => 'PrepCostPerUnit',
        'PRODUCTION_COST'               => 'ProductionCost',
        'PROD_COST_CONFIRMED'           => 'ProdCostConfirmed',
        'PROD_START_DATE'               => 'ProdStartDate',
        'EXPECTED_GOODS_READY_DATE'     => 'ExpectedGoodsReadyDate',
        'FOR_COUNTRY'                   => 'ForCountry',
        'MSKU'                          => 'mSKU',
        'ACTUAL_FIRST_GOODS_READY_DATE' => 'actualFirstGoodsReadyDate',
        'ACTUAL_GOODS_READY_DATE'       => 'actualGoodsReadyDate',
        'LABELLING'                     => 'labelling',
    ];

    public const array ADMIN_NOT_REQUIRED_IMPORT_HEADERS = [
        self::HEADER_SUPPLIER_ORDER_STOCK['SUPPLIER_ORDER_ID'],
        self::HEADER_SUPPLIER_ORDER_STOCK['MSKU'],
        self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_FIRST_GOODS_READY_DATE'],
        self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_GOODS_READY_DATE'],
    ];

    const string SHEET_NAME_SUPPLIER_PRICE_LIST = 'PriceList';
    const string SHEET_NAME_SUPPLIER_ORDER_STOCK = 'Stock';
    const string DEFAULT_SUPPLIER_PRICE_LIST_VALID_FROM = 'today';
    const string DEFAULT_SUPPLIER_PRICE_LIST_VALID_TO = '2030-01-01';

    public function __construct(
        protected LoggerInterface   $logger,
        ManagerRegistry             $doctrine,
        FileSystemSwitchboard       $switchboard,
        protected string            $fileSystemUploadsAdmin,
    ) {
        $this->setEmFromDoctrine($doctrine);
        $this->setFileSystemSwitchboard($switchboard, $fileSystemUploadsAdmin);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param SupplierPriceListDoc $priceListDoc
     * @param bool $doFlush
     * @return array{message: string}
     * @throws \Exception
     */
    public function parseSupplierPriceListDoc(SupplierPriceListDoc $priceListDoc, bool $doFlush=false): array
    {
        $priceListFile = $this->castFileSystemFileToLocalFile($priceListDoc);
        $filePath = GravitiqTools::getFilePath($priceListFile, 'parseSupplierPriceListDoc');

        $dataArray = $this->getDataArrayFromXLSXFile($filePath, self::SHEET_NAME_SUPPLIER_PRICE_LIST);
        [$validatedDataArray, $validationErrors] = $this->validateAndSanitizeSupplierPriceListData($dataArray);
        if (!empty($validationErrors)) {
            foreach ($validationErrors as $error) {
                $priceListDoc->addMessageError($error);
            }
            $priceListDoc->addMessageError('(PriceList file was not processed due to errors)');
            return ['message' => "Failed to parse supplier price list file due to validation errors."];
        }

        $supplierIds = [];
        $blueprintSkus = [];
        foreach ($validatedDataArray as $data) {
            $supplierIds[] = $data[self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID']];
            $blueprintSkus[] = $data[self::HEADER_SUPPLIER_PRICE_LIST['ISKU']];
        }

        /** @var BlueprintRepository $blueprintRepo */
        $blueprintRepo = $this->getRepository(Blueprint::class);
        /** @var SupplierRepository $supplierRepo */
        $supplierRepo = $this->getRepository(Supplier::class);
        $blueprintsData = $blueprintRepo->retrieveByIskus($blueprintSkus);
        $suppliers = $supplierRepo->retrieveByIdsIndexedById($supplierIds);

        foreach ($validatedDataArray as $data) {
            if (empty($suppliers[$data[self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID']]])) {
                $priceListDoc->addMessageError("SupplierID '{$data[self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID']]}' not found in database.");
            }
            if (empty($blueprintsData[$data[self::HEADER_SUPPLIER_PRICE_LIST['ISKU']]])) {
                $priceListDoc->addMessageError("Blueprint ISKU '{$data[self::HEADER_SUPPLIER_PRICE_LIST['ISKU']]}' not found in database.");
            }
        }
        if ($priceListDoc->hasErrors()) {
            $priceListDoc->addMessageError('(PriceList file was not processed due to errors)');
            return [
                'message' => "Failed to parse supplier price list file.",
            ];
        }

        $newSupplierBlueprints = [];
        foreach ($validatedDataArray as $data) {
            $newSupplierBlueprint = $this->createSupplierBlueprint(
                $suppliers[$data[self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID']]],
                $blueprintsData[$data[self::HEADER_SUPPLIER_PRICE_LIST['ISKU']]],
                $data
            );
            $newSupplierBlueprints[] = $newSupplierBlueprint;
        }

        /** @var SupplierBlueprintRepository $supplierBlueprintRepo */
        $supplierBlueprintRepo = $this->getRepository(SupplierBlueprint::class);
        $keyedExistingSupplierBlueprints = $supplierBlueprintRepo->retrieveOverlappingEntities($newSupplierBlueprints);
        foreach ($newSupplierBlueprints as $newSupplierBlueprint) {
            $key1 = $newSupplierBlueprint->generateKeyNotBasedOnValidityDates();
            $newIsInteresting = true;
            if (!empty($keyedExistingSupplierBlueprints[$key1])) {
                foreach ($keyedExistingSupplierBlueprints[$key1] as $existingSb) {
                    try {
                        $newIsInteresting = $this->adjustExistingSbBasedOnNewTrustedSb($existingSb, $newSupplierBlueprint);
                    } catch (\RuntimeException $e) {
                        $priceListDoc->addMessageError($e->getMessage());
                        throw new \RuntimeException($e->getMessage());
                    }
                    if (!$newIsInteresting) {
                        break;
                    }
                }
            }
            if ($newIsInteresting) {
                $this->persist($newSupplierBlueprint);
            }
        }

        if ($doFlush) {
            $this->flushChanges();
        }

        $priceListDoc->addMessageSuccess("Successfully parsed supplier price list file.");
        return ['message' => "Successfully parsed supplier price list file."];
    }

    /**
     * @param SupplierOrderStockDoc $stockDoc
     * @param bool $doFlush
     * @return array{message: string}
     * @throws \Exception
     */
    public function parseSupplierOrderStockDoc(SupplierOrderStockDoc $stockDoc, bool $doFlush=false): array
    {
        $stockFile = $this->castFileSystemFileToLocalFile($stockDoc);
        $filePath = GravitiqTools::getFilePath($stockFile, 'parseSupplierOrderStockDoc');

        $dataArray = $this->getDataArrayFromXLSXFile($filePath, self::SHEET_NAME_SUPPLIER_ORDER_STOCK);
        [$validatedDataArray, $validationErrors] = $this->validateAndSanitizeSupplierOrderStockData($dataArray);

        if (!empty($validationErrors)) {
            foreach ($validationErrors as $error) {
                $stockDoc->addMessageError($error);
            }
            $stockDoc->addMessageError('(StockDoc file was not processed due to errors)');
            return ['message' => "Failed to parse StockDoc file due to validation errors."];
        }

        list($supplierOrder, $blueprints, $boxPlans, $merchantSkus) = $this->retrieveAndValidateLinkedObjectsForSupplierOrderStock($stockDoc, $validatedDataArray);
        if ($stockDoc->hasErrors()) {
            $stockDoc->addMessageError('(StockDoc file was not processed due to linked object errors)');
            return ['message' => "Failed to parse StockDoc file due to linked object errors."];
        }

        foreach ($validatedDataArray as $data) {
            $blueprint = $blueprints[$data[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']]];
            $boxPlan = $boxPlans[$data[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']]] ?? null;
            $msku = null;
            if (!empty($data[self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']])) {
                $key = $data[self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']] . '~' . $blueprint->getIsku();
                if (!empty($merchantSkus[$key])) {
                    $msku = $merchantSkus[$key];
                }
            }

            $newStock = $this->createSupplierOrderStock($supplierOrder, $blueprint, $data, $boxPlan, $msku);
            $this->persist($newStock);
        }

        if ($doFlush) {
            $this->flushChanges();
        }

        $stockDoc->addMessageSuccess("Successfully parsed SupplierOrderStockDoc file.");
        return [
            'message' => "Successfully parsed SupplierOrderStockDoc file.",
        ];
    }

    /**
     * @param SupplierOrderStockDoc $stockDoc
     * @param list<array<string, mixed>> $validatedDataArray
     * @return array{?SupplierOrder, array<string, Blueprint>, array<string, BoxPlan>, array<string, MerchantSku>};
     */
    protected function retrieveAndValidateLinkedObjectsForSupplierOrderStock(SupplierOrderStockDoc $stockDoc, array $validatedDataArray): array
    {
        $blueprints = [];
        $boxPlans = [];
        $merchantSkus = [];
        $supplierOrder = null;

        $blueprintIskus = [];
        $boxPlanDescriptions = [];
        $supplierOrderIds = [];
        foreach ($validatedDataArray as $data) {
            $blueprintIskus[] = $data[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']];
            $boxPlanDescriptions[] = $data[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']];
            $supplierOrderIds[] = $data[self::HEADER_SUPPLIER_ORDER_STOCK['SUPPLIER_ORDER_ID']];
        }

        $blueprintIskus = array_filter(array_unique($blueprintIskus));
        $boxPlanDescriptions = array_filter(array_unique($boxPlanDescriptions));
        $supplierOrderIds = array_filter(array_unique($supplierOrderIds));

        if (count($supplierOrderIds) !== 1) {
            $stockDoc->addMessageError("Multiple SupplierOrder IDs found in the same file. Only one SupplierOrder ID is allowed per file.");
            return [$supplierOrder, $blueprints, $boxPlans, $merchantSkus];
        }

        /** @var BlueprintRepository $blueprintRepo */
        $blueprintRepo = $this->getRepository(Blueprint::class);
        /** @var BoxPlanRepository $boxPlanRepo */
        $boxPlanRepo = $this->getRepository(BoxPlan::class);
        /** @var MerchantSkuRepository $msRepo */
        $msRepo = $this->getRepository(MerchantSku::class);
        /** @var SupplierOrderRepository $soRepo */
        $soRepo = $this->getRepository(SupplierOrder::class);
        /** @var SupplierBlueprintRepository $priceRepo */
        $priceRepo = $this->getRepository(SupplierBlueprint::class);

        $blueprints = $blueprintRepo->retrieveByIskus($blueprintIskus);
        $boxPlans = $boxPlanRepo->retrieveByDescriptions($boxPlanDescriptions);
        $orders = $soRepo->retrieveByIds($supplierOrderIds);
        if (count($blueprints) !== count($blueprintIskus)) {
            $stockDoc->addMessageError("One or more Blueprint ISKUs not found in database.");
        }
        if (count($boxPlans) !== count($boxPlanDescriptions)) {
            $stockDoc->addMessageError("One or more BoxPlan descriptions not found in database.");
        }
        if (count($orders) !== 1) {
            $stockDoc->addMessageError("SupplierOrder not found in database.");
        }
        $supplierOrder = reset($orders);

        // check there is a SupplierPriceList for every Blueprint on the order:
        $priceList = $priceRepo->retrieveBySupplierOrderAndBlueprints($supplierOrder, $blueprints);
        if (count($priceList) !== count($blueprints)) {
            foreach ($blueprints as $bp) {
                if (empty($priceList[$bp->getId()])) {
                    $stockDoc->addMessageError("No SupplierPriceList found for Blueprint ISKU '{$bp->getIsku()}'");
                }
            }
            $stockDoc->addMessageError("Mismatch between SupplierPriceList entries and Blueprints");
        }

        foreach ($validatedDataArray as $data) {
            $blueprint = $blueprints[$data[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']]];
            $country = $data[self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']];
            $mskuRequested = $data[self::HEADER_SUPPLIER_ORDER_STOCK['MSKU']];

            if (!empty($country)) {
                $merchantSkus = $msRepo->retrieveSkusMatchingBlueprintAndCountry($blueprint->getId(), $country);
            }
            if (!empty($mskuRequested)) {
                $foundMatch = false;
                foreach ($merchantSkus as $ms) {
                    if ($ms->getSku() === $mskuRequested) {
                        if (false !== $foundMatch) {
                            $stockDoc->addMessageError("More than one match found for Merchant SKU '$mskuRequested' with ISKU '{$blueprint->getIsku()}' and country '$country'");
                        }
                        $key = "$country~{$blueprint->getIsku()}";
                        $merchantSkus[$key] = $ms;
                        $foundMatch = $ms;
                    }
                }
                if (false === $foundMatch) {
                    $stockDoc->addMessageError("Merchant SKU '$mskuRequested' not found for Blueprint ISKU '{$blueprint->getIsku()}' and country '$country'");
                }
            } else {
                if (count($merchantSkus) === 1) {
                    $ms = reset($merchantSkus);
                    $key = "$country~{$blueprint->getIsku()}";
                    $merchantSkus[$key] = $ms;
                }
            }
        }

        return [$supplierOrder, $blueprints, $boxPlans, $merchantSkus];
    }

    /**
     * @param string $filePath
     * @param string $sheetName
     * @return array<int, array<string, mixed>>
     * @throws \Exception
     */
    public function getDataArrayFromXLSXFile(string $filePath, string $sheetName): array
    {
        $dataArray = [];
        try {
            $spreadsheet = IOFactory::load($filePath);
            $sheet = $spreadsheet->getSheetByName($sheetName);
            if (is_null($sheet)) {
                throw new \Exception("Sheet '$sheetName' not found in xlsx file.");
            }

            $header = [];
            $headerRow = $sheet->getRowIterator()->current();
            foreach ($headerRow->getCellIterator() as $cell) {
                $header[] = $cell->getValue();
            }

            $highestRow = $sheet->getHighestDataRow();
            for ($row = 2; $row <= $highestRow; ++$row) {
                $rowData = [];
                $currentRow = $sheet->getRowIterator($row)->current();
                foreach ($currentRow->getCellIterator() as $cell) {
                    $columnIndex = Coordinate::columnIndexFromString($cell->getColumn()) - 1;
                    $rowData[$header[$columnIndex]] = $cell->getFormattedValue();
                }
                $dataArray[] = $rowData;
            }
        } catch (ReaderException $e) {
            throw new \Exception("Error reading xlsx file: " . $e->getMessage());
        } catch (\Throwable $e) {
            throw new \Exception("An error occurred while parsing xlsx file: " . $e->getMessage());
        }

        return $dataArray;
    }

    /**
     * @param array<int, array<string, mixed>> $dataArray
     * @return array{0: list<array<string, mixed>>, 1: list<string>} [Sanitized data, errors]
     */
    protected function validateAndSanitizeSupplierPriceListData(array $dataArray): array
    {
        $sanitizedData = [];
        $errors = [];

        foreach ($dataArray as $i => $dataRow) {
            $rowNum = $i + 2; // Accounting for header row and 1-based indexing
            $rowData = [];
            $rowErrors = [];

            if (empty($dataRow[self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID']])) {
                $rowErrors[] = "Missing 'SupplierID' in row $rowNum.";
            }

            if (empty($dataRow[self::HEADER_SUPPLIER_PRICE_LIST['ISKU']])) {
                $rowErrors[] = "Missing 'Blueprint ISKU' in row $rowNum.";
            }

            if (empty($dataRow[self::HEADER_SUPPLIER_PRICE_LIST['QTY_1']])) {
                $rowErrors[] = "Missing 'qty1' in row $rowNum.";
            }
            if (empty($dataRow[self::HEADER_SUPPLIER_PRICE_LIST['PRICE_1']])) {
                $rowErrors[] = "Missing 'price1' in row $rowNum.";
            }

            foreach ($dataRow as $key => $value) {
                $result = $this->sanitizeAndValidateSupplierPriceListColumnValue($key, $value);
                if ($result['error']) {
                    $rowErrors[] = $result['error'] . " in row $rowNum.";
                } else {
                    $rowData[$key] = $result['value'];
                }
            }

            $newRowErrors = $this->validateEntireSupplierPriceListRow($rowData);
            if (!empty($newRowErrors)) {
                foreach ($newRowErrors as $newRowError) {
                    $rowErrors[] = $newRowError . " in row $rowNum.";
                }
            }

            if (empty($rowErrors)) {
                $sanitizedData[] = $rowData;
            } else {
                $errors = array_merge($errors, $rowErrors);
            }
        }

        return [$sanitizedData, $errors];
    }

    /**
     * @param string $header
     * @param ?string $value
     * @return array{value:mixed, error: ?string}
     */
    private function sanitizeAndValidateSupplierPriceListColumnValue(string $header, ?string $value): array
    {
        if (!is_null($value)) {
            $value = trim($value);
        }

        $requiredFields = [
            self::HEADER_SUPPLIER_PRICE_LIST['SUPPLIER_ID'],
            self::HEADER_SUPPLIER_PRICE_LIST['ISKU'],
            // self::HEADER_SUPPLIER_PRICE_LIST['PRODUCT_CODE'],
            // self::HEADER_SUPPLIER_PRICE_LIST['LEAD_TIME'],
            self::HEADER_SUPPLIER_PRICE_LIST['CURRENCY'],
            self::HEADER_SUPPLIER_PRICE_LIST['QTY_1'],
            self::HEADER_SUPPLIER_PRICE_LIST['PRICE_1'],
        ];
        if (in_array($header, $requiredFields) && empty($value)) {
            return ['value' => null, 'error' => "'$header' is required but missing"];
        }

        switch ($header) {
            case self::HEADER_SUPPLIER_PRICE_LIST['ISKU']:
                $value = strtoupper($value);
                // Allow letters, numbers, and dashes only
                if (!preg_match(Blueprint::ISKU_PATTERN, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'"];
                }
                return ['value' => $value, 'error' => null];

            case self::HEADER_SUPPLIER_PRICE_LIST['LEAD_TIME']:
                // Validate as integer
                if (empty($value)) {
                    $value = null;
                } elseif (!ctype_digit($value)) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Must be a whole number"];
                }
                return ['value' => $value, 'error' => null];

            case self::HEADER_SUPPLIER_PRICE_LIST['CURRENCY']:
                $value = strtoupper($value);
                if ($value === 'RMB') { // special case for RMB
                    return ['value' => Currency::CNY, 'error' => null];
                } else if (!preg_match(self::CURRENCY_REGEX, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Must be a three-letter currency code"];
                }
                return ['value' => $value, 'error' => null];

            /** @noinspection PhpMissingBreakStatementInspection - QTY_1 is intended to cascade to QTY_X after special check that it's not blank */
            case self::HEADER_SUPPLIER_PRICE_LIST['QTY_1']:
                if (empty($value)) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Qty1 is required"];
                }
            case self::HEADER_SUPPLIER_PRICE_LIST['QTY_2']:
            case self::HEADER_SUPPLIER_PRICE_LIST['QTY_3']:
            case self::HEADER_SUPPLIER_PRICE_LIST['QTY_4']:
            case self::HEADER_SUPPLIER_PRICE_LIST['QTY_5']:
                if (empty($value)) {
                    return ['value' => $value, 'error' => null];
                }
                if (!ctype_digit($value) || str_contains($value, '.')) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be a whole number"];
                }
                return ['value' => $value, 'error' => null];

            /** @noinspection PhpMissingBreakStatementInspection - PRICE_1 is intended to cascade to PRICE_X after special check that it's not blank */
            case self::HEADER_SUPPLIER_PRICE_LIST['PRICE_1']:
                if (empty($value)) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Price1 is required"];
                }
            case self::HEADER_SUPPLIER_PRICE_LIST['PRICE_2']:
            case self::HEADER_SUPPLIER_PRICE_LIST['PRICE_3']:
            case self::HEADER_SUPPLIER_PRICE_LIST['PRICE_4']:
            case self::HEADER_SUPPLIER_PRICE_LIST['PRICE_5']:
                if (empty($value)) {
                    return ['value' => $value, 'error' => null];
                }
                if (!preg_match(self::PRICE_REGEX, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be a numeric value"];
                }
                return ['value' => $value, 'error' => null];

            case self::HEADER_SUPPLIER_PRICE_LIST['VALID_FROM']:
            case self::HEADER_SUPPLIER_PRICE_LIST['VALID_TO']:
                if (empty($value)) {
                    return ['value' => '', 'error' => null];
                }
                $parsedDate = \DateTime::createFromFormat('Y-m-d', $value);
                if (!$parsedDate || $parsedDate->format('Y-m-d') !== $value) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be in Y-m-d format"];
                }
                return ['value' => $value, 'error' => null];

            default:
                return ['value' => $value, 'error' => null];
        }
    }

    /**
     * @param array<string, mixed> $rowData
     * @return list<string>
     */
    protected function validateEntireSupplierPriceListRow(array $rowData): array
    {
        $qtyKeyRoot = 'QTY_';
        $priceKeyRoot = 'PRICE_';
        $errors = [];

        $qtyKey = $qtyKeyRoot . '1';
        $priceKey = $priceKeyRoot . '1';
        if (empty($rowData[self::HEADER_SUPPLIER_PRICE_LIST[$qtyKey]])) {
            $errors[] = 'Missing qty1';
        }
        if (empty($rowData[self::HEADER_SUPPLIER_PRICE_LIST[$priceKey]])) {
            $errors[] = 'Missing price1';
        }
        if (!empty($errors)) {
            return $errors;
        }

        $qty1 = $rowData[self::HEADER_SUPPLIER_PRICE_LIST[$qtyKey]];
        $price1 = $rowData[self::HEADER_SUPPLIER_PRICE_LIST[$priceKey]];
        $lastSeenIndex = 1;

        for ($i = 2; $i <= 5; $i++) {
            $qtyKey = $qtyKeyRoot . $i;
            $priceKey = $priceKeyRoot . $i;
            $loopQty = $rowData[self::HEADER_SUPPLIER_PRICE_LIST[$qtyKey]] ?? null;
            $loopPrice = $rowData[self::HEADER_SUPPLIER_PRICE_LIST[$priceKey]] ?? null;

            if (empty($loopQty) && empty($loopPrice)) {
                continue;
            }
            if (!empty($loopQty) && !empty($loopPrice)) {
                $expectedPreviousIndex = $i - 1;
                if ($lastSeenIndex < $expectedPreviousIndex) {
                    $errors[] = "Price break #$lastSeenIndex and #$i provided, but #$expectedPreviousIndex is missing";
                }
                if ($loopQty <= $qty1) {
                    $errors[] = "Qty break $i must be greater than qty break $lastSeenIndex";
                }
                if ($loopPrice >= $price1) {
                    $errors[] = "Price break $i ($loopPrice) must be less than price break $lastSeenIndex ($price1)";
                }
            } else {
                $errors[] = "Need to set price AND qty for price break $i";
            }
            ++$lastSeenIndex;
        }
        return $errors;
    }

    /**
     * @param Supplier $supplier
     * @param Blueprint $blueprint
     * @param array<string, mixed> $data
     * @return SupplierBlueprint
     * @throws \Exception
     */
    private function createSupplierBlueprint(Supplier $supplier, Blueprint $blueprint, array $data): SupplierBlueprint {
        $supplierBlueprint = new SupplierBlueprint();
        $supplierBlueprint->setSupplier($supplier);
        $supplierBlueprint->setBlueprint($blueprint);
        $supplierBlueprint->setProductCode($data[self::HEADER_SUPPLIER_PRICE_LIST['PRODUCT_CODE']]);
        $supplierBlueprint->setLeadTimeDays($data[self::HEADER_SUPPLIER_PRICE_LIST['LEAD_TIME']]);
        $supplierBlueprint->setCurrency($data[self::HEADER_SUPPLIER_PRICE_LIST['CURRENCY']]);
        $supplierBlueprint->setBreakpointNotes($data[self::HEADER_SUPPLIER_PRICE_LIST['QTY_NOTES']]);

        if (!empty($data[self::HEADER_SUPPLIER_PRICE_LIST['VALID_FROM']])) {
            $supplierBlueprint->setValidFrom(new \DateTime($data[self::HEADER_SUPPLIER_PRICE_LIST['VALID_FROM']]));
        } else {
            $supplierBlueprint->setValidFrom(new \DateTime(self::DEFAULT_SUPPLIER_PRICE_LIST_VALID_FROM));
        }

        if (!empty($data[self::HEADER_SUPPLIER_PRICE_LIST['VALID_TO']])) {
            $supplierBlueprint->setValidTo(new \DateTime($data[self::HEADER_SUPPLIER_PRICE_LIST['VALID_TO']]));
        } else {
            $supplierBlueprint->setValidTo(new \DateTime(self::DEFAULT_SUPPLIER_PRICE_LIST_VALID_TO));
        }

        $supplierBlueprint->setNotes($data[self::HEADER_SUPPLIER_PRICE_LIST['NOTES']]);
        $amountsAtBreaks = $data[self::HEADER_SUPPLIER_PRICE_LIST['QTY_1']] . ': ' . $data[self::HEADER_SUPPLIER_PRICE_LIST['PRICE_1']];
        for ($i = 2; $i <= 5; $i++) {
            $qtyKey = self::HEADER_SUPPLIER_PRICE_LIST['QTY_' . $i];
            $priceKey = self::HEADER_SUPPLIER_PRICE_LIST['PRICE_' . $i];
            if (!empty($data[$qtyKey]) && !empty($data[$priceKey])) {
                $amountsAtBreaks .= "\n" . $data[$qtyKey] . ': ' . $data[$priceKey];
            } else {
                break;
            }
        }
        $supplierBlueprint->setAmountsAtBreaks($amountsAtBreaks);

        return $supplierBlueprint;
    }

    /**
     * @param SupplierBlueprint $existingSb
     * @param SupplierBlueprint $newSb
     * @return bool - true if $newSb should be kept, false if it can be dropped
     */
    public function adjustExistingSbBasedOnNewTrustedSb(SupplierBlueprint $existingSb, SupplierBlueprint $newSb): bool
    {
        if ($existingSb->getValidFrom() > $newSb->getValidFrom() && $existingSb->getValidTo() < $newSb->getValidTo()) {
            $this->remove($existingSb);
            return true;
        }

        if ($existingSb->getValidTo() < $newSb->getValidFrom() || $existingSb->getValidFrom() > $newSb->getValidTo()) {
            return true;
        }

        $significantChange = false;
        $field = [];
        if ($existingSb->getProductCode() != $newSb->getProductCode()) {
            if (empty($existingSb->getProductCode())) {
                $existingSb->setProductCode($newSb->getProductCode());
            } else {
                $significantChange = true;
                $field[] = "productCode";
            }
        }
        if ($existingSb->getLeadTimeDays() != $newSb->getLeadTimeDays()) {
            $significantChange = true;
            $field[] = "leadTimeDays";
        }
        if ($existingSb->getCurrency() != $newSb->getCurrency()) {
            $significantChange = true;
            $field[] = "currency";
        }
        if ($existingSb->getAmountsAtBreaks() != $newSb->getAmountsAtBreaks()) {
            $significantChange = true;
            $field[] = "amountsAtBreaks";
        }

        if ($existingSb->getValidFrom() == $newSb->getValidFrom() && $existingSb->getValidTo() == $newSb->getValidTo()) {
            if ($significantChange) {
                $fields = implode(',', $field);
                throw new \RuntimeException("You attempted to change a significant field ($fields) of an existing Pricelist for a given date range. This is not allowed via batch action, please edit it in the admin instead.");
            }
        }

        if ($newSb->getValidTo() > $existingSb->getValidTo() && $newSb->getValidFrom() >= $existingSb->getValidFrom()) {
            $existingSb->setValidTo(GravitiqTools::getDate1DayBefore($newSb->getValidFrom()));
            return true;
        }

        if ($existingSb->getValidFrom() >= $newSb->getValidFrom() && $existingSb->getValidTo() > $newSb->getValidTo()) {
            $existingSb->setValidFrom(GravitiqTools::getDate1DayAfter($newSb->getValidTo()));
            return true;
        }

        if ($significantChange) {
            $existingSb->setValidTo(GravitiqTools::getDate1DayBefore($newSb->getValidFrom()));
            return true;
        } else {
            $existingSb->updateTertiaryFieldsFromEntity($newSb);
            return false;
        }
    }

    /**
     * @param array<int, array<string, mixed>> $dataArray
     * @return array{0: list<array<string, mixed>>, 1: list<string>} [Sanitized data, errors]
     */
    protected function validateAndSanitizeSupplierOrderStockData(array $dataArray): array
    {
        $sanitizedData = [];
        $errors = [];

        foreach ($dataArray as $i => $dataRow) {
            $rowNum = $i + 2; // Accounting for header row and 1-based indexing
            $rowData = [];
            $rowErrors = [];

            foreach ($dataRow as $key => $value) {
                $result = $this->sanitizeAndValidateSupplierOrderStockColumnValue($key, $value);
                if ($result['error']) {
                    $rowErrors[] = $result['error'] . " in row $rowNum.";
                } else {
                    $rowData[$key] = $result['value'];
                }
            }

            if (empty($rowErrors)) {
                $sanitizedData[] = $rowData;
            } else {
                $errors = array_merge($errors, $rowErrors);
            }
        }

        return [$sanitizedData, $errors];
    }

    /**
     * @param string $header
     * @param ?string $value
     * @return array{value:mixed, error: ?string}
     */
    private function sanitizeAndValidateSupplierOrderStockColumnValue(string $header, ?string $value): array
    {
        if (!is_null($value)) {
            $value = trim($value);
        }

        $requiredFields = [
            self::HEADER_SUPPLIER_ORDER_STOCK['SUPPLIER_ORDER_ID'],
            self::HEADER_SUPPLIER_ORDER_STOCK['ISKU'],
            self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED'],
            self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST'],
        ];

        if (in_array($header, $requiredFields) && empty($value)) {
            return ['value' => null, 'error' => "'$header' is required but missing"];
        }

        switch ($header) {
            case self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']:
                $value = strtoupper($value);
                // Allow letters, numbers, and dashes only
                if (!preg_match(Blueprint::ISKU_PATTERN, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'"];
                }
                break;

            case self::HEADER_SUPPLIER_ORDER_STOCK['SUPPLIER_ORDER_ID']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_DELIVERED']:
                if (empty($value)) {
                    $value = 0;
                    break;
                }
                if (!ctype_digit($value) || !is_int(1*$value)) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Must be a whole number"];
                }
                break;

            case self::HEADER_SUPPLIER_ORDER_STOCK['PREP_COST_PER_UNIT']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST']:
                if (empty($value)) {
                    $value = 0;
                    break;
                }
                if (!preg_match(self::PRICE_REGEX, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be a numeric value"];
                }
                break;

            case self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED']:
                if (empty($value)) {
                    $value = null;
                    break;
                }
                $value = strtoupper($value);
                if ('N' !== $value && 'Y' !== $value) {
                    return ['value' => null, 'error' => "Invalid '$header' value: $value. Must be 'Y' or 'N'"];
                }
                break;

            case self::HEADER_SUPPLIER_ORDER_STOCK['PROD_START_DATE']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['EXPECTED_GOODS_READY_DATE']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_FIRST_GOODS_READY_DATE']:
            case self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_GOODS_READY_DATE']:
                if (empty($value)) {
                    $value = null;
                    break;
                }

                $parsedDate = \DateTime::createFromFormat('Y-m-d', $value);
                if (!$parsedDate || $parsedDate->format('Y-m-d') !== $value) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be in Y-m-d format"];
                }
                $value = $parsedDate;
                break;

            case self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']:
                if (empty($value)) {
                    $value = null;
                    break;
                }
                $value = strtoupper($value);
                if (!preg_match(self::COUNTRY_REGEX, $value)) {
                    return ['value' => null, 'error' => "Invalid '$header' format for '$value'. Must be a 2 character country code."];
                }
                break;
        }
        return ['value' => $value, 'error' => null];
    }

    /**
     * @param SupplierOrder $supplierOrder
     * @param Blueprint $blueprint
     * @param array<string, mixed> $data
     * @param ?BoxPlan $boxPlan
     * @param ?MerchantSku $msku
     * @return Stock
     * @throws \Exception
     */
    private function createSupplierOrderStock(SupplierOrder $supplierOrder, Blueprint $blueprint, array $data, ?BoxPlan $boxPlan=null, ?MerchantSku $msku=null): Stock
    {
        $stock = new Stock();
        $stock
            ->setSupplierOrder($supplierOrder)
            ->setBlueprint($blueprint)
            ->setBoxPlan($boxPlan)
            ->setLabelledAsMsku($msku)
            ->setUnitsOrdered($data[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED']])
            ->setUnitsDelivered($data[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_DELIVERED']] ?? 0)
            ->setProductionStartDate($data[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_START_DATE']] ?? null)
            ->setExpectedGoodsReadyDate($data[self::HEADER_SUPPLIER_ORDER_STOCK['EXPECTED_GOODS_READY_DATE']] ?? null)
            ->setActualFirstGoodsReadyDate($data[self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_FIRST_GOODS_READY_DATE']] ?? null)
            ->setActualGoodsReadyDate($data[self::HEADER_SUPPLIER_ORDER_STOCK['ACTUAL_GOODS_READY_DATE']] ?? null)
            ->setPrepNotes($data[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_NOTES']] ?? null)
            ->setPlannedDestinationCountry($data[self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']] ?? null)
        ;
        $stock->setProductionCostAmountFromDecimal($data[self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST']]);
        $stock->setPrepCostAmountFromDecimal($data[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_COST_PER_UNIT']] ?? 0);

        if (!empty($data[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED']]) && 'Y' === strtoupper($data[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED']])) {
            $stock->setProductionCostConfirmed(true);
        } else {
            $stock->setProductionCostConfirmed(false);
        }

        return $stock;
    }

    public function processAutoGeneratePoNumber(SupplierOrder $order): void
    {
        if ($order->isEligibleToGeneratePoNumber()) {
            $newPoNumber = $this->generatePoNumber($order);
            $order->setPoNumber($newPoNumber);
        }
    }

    private function generatePoNumber(SupplierOrder $order): string
    {
        /** @var SupplierOrderRepository $soRepo */
        $soRepo = $this->getRepository(SupplierOrder::class);

        $brandCode = $order->getItems()->first()->getBrandCode();
        $yearMonth = $order->getOrderDate()->format('ym');
        $poNumberPrefix = $brandCode . $yearMonth;

        $existingOrders = $soRepo->retrieveByPoNumberPrefix($poNumberPrefix);

        $maxSequence = 0;
        foreach ($existingOrders as $existingOrder) {
            $numericSequence = $this->getNumericPartFromPoNumber($existingOrder->getPoNumber(), $poNumberPrefix);
            if ($numericSequence > $maxSequence) {
                $maxSequence = $numericSequence;
            }
        }

        $newSequence = str_pad((string)($maxSequence + 1), 2, '0', STR_PAD_LEFT);

        return $poNumberPrefix . $newSequence;
    }

    private function getNumericPartFromPoNumber(string $poNumber, string $poNumberPrefix): int
    {
        $poWithoutPrefix = substr($poNumber, strlen($poNumberPrefix));
        preg_match('/^\d+/', $poWithoutPrefix, $matches);

        return isset($matches[0]) ? (int)$matches[0] : 0;
    }

    /**
     * @param string[] $headers
     */
    protected function checkAllRequiredHeadersIfPresent(array $headers): void
    {
        $missingHeaders = [];
        $requiredHeaders = array_diff(
            self::HEADER_SUPPLIER_ORDER_STOCK,
            self::ADMIN_NOT_REQUIRED_IMPORT_HEADERS
        );

        foreach ($requiredHeaders as $requiredHeader) {
            if (!in_array($requiredHeader, $headers, true)) {
                $missingHeaders[] = "Header '$requiredHeader' is missing";
            }
        }

        if (!empty($missingHeaders)) {
            throw new \RuntimeException(implode(" <br>", $missingHeaders));
        }
    }

    /**
     * @param Spreadsheet $spreadsheet
     * @return string[]
     * @throws Exception
     */
    protected function getHeaderOfImportedStockFromAdmin(Spreadsheet $spreadsheet): array
    {
        $header = [];
        $expectedHeaderRow = 1;
        if ($spreadsheet->getActiveSheet()->getHighestRow() < $expectedHeaderRow) {
            throw new \RuntimeException("Header row is empty");
        }
        $headerRow = $spreadsheet->getActiveSheet()->getRowIterator($expectedHeaderRow)->current();
        foreach ($headerRow->getCellIterator() as $cell) {
            $cellValue = $cell->getCalculatedValue();
            if (is_null($cellValue)) {
                $cellCoord = $cell->getCoordinate();
                throw new \RuntimeException("Header contains null values in $cellCoord");
            }

            $header[] = $cellValue;
        }

        $this->checkAllRequiredHeadersIfPresent($header);

        return $header;
    }

    /**
     * @param Spreadsheet $spreadsheet
     * @param string[] $header
     * @return list<array<string, string|null>>
     * @throws Exception
     */
    protected function extractImportedStockXlsxDataFromAdmin(Spreadsheet $spreadsheet, array $header): array
    {
        $data = [];
        $expectedDataRowStart = 2;
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();
        if ($highestRow < $expectedDataRowStart) {
            throw new \RuntimeException("Spreadsheet contains no data");
        }
        $rowIterator = $sheet->getRowIterator($expectedDataRowStart);
        foreach ($rowIterator as $row) {
            $rowData = [];
            $index = 0;
            foreach ($row->getCellIterator() as $cell) {
                $rowData[$header[$index]] = $cell->getCalculatedValue();
                $index++;
            }

            $data[] = $rowData;
        }

        return $data;
    }

    /**
     * @param array{array<string, mixed>} $spreadsheetRow
     * @param Supplier $supplier
     * @return array{blueprints: array<string, Blueprint>, boxPlans: array<string, BoxPlan>}|array{errors: list<string>}
     */
    protected function validateImportedStockXlsxRowForSupplier(array $spreadsheetRow, Supplier $supplier): array
    {
        $data = [];
        $errors = [];

        /** @var BlueprintRepository $blueprintRepo */
        $blueprintRepo = $this->getRepository(Blueprint::class);
        $blueprintRows = array_column($spreadsheetRow, self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']);
        $validBlueprints = $blueprintRepo->retrieveByIskus($blueprintRows);
        $blueprintsMatchingSupplier = $blueprintRepo->retrieveBlueprintsMatchingSupplierIndexedByIsku($supplier);

        foreach ($blueprintRows as $index => $blueprint) {
            if (is_null($blueprint)) {
                $errors[] = "Blueprint ISKU cannot be empty in row " . ($index + 2);
                continue;
            }

            if (!in_array($blueprint, $validBlueprints)) {
                $errors[] = "Invalid Blueprint ISKU: $blueprint in row " . ($index + 2) . " (does not exist in system)";
                continue;
            }

            if (!array_key_exists($blueprint, $blueprintsMatchingSupplier)) {
                $errors[] = "Blueprint ISKU '$blueprint' exists (row " . ($index + 2) . ") but is not valid. No price listed for this Supplier (need to add prices before adding Stock to an order).";
            }
        }

        if (!empty($errors)) {
            $data['errors'] = $errors;
            return $data;
        }

        $data['blueprints'] = $validBlueprints;

        $boxPlans = [];
        foreach ($spreadsheetRow as $index => $row) {
            $blueprint = $row[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']];
            $boxPlan = $row[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']];

            if (is_null($boxPlan)) {
                continue;
            }

            if (!empty($blueprint) && !empty($boxPlan)) {
                $blueprintEntity = $validBlueprints[$blueprint];
                $boxPlansForBlueprint = $blueprintEntity->getBoxPlansIndexedByDescription();
                if (!in_array($boxPlan, $boxPlansForBlueprint)) {
                    $errors[] = "Invalid Box Plan: $boxPlan for Blueprint ISKU: $blueprint in row " . ($index + 2);
                } else {
                    $boxPlans[$boxPlan] = $boxPlansForBlueprint[$boxPlan];
                }
            }
        }

        if (!empty($errors)) {
            $data['errors'] = $errors;
            return $data;
        }

        $data['boxPlans'] = $boxPlans;

        foreach ($spreadsheetRow as $index => $row) {
            $boxPlan = $row[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']];

            if (is_null($boxPlan)) {
                continue;
            } elseif (!array_key_exists($boxPlan, $boxPlans)) {
                $errors[] = "Invalid Box Plan: $boxPlan in row " . ($index + 2);
                continue;
            }

            $unitsOrdered = $row[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED']];
            $blueprintEntity = $validBlueprints[$row[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']]];
            if (0 === $unitsOrdered) {
                continue;
            }
            $boxPlanEntity = $boxPlans[$boxPlan];
            $unitsPerBox = $boxPlanEntity->getExpectedUnitsPerBoxForBlueprint($blueprintEntity);
            if ($unitsOrdered % $unitsPerBox !== 0) {
                $errors[] = "Units ordered must be a multiple of the box plan units ordered for Box Plan: $boxPlan. Units ordered: $unitsOrdered, Expected multiple: " . $unitsPerBox . " in row " . ($index + 2);
            }
        }
        $countryRows = array_column($spreadsheetRow, self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']);
        $countryChoices = Stock::getCountryChoices();
        foreach ($countryRows as $index => $country) {
            if (is_null($country)) {
                continue;
            }
            if (!in_array($country, $countryChoices)) {
                $errors[] = "Invalid country code: $country in row " . ($index + 2);
            }
        }

        if (!empty($errors)) {
            $data['errors'] = $errors;
        }

        return $data;
    }

    /**
     * @param SupplierOrder $supplierOrder
     * @param string $filePath
     * @return array{created: list<string>}
     * @throws \Exception
     */
    public function processImportedStockXlsx(SupplierOrder $supplierOrder, string $filePath): array
    {
        $created = [];
        $spreadsheet = IOFactory::load($filePath);
        $supplier = $supplierOrder->getSupplier();
        if ($spreadsheet->getActiveSheet()->getHighestRow() <= 1) {
            throw new \RuntimeException("Spreadsheet does not contain any data");
        }

        try {
            $headers = $this->getHeaderOfImportedStockFromAdmin($spreadsheet);
        } catch (Exception $e) {
            throw new \RuntimeException("There was an error reading the header row: " . $e->getMessage());
        }

        $spreadSheetRowArray = $this->extractImportedStockXlsxDataFromAdmin($spreadsheet, $headers);

        $validatedXlsxRows = $this->validateImportedStockXlsxRowForSupplier($spreadSheetRowArray, $supplier);
        if (isset($validatedXlsxRows['errors'])) {
            throw new \RuntimeException(implode(' <br>', $validatedXlsxRows['errors']));
        }

        foreach ($spreadSheetRowArray as $index => $row) {
            $stockData = [
                self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED'] =>
                    is_numeric($row[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED']])
                        ? (int) $row[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_ORDERED']]
                        : 0,

                self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_DELIVERED'] =>
                    is_numeric($row[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_DELIVERED']])
                        ? (int) $row[self::HEADER_SUPPLIER_ORDER_STOCK['UNITS_DELIVERED']]
                        : 0,

                self::HEADER_SUPPLIER_ORDER_STOCK['PREP_NOTES'] =>
                    !empty($row[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_NOTES']])
                        ? (string) $row[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_NOTES']]
                        : null,

                self::HEADER_SUPPLIER_ORDER_STOCK['PREP_COST_PER_UNIT'] =>
                    is_numeric($row[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_COST_PER_UNIT']])
                        ? (float) $row[self::HEADER_SUPPLIER_ORDER_STOCK['PREP_COST_PER_UNIT']]
                        : 0.0,

                self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST'] =>
                    is_numeric($row[self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST']])
                        ? (float) $row[self::HEADER_SUPPLIER_ORDER_STOCK['PRODUCTION_COST']]
                        : 0.0,

                self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED'] =>
                    !empty($row[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED']])
                        ? (string) $row[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_COST_CONFIRMED']]
                        : 'N',

                self::HEADER_SUPPLIER_ORDER_STOCK['PROD_START_DATE'] =>
                    isset($row[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_START_DATE']])
                        ? Date::excelToDateTimeObject((int) $row[self::HEADER_SUPPLIER_ORDER_STOCK['PROD_START_DATE']])
                        : null,

                self::HEADER_SUPPLIER_ORDER_STOCK['EXPECTED_GOODS_READY_DATE'] =>
                    isset($row[self::HEADER_SUPPLIER_ORDER_STOCK['EXPECTED_GOODS_READY_DATE']])
                        ? Date::excelToDateTimeObject((int) $row[self::HEADER_SUPPLIER_ORDER_STOCK['EXPECTED_GOODS_READY_DATE']])
                        : null,

                self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY'] =>
                    (string) ($row[self::HEADER_SUPPLIER_ORDER_STOCK['FOR_COUNTRY']] ?? ''),
            ];

            $blueprint = $validatedXlsxRows['blueprints'][$row[self::HEADER_SUPPLIER_ORDER_STOCK['ISKU']]];
            $boxPlan = (is_null($row[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']])) ? null : $validatedXlsxRows['boxPlans'][$row[self::HEADER_SUPPLIER_ORDER_STOCK['BOX_PLAN']]];

            $stock = $this->createSupplierOrderStock($supplierOrder, $blueprint, $stockData, $boxPlan);
            $supplierOrder->addItem($stock);
            $this->persist($stock);

            $created[] = "Excel Row " . ($index + 2) . " - SOItem created for Blueprint ISKU: {$blueprint->getIsku()}";
        }

        $this->flushChanges();

        return ['created' => $created];
    }

    /**
     * @param Supplier $supplier
     * @param string $templateFile
     * @return string The path to the generated Excel file.
     * @throws Exception
     */
    public function populateSupplierPriceListTemplate(Supplier $supplier, string $templateFile): string
    {
        try {
            $spreadsheet = IOFactory::load($templateFile);
        } catch (\Exception $exception) {
            throw new \RuntimeException("Error loading template file: " . $exception->getMessage());
        }

        $sheet = $spreadsheet->getActiveSheet();
        $row = 2;

        foreach ($supplier->getSupplierBlueprints() as $pricelist) {
            $sheet->setCellValue("A{$row}", $pricelist->getSupplier()->getId());
            $sheet->setCellValue("B{$row}", $pricelist->getBlueprint()->getIsku());
            $sheet->setCellValue("C{$row}", $pricelist->getProductCode());
            $sheet->setCellValue("D{$row}", $pricelist->getLeadTimeDays());
            $sheet->setCellValue("E{$row}", $pricelist->getCurrency());
            $sheet->setCellValue("F{$row}", $pricelist->getBreakpointNotes());
            $sheet->setCellValue("Q{$row}", $pricelist->getValidFrom()->format('Y-m-d'));
            $sheet->setCellValue("R{$row}", $pricelist->getValidTo()->format('Y-m-d'));
            $sheet->setCellValue("S{$row}", $pricelist->getNotes());

            $amountAtBreaks = $pricelist->getAmountsAtBreaks() ? explode("\n", $pricelist->getAmountsAtBreaks()) : [];
            if (count($amountAtBreaks) > 0) {
                $colIndex = Coordinate::columnIndexFromString('G');
                foreach ($amountAtBreaks as $amountAtBreak) {
                    if (str_contains($amountAtBreak, ':')) {
                        list($quantity, $price) = array_map('trim', explode(':', $amountAtBreak, 2));
                        $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                        $sheet->setCellValue("{$colLetter}{$row}", $quantity);
                        $colLetter = Coordinate::stringFromColumnIndex($colIndex + 1);
                        $sheet->setCellValue("{$colLetter}{$row}", $price);
                        $colIndex += 2;
                    }
                }
            }
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $tempFile = GravitiqTools::getFilePath(tmpfile(), 'populateSupplierPriceListTemplate');
        $writer->save($tempFile);

        return $tempFile;
    }
}