<?php

namespace App\Service\Crawler;

use App\Entity\AccountGeneric;
use App\Entity\AccountProfileSpa;
use App\Entity\Crawler\ApiRequestCrawlAsc;
use App\Entity\Crawler\AscNews;
use App\Entity\Crawler\DataCrawlAscDeal;
use App\Entity\Crawler\DataCrawlAscPoeNodeNiche;
use App\Entity\Spa\DataSpaBrowseTreeNode;
use App\Entity\SpaListing\DataSpaListListing;
use App\Repository\AccountProfileSpaRepository;
use App\Repository\Crawler\ApiRequestCrawlAscRepository;
use App\Repository\Crawler\AscNewsRepository;
use App\Repository\Crawler\DataCrawlAscDealRepository;
use App\Repository\Crawler\DataCrawlAscPoeNodeNicheRepository;
use App\Repository\Spa\DataSpaBrowseTreeNodeRepository;
use App\Repository\SpaListing\DataSpaListListingRepository;
use App\Service\AmazonSpaBridge;
use App\Service\BaseApiBridge;
use App\Service\Captcha\TwoCaptchaManager;
use App\Service\FileSystemSwitchboard;
use App\Service\Messaging\SlackMessenger;
use App\Service\Trait\FileSystemSwitchboardTrait;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\Mapping\MappingException;
use Facebook\WebDriver\Exception\WebDriverException;
use GuzzleHttp\Exception\GuzzleException;
use League\Flysystem\FilesystemException;
use OTPHP\TOTP;
use Psr\Log\LoggerInterface;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

/**
 * @phpstan-type AscNewsResponseArrayShape = array{
 *     badgeDate: null,
 *     categories: list<string>,
 *     content: null|string,
 *     forumsId: null,
 *     legacyId: null,
 *     newForumsId: string,
 *     publicId: string,
 *     revisionReason: null,
 *     startDate: int,
 *     title: string,
 * }
 */
class AmazonSellerCentralCrawler extends BaseCrawler
{
    use FileSystemSwitchboardTrait;

    protected const string URL_HOMEPAGE = 'https://%ROOT_URL%/';
    protected const string URL_ADVERTISING_CENTRAL_HOMEPAGE = 'https://%ROOT_URL%/cm/ref=xx_cmpmgr_dnav_xx';
    protected const string URL_ACCOUNT_SWITCHER = 'https://%ROOT_URL%/account-switcher/default/merchantMarketplace';
    protected const string URL_DASHBOARD = 'https://%ROOT_URL%/home';
    protected const string URL_TRANSACTION_DATE_RANGE_REPORTS = 'https://%ROOT_URL%/payments/reports/custom/request?ref_=xx_report_ttab_dash&tbla_daterangereportstable=sort:%7B%22sortOrder%22%3A%22DESCENDING%22%7D;search:undefined;pagination:1;';
    protected const string URL_GLOBAL_PICKER = 'https://%ROOT_URL%/global-picker';
    protected const string URL_ADVERTISING_CENTRAL = 'https://%ROOT_URL%/cm/ref=xx_cmpmgr_dnav_xx';
    protected const string URL_BRAND_ANALYTICS_SQ_PERF_ASIN_START = 'https://%ROOT_URL%/brand-analytics/dashboard/query-performance?view-id=query-performance-asin-view&country-id=%country_code%';
    protected const string URL_BRAND_ANALYTICS_SQ_PERF_BRAND_RESULTS_WEEKLY = 'https://%ROOT_URL%/brand-analytics/dashboard/query-performance?view-id=query-performance-brands-view&brand=%brand_id%&reporting-range=weekly&weekly-week=%report_end_date%&country-id=%country_code%';
    protected const string URL_BRAND_ANALYTICS_SQ_PERF_ASIN_RESULTS_WEEKLY = 'https://%ROOT_URL%/brand-analytics/dashboard/query-performance?view-id=query-performance-asin-view&asin=%asin%&reporting-range=weekly&weekly-week=%report_end_date%&country-id=%country_code%';
    protected const string URL_BRAND_ANALYTICS_SQ_PERF_ASIN_RESULTS_DOWNLOAD = 'https://%ROOT_URL%/api/brand-analytics/v1/dashboard/query-performance/download';
    protected const string URL_BRAND_ANALYTICS_SQ_DETAILS_ASIN_RESULTS_WEEKLY = 'https://%ROOT_URL%/brand-analytics/dashboard/query-detail?view-id=query-detail-asin-view&asin=%asin%&search-term-freeform=%url_encoded_query%&reporting-range=weekly&weekly-week=%report_end_date%&country-id=%country_code%';
    protected const string URL_EXPERIMENT_DASHBOARD = 'https://%ROOT_URL%/experiments/dashboard/ref=xx_mye_dnav_xx';
    protected const string URL_EXPERIMENT_RESULTS = 'https://%ROOT_URL%/experiments/%experiment_id%/results';
    protected const string URL_DEALS_START = 'https://%ROOT_URL%/merchandising-new/?selectedStatusFilters=running%2cupcoming%2cneeds_attention%2cended%2ccancelled%2cdraft&pageNumber=%page%&pageSize=50&sortColumn=START_DATE&sortOrder=DESC';
    protected const string URL_DEAL_DETAILS = 'https://%ROOT_URL%/merchandising-new/deal/view/%campaign_id%';
    protected const string URL_POE_DASHBOARD = 'https://%ROOT_URL%/opportunity-explorer';
    protected const string URL_POE_NODE_NICHES = 'https://%ROOT_URL%/opportunity-explorer/search?browsenodes=[{"browseNodeId":"%browseNodeId%","browseNodeName":"%browseNodeName%"}]';
    protected const string URL_POE_NICHE_PRODUCTS = 'https://%ROOT_URL%/opportunity-explorer/niche/%niche_id%';
    protected const string URL_LOGOUT = 'https://%ROOT_URL%/sign-out?ref_=xx_logout_dnav_xx';
    protected const string URL_COUPONS_DASHBOARD = 'https://%ROOT_URL%/coupons/ref=xx_scoupn_dnav_xx';
    protected const string URL_COUPON_DETAILS = 'https://%ROOT_URL%/coupons/view-coupon/%coupon_id%';
    protected const string URL_RESTART_COUPON = 'https://%ROOT_URL%/coupons/create-coupon/%coupon_id%';
    protected const string URL_CREATE_COUPON = 'https://%ROOT_URL%/coupons/create-coupon';
    protected const string URL_INVENTORY_DASHBOARD = 'https://%ROOT_URL%/fba/dashboard/?ref_=xx_fbadashboard_dnav_xx';
    protected const string URL_COUPON_DASHBOARD = 'https://%ROOT_URL%/coupons/ref=xx_scoupn_dnav_xx';
    protected const string FETCH_PATH_COUPON_DETAILS = 'https://%ROOT_URL%/coupons/api/couponPromotion?promotionId=%promotion_id%';
    protected const string FETCH_PATH_COUPON_ASINS = 'https://%ROOT_URL%/coupons/api/couponPromotion/products?productSelectionId=%product_selection_id%&paginationSize=%pagination_size%';
    protected const string AJAX_PATH_SQD = '/api/brand-analytics/v1/dashboard/query-detail/reports';
    protected const string AJAX_PATH_SQP = '/api/brand-analytics/v1/dashboard/query-performance/reports';
    protected const string AJAX_PATH_SQP_METADATA = '/api/brand-analytics/v1/dashboard/query-performance/metadata';
    protected const string AJAX_PATH_EXPERIMENTS = 'https://%ROOT_URL%/api/experiments/v1/experiments';
    protected const string AJAX_PATH_EXPERIMENT_RESULTS = '/api/experiments/v1/experiments/%experiment_id%/results';
    protected const string AJAX_PATH_EXPERIMENT_DETAILS = '/api/experiments/v1/experiments/%experiment_id%';
    protected const string AJAX_PATH_INVENTORY_DASHBOARD_CAPACITY_MONITOR = '/inventory-performance/api/capacity-monitor';
    protected const string FETCH_PATH_CAPACITY_MONITOR = 'https://%ROOT_URL%/inventory-performance/api/capacity-monitor';
    protected const string FETCH_PATH_DEALS = '/merchandising/api/v4/manage/data?pageNumber=%page%&pageSize=50&campaignStatuses=ACTIVE%2cACTIVE_WITH_SUPPRESSIONS%2cAPPROVED%2cSUPPRESSED%2cENDED%2cCANCELLED%2cREJECTED%2cDRAFT%2cREWORK&merchandisingType=&sortColumn=START_DATE&sortOrder=DESC&selectedStatusFilters=running%2cupcoming%2cneeds_attention%2cended%2ccancelled%2cdraft';
    protected const string FETCH_PATH_DEAL_DETAILS = '/merchandising/api/v4/deals/get?campaignId=%campaign_id%';
    protected const string AJAX_PATH_COUPONS = 'https://%ROOT_URL%/coupons/api/getCouponPromotions?paginationSize=%pagination_size%&paginationSkip=%pagination_skip%';
    protected const string SLACK_CHANNEL_CAPTCHA = 'C067XC51FN1';
    protected const string SERVICE_UNAVAILABLE = 'Service Unavailable';
    protected const int NUM_DAYS_TO_FILL_IN_DEAL_REPORTING_DATA = 7;
    protected const int MAX_SQD_QUERY_LENGTH_TO_RETRY = 250;
    protected const string URL_PAYMENT_TRANSACTIONS_DASHBOARD = 'https://%ROOT_URL%/payments/dashboard/index.html/?ref=xx_payments_dnav_xx';
    protected const string URL_PAYMENT_DEFERRED_TRANSACTIONS = 'https://%ROOT_URL%/payments/event/view?accountType=ALL&startDate=%start_date%&endDate=%end_date%&transactionstatus=DEFERRED&category=DEFERRED&resultsPerPage=50&pageNumber=1';
    protected const string AJAX_PATH_PAYMENT_DEFERRED_TRANSACTIONS = 'https://%ROOT_URL%/payments/api/events-view?limit=%limit%&offset=%offset%&accountType=ALL&fiqlFiltersString=(startTimestamp%3D%3D%start_timestamp%)%3B(endTimestamp%3D%3D%end_timestamp%)%3B(transactionstatus%3D%3DHOLD)&sortType=DESC&category=DEFERRED';
    protected const string FETCH_PATH_ASC_NEWS = 'https://%ROOT_URL%/news/api/getarticles?&types=NEWS&headlines=true&suggestions=false&num=%num%';
    protected const string FETCH_PATH_ASC_NEWS_CONTENT = 'https://%ROOT_URL%//news/api/getarticlebyid?&id=%id%&headline=false';
    protected const string SELECTOR_LOGIN_FORM_SUBMIT_V1 = 'input#signInSubmit';
    protected const string SELECTOR_LOGIN_FORM_SUBMIT_V2 = 'input#continue';
    protected const string SELECTOR_LOGIN_FORM_SUBMIT_ALL = self::SELECTOR_LOGIN_FORM_SUBMIT_V1 . ', ' . self::SELECTOR_LOGIN_FORM_SUBMIT_V2;

    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_WRAPPER = 'div.full-page-account-switcher-accounts-wrapper ';
    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_NODE_RELATIVE_TO_PARENT = 'div.full-page-account-switcher-accounts > div.full-page-account-switcher-account';
    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_ACCOUNT_NODE = self::SELECTOR_ACCOUNT_SWITCHER_V2_WRAPPER . ' > ' . self::SELECTOR_ACCOUNT_SWITCHER_V2_NODE_RELATIVE_TO_PARENT;
    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_BUTTON_RELATIVE = 'button.full-page-account-switcher-account-details';
    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_LABEL_RELATIVE = 'span.full-page-account-switcher-account-label';
    protected const string SELECTOR_ACCOUNT_SWITCHER_V2_SELECTED_COUNTRY = '.full-page-account-switcher-account-selected-icon';
    protected const string CAPTCHA_TYPE_SIGN_IN = 'sign_in';
    protected const string CAPTCHA_TYPE_CVF = 'cvf';
    protected const string CAPTCHA_TYPE_HARD_HIT = 'hard_hit';
    protected string $baseUrl = '';
    protected string $currentCountry;
    protected string $currentRegion;
    protected string $googleDrivePath;
    /** @var array{email?: string, password?: string, otpSeed?: string} $credentials */
    protected array $credentials = [];
    protected ?string $datePeriod = null;
    protected ?int $nestedCallDepth = 0;
    protected ?int $nestedPaginationDepth = 0;
    protected bool $loggedIn = false;
    protected string $loggedInUsername = '';
    private ?\JoliCode\Slack\Api\Model\ObjsMessage $lastSlackMessage = null;

    /** @var array<string, true> $cachedListOfAlreadyRequestedSqpAsinAndDate */
    protected array $cachedListOfAlreadyRequestedSqpAsinAndDate = [];

    /**
     * @param SlackMessenger $slackMessenger
     * @param ManagerRegistry $doctrine
     * @param LoggerInterface $logger
     * @param array{clientType: ?string, mitmProfile: ?string, memoryLimitGb: int, timeBetweenMemoryChecks: int} $crawlerSettings
     * @param string $googleDriveAsc
     * @param array{email: string, password: string, otpSeed: string} $ascCredentials
     * @param FileSystemSwitchboard $switchboard
     * @param string $fileSystemAmazonCrawler
     * @param string $fileSystemUrlAccessible
     * @param TwoCaptchaManager $twoCaptchaManager
     * @param AmazonScNewsManager $amazonScNewsManager
     */
    public function __construct(
        protected SlackMessenger      $slackMessenger,
        ManagerRegistry               $doctrine,
        LoggerInterface               $logger,
        array                         $crawlerSettings,
        string                        $googleDriveAsc,
        array                         $ascCredentials,
        FileSystemSwitchboard         $switchboard,
        string                        $fileSystemAmazonCrawler,
        protected string              $fileSystemUrlAccessible,
        protected TwoCaptchaManager   $twoCaptchaManager,
        protected AmazonScNewsManager $amazonScNewsManager,
    )
    {
        $this->credentials = $ascCredentials;
        $this->credentials['otpSeed'] = str_replace(' ', '', $this->credentials['otpSeed']);
        $this->googleDrivePath = $googleDriveAsc;
        $this->setFileSystemSwitchboard($switchboard, $fileSystemAmazonCrawler);

        parent::__construct($doctrine, $logger, $crawlerSettings);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->twoCaptchaManager->setLogger($logger);
        $this->amazonScNewsManager->setLogger($logger);
        parent::setLogger($logger);
    }

    protected function convertCountryCodeToFullText(string $countryCode): string
    {
        $lookup = AmazonSpaBridge::getAmazonCountriesIndexedByCode();
        if (!isset($lookup[$countryCode])) {
            throw new \InvalidArgumentException("Unknown country code ($countryCode) provided in convertCountryCodeToFullText()");
        }
        return $lookup[$countryCode];
    }

    protected function convertRegionToRootUrl(string $region = null, bool $isAdvertising = false): string
    {
        $region = $region ?: $this->currentRegion;
        if ($isAdvertising) {
            $lookup = [
                'EUR' => 'advertising-europe.amazon.com',
                'NAM' => 'advertising.amazon.com',
                'FEA' => 'advertising.amazon.com.au',
            ];
        } else {
            if ('UK' === $this->currentCountry) {
                return 'sellercentral.amazon.co.uk';
            }
            $lookup = [
                'EUR' => 'sellercentral-europe.amazon.com',
                'NAM' => 'sellercentral.amazon.com',
                'FEA' => 'sellercentral.amazon.com.au',
            ];
        }
        if (empty($lookup[$region])) {
            throw new \InvalidArgumentException("Cannot find root URL for region $region");
        }
        return $lookup[$region];
    }

    protected function localiseUrl(string $urlWithPlaceholder, string $region = null, bool $isAdvertising = false): string
    {
        if (str_starts_with($urlWithPlaceholder, '/')) {
            $urlWithPlaceholder = "https://%ROOT_URL%{$urlWithPlaceholder}";
        }
        if (!empty($this->baseUrl)) {
            return str_replace('https://%ROOT_URL%', $this->baseUrl, $urlWithPlaceholder);
        }
        return str_replace('%ROOT_URL%', $this->convertRegionToRootUrl($region, $isAdvertising), $urlWithPlaceholder);
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @param string|null $countryCodeRequested
     * @param bool $ignorePreLaunchAsins
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfSearchQueryPerformanceReports(array $profiles, \DateTimeInterface $startDate, \DateTimeInterface $endDate, ?string $countryCodeRequested = null, bool $ignorePreLaunchAsins = false): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_WEEKLY;

        // Request missing reports
        foreach ($profiles as $profile) {
            $countryCode = $countryCodeRequested ?? $profile->getCountryCode();
            $this->logger->info("Doing BA-SQP fetch for {$profile->getAlias()} in $countryCode");

            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->scrapeSqpBrandDataFromBrandAnalytics($profile, $countryCode, $startDate, $endDate);
                $this->rebootClientIfMemoryUsageIsHigh($profile);
                $this->scrapeSqpAsinDataFromBrandAnalytics($profile, $countryCode, $startDate, $endDate, $ignorePreLaunchAsins);
                $this->rebootClientIfMemoryUsageIsHigh($profile);
                $this->flushChanges();
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @param string|null $countryCodeRequested
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfSearchQueryDetailReports(array $profiles, \DateTimeInterface $startDate, \DateTimeInterface $endDate, ?string $countryCodeRequested = null): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_WEEKLY;

        foreach ($profiles as $profile) {
            $profileAlias = $profile->getAlias();
            $countryCode = $countryCodeRequested ?? $profile->getCountryCode();
            $timedOutSqdRequests = $this->retrieveTimedOutSqdRequests($profile, $countryCode, $startDate, $endDate);
            if (empty($timedOutSqdRequests)) {
                $this->logger->info("No timed out SQD requests found for $profileAlias in $countryCode");
                continue;
            }

            $this->logger->info("Doing BA-SQD fetch for $profileAlias in $countryCode");
            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->retryTimedOutSqdDataFromBrandAnalytics($profile, $countryCode, $startDate, $endDate);
                $this->rebootClientIfMemoryUsageIsHigh($profile);
                $this->retryTimedOutSqdDataFromBrandAnalytics($profile, $countryCode, $startDate, $endDate);
                $this->rebootClientIfMemoryUsageIsHigh($profile);

                $this->flushChanges();
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @param ?int $maxAgeDays
     * @param bool $onlyPrevious
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfExperimentResults(array $profiles, ?int $maxAgeDays, bool $onlyPrevious = false): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_NONE;

        if ($onlyPrevious) {
            $accountProfileIds = array_map(fn(AccountProfileSpa $profile) => $profile->getId(), $profiles);
            $profilesWithExperiments = $this->getApiRequestRepository()->findProfilesWithExperimentRecords($accountProfileIds);
            $profiles = array_filter($profiles, fn(AccountProfileSpa $profile) => in_array($profile->getId(), $profilesWithExperiments));
            if (empty($profiles)) {
                $this->logger->info("No profiles with existing experiments found... exiting");
                return;
            }
        }
        foreach ($profiles as $profile) {
            $this->logger->info("Doing experiment results fetch for {$profile->getAlias()}");

            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->scrapeExperimentsDataFromBrandAnalytics($profile, $profile->getCountryCode(), $maxAgeDays);
                $this->rebootClientIfMemoryUsageIsHigh($profile);
                $this->flushChanges();
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @param ?int $minHoursBetweenRequests
     * @param bool $onlyGetNewDeals
     * @param bool $onlyRunningDeals
     * @param bool $fullRefresh
     * @param int $startPage
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfDeals(
        array $profiles,
        ?int  $minHoursBetweenRequests,
        bool  $onlyGetNewDeals = false,
        bool  $onlyRunningDeals = false,
        bool  $fullRefresh = false,
        int   $startPage = 1
    ): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_NONE;

        /** @var DataCrawlAscDealRepository $dealsRepo */
        $dealsRepo = $this->getRepository(DataCrawlAscDeal::class);

        foreach ($profiles as $profile) {
            if ($onlyRunningDeals) {
                $profileHasRunningDeal = $dealsRepo->checkIfProfileHasActiveDealSinceLastRequest($profile);
                if (!$profileHasRunningDeal) {
                    $this->logger->warning("No running deals found for {$profile->getAlias()}... skipping profile");
                    continue;
                }
            }

            $this->logger->info("Fetching deals for {$profile->getAlias()}");

            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->scrapeDealsFromAdvertising($profile, $profile->getCountryCode(), $minHoursBetweenRequests, $onlyGetNewDeals, $fullRefresh, $startPage);
                $this->rebootClientIfMemoryUsageIsHigh($profile);
                $this->flushChanges();
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @param AccountProfileSpa $profile
     * @param ?int $minHoursBetweenRequests
     * @param int|string|list<int|string> $rootNodeIds
     * @param int|string|list<int|string> $branchNodeIds
     * @param bool $ignoreNodeRequestDate
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfPoeNiches(AccountProfileSpa $profile, ?int $minHoursBetweenRequests, int|string|array $rootNodeIds, int|string|array $branchNodeIds = [], bool $ignoreNodeRequestDate = false): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_NONE;

        try {
            $nodeMinHours = $ignoreNodeRequestDate ? null : $minHoursBetweenRequests;
            $browseNodes = $this->retrieveListOfStaleBrowseNodesToCrawl($nodeMinHours, $rootNodeIds, $branchNodeIds);
            if (empty($browseNodes)) {
                $this->logger->info("No browseNodes left to process, so exiting...");
                return;
            } else {
                $this->logger->info(count($browseNodes) . " browseNodes to process");
            }

            $this->logger->info("Fetching POE niches using $profile");

            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                return;
            }

            $this->scrapePoeNiches($profile, $minHoursBetweenRequests, $browseNodes);
            $this->flushChanges();

            if ($this->loggedIn) {
                $this->logOutFromSellerCentral();
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getTraceAsString());
            throw $e;
        } finally {
            $this->tidyUpAtEndOfRun();
        }
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @return void
     * @throws \Exception
     */
    public function manageDownloadOfInventoryDashboard(array $profiles): void
    {
        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_NONE;

        foreach ($profiles as $profile) {
            if ($profile->isSecondaryPotentialPanEuCountry()) {
                $this->logger->info("Skipping profile $profile as it is a secondary potential Pan-EU country");
                continue;
            }
            $this->logger->info("Fetching inventory dashboard data using $profile");

            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->scrapeInventoryDashboard($profile);
                $this->flushChanges();
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @throws \Exception
     */
    protected function handleLoginAndAccountSwitchWithCleanupOnException(AccountProfileSpa $profile, bool $forceLogin = false, bool $throwExceptionIfFail=true): bool
    {
        try {
            $this->doLoginAndAccountSwitchAsNeededForProfile($profile, $forceLogin,  $throwExceptionIfFail);
            return true;
        } catch (\Exception $e) {
            if ($this->isExceptionForNoMarketplaceFound($e, $profile)) {
                return false;
            }
            if ($this->isExceptionForFailedToSwitchAccount($e)) {
                return false;
            }
            $this->tidyUpAtEndOfRun();
            throw $e;
        }
    }

    /**
     * @throws \Exception
     */
    protected function handleLoadAdvertisingCentralAndAccountSwitchWithCleanupOnException(AccountProfileSpa $profile): bool
    {
        try {
            $url = $this->localiseUrl(static::URL_ADVERTISING_CENTRAL_HOMEPAGE, $profile->getAccountAmazon()->getRegion()); // note that this is not an AdCentral URL - using an SC URL causes a bounce that sets the right account in AdCentral
            $this->logger->info("URL is $url");
            $waitTime = 3 + (2 * $this->nestedCallDepth);
            $this->getNewUrl($url, $waitTime);
            usleep(rand(800000, 1800000));

            $this->crawler = $this->client->getCrawler();
            $accountLabel = $this->crawler->filter('button[data-ccx-e2e-id="aac-account-dropdown"] > div > div > p:first-child');
            if ($accountLabel->count() === 0) {
                $this->warningWithScreenshot('AdvertisingCentral no account label');
                return false;
            } else {
                $accountNameFromBrowser = strtolower($accountLabel->text());
                $accountNameFromProfile = strtolower($this->getAmazonAdvertisingAccountNameByProfile($profile));
                $this->logger->info("Current account in AdvertisingCentral: $accountNameFromBrowser");
                if ($accountNameFromProfile !== $accountNameFromBrowser) {
                    $this->warningWithScreenshot("Advertising account name mismatch for " . $profile->getAlias());
                    return false;
                }
            }
            return true;
        } catch (\Exception $e) {
            $this->tidyUpAtEndOfRun();
            throw $e;
        }
    }

    protected function logOutAndTidyUpAtEndOfRun(): void
    {
        try {
            if ($this->loggedIn) {
                $this->logOutFromSellerCentral();
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getTraceAsString());
            throw $e;
        } finally {
            $this->tidyUpAtEndOfRun();
        }
    }

    /**
     * @throws \Exception
     */
    protected function doLoginAndAccountSwitchAsNeededForProfile(AccountProfileSpa $profile, bool $forceLogin = false, bool $throwExceptionIfFail = true): static
    {
        if ($forceLogin || !$this->alreadyLoggedInWithMatchingCredentials($profile)) {
            $this->logInToSellerCentral($profile);

            if (!$this->alreadyLoggedInWithMatchingCredentials($profile)) {
                $message = "Could not log into Seller Central as {$profile->getAlias()}";
                if ($throwExceptionIfFail) {
                    $this->logger->error($message);
                    throw new \RuntimeException($message);
                } else {
                    $this->logger->warning($message);
                    return $this;
                }
            }
        }

        try {
            $this->changeAccountToMatchProfile($profile);
        } catch (\Exception $e) {
            if ($this->isExceptionForNoMarketplaceFound($e, $profile)) {
                throw $e;
            }
            $this->logger->warning('Exception: ' . $e->getMessage());
            $this->logger->warning('Current URL: ' . $this->client->getCurrentURL());
            $this->warningWithScreenshot('486 login exception: Could not change account to match profile... rebooting client to try again');
            $this->rebootClient($profile);
        }

        return $this;
    }

    /**
     * @param AccountProfileSpa $profile
     * @return void
     * @throws \Exception
     */
    protected function logInToSellerCentral(AccountProfileSpa $profile): void
    {
        $loginFormLoaded = $this->navigateToSellerCentralLogInForm($profile);
        if (!$loginFormLoaded) {
            return;
        }

        $shouldBeLoggedInNow = $this->processLoginFormCaptchaAndOtp($profile);
        $this->setLoginTrackerToNotLoggedIn();
        if ($shouldBeLoggedInNow) {
            if ($this->crawler->filter(self::SELECTOR_LOGIN_FORM_SUBMIT_ALL)->count() > 0) {
                $this->logger->info("Should be logged in, but actually back on login page...");
                $this->sleepForSeconds(5, 20);
                $shouldBeLoggedInNow = $this->processLoginFormCaptchaAndOtp($profile);
                if ($shouldBeLoggedInNow) {
                    /** @phpstan-ignore-next-line */
                    if ($this->crawler->filter(self::SELECTOR_LOGIN_FORM_SUBMIT_ALL)->count() > 0) {
                        $this->errorWithScreenshot("failed login again: should be logged in, but actually back on login page AGAIN... giving up");
                    } else { /** @phpstan-ignore-line */
                        $this->setLoginTrackerToLoggedInAs($profile);
                    }
                }
            } else {
                $this->setLoginTrackerToLoggedInAs($profile);
            }
        }

        $this->resetTimeOfNextMemoryCheck();
    }

    /**
     * @param AccountProfileSpa $profile
     * @return bool
     * @throws \Exception
     */
    protected function navigateToSellerCentralLogInForm(AccountProfileSpa $profile): bool
    {
        $this->baseUrl = '';
        $this->currentCountry = $profile->getCountryCode();
        $this->currentRegion = $profile->getAccountAmazon()->getRegion();
        $potentiallyPersistentProfileInUse = $this->useMitmProfileIfConfigured();
        $this->client = $this->createClient();
        if ($potentiallyPersistentProfileInUse) {
            $this->logOutFromSellerCentral();
        }
        $this->logger->info("Logging in to ASC");
        $url = $this->localiseUrl(self::URL_HOMEPAGE);
        $this->getNewUrl($url, 5);
        $this->debugWithScreenshot('01A start');

        $alreadyLoggedInUrl = $this->localiseUrl(self::URL_DASHBOARD);
        $currentUrl = $this->client->getCurrentURL();
        if ($alreadyLoggedInUrl === $currentUrl) {
            $this->logger->info('Already logged in...');
            $this->setLoginTrackerToLoggedInAs($profile);
            $this->logOutFromSellerCentral();
        }

        if ($this->crawler->filter('a[data-name="sign_out_request"]')->count() > 0) {
            $this->logger->info("Clicking 'Sign out'");
            $this->crawler = $this->client->clickLink('Sign out');
            sleep(3);
            $this->getNewUrl($url, 5);
        }

        $passedCaptcha = $this->manageCaptcha($profile);
        if (!$passedCaptcha) {
            $this->setLoginTrackerToNotLoggedIn();
            $this->errorWithScreenshot('09X Defeated by pre-login CAPTCHA... sad times');
            $this->logger->error($this->client->getCrawler()->html());
            return false;
        }

        if ($this->crawler->filter(self::SELECTOR_LOGIN_FORM_SUBMIT_ALL)->count() === 0) {
            $this->logger->info("Clicking 'Log in'");
            $this->crawler = $this->client->clickLink('Log in');
            sleep(3);
        }

        $passedCaptcha = $this->manageCaptcha($profile);
        if (!$passedCaptcha) {
            $this->setLoginTrackerToNotLoggedIn();
            $this->errorWithScreenshot('09Y Defeated by post-login CAPTCHA... sad times');
            $this->logger->error($this->client->getCrawler()->html());
            return false;
        }

        $accountSwitcher = $this->crawler->filter('#ap_switch_account_link');
        if ($accountSwitcher->count()) {
            $this->infoWithScreenshot('0436A Clicking account switcher');
            $this->client->clickLink($accountSwitcher->text());
        }

        $signOutLinkSelector = '.cvf-account-switcher-sign-out-link';
        try {
            $this->crawler = $this->client->waitFor(self::SELECTOR_LOGIN_FORM_SUBMIT_ALL . ", $signOutLinkSelector", 5);
        } catch (\Exception $e) {
            $this->logger->error($e::class);
            $this->setLoginTrackerToNotLoggedIn();
            return false;
        }

        if ($this->crawler->filter($signOutLinkSelector)->count() > 0) {
            $this->logger->info("Clicking 'Log out' on account switcher");
            $this->clickElementAndWait($signOutLinkSelector);
        }

        $this->debugWithScreenshot('01B Waiting for login form to appear');
        try {
            $this->crawler = $this->client->waitForEnabled(self::SELECTOR_LOGIN_FORM_SUBMIT_ALL);
            $this->debugWithScreenshot('01C pre login');
            return true;
        } catch (\Exception $e) {
            $this->logger->error("Error waiting for login form: " . $e->getMessage());
            return false;
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @return bool
     * @throws \Exception
     */
    protected function processLoginFormCaptchaAndOtp(AccountProfileSpa $profile): bool
    {
        $credentials = $profile->getCrawlerCredentials($this->credentials);
        $this->logger->info("Logging in to ASC as {$credentials['email']}");

        $formHasEmailInput = $this->crawler->filter('input[name="email"].auth-required-field')->count();
        $formHasPasswordInput = $this->crawler->filter('input[name="password"].auth-required-field')->count();
        $this->debugWithScreenshot('01C0 pre login');
        if ($formHasEmailInput && $formHasPasswordInput) {
            $this->logger->info('Filling in email and password');
            $formNode = $this->crawler->filter('form[name="signIn"]');
            $emailType = $formNode->filter('input[name="email"]')->getAttribute('type'); /** @phpstan-ignore-line getAttribute() is OK */

            $formData = ['password' => $credentials['password']];
            if ('hidden' !== $emailType) {
                $formData['email'] = $credentials['email'];
            }

            try {
                $form = $formNode->form($formData);
                $this->crawler = $this->client->submit($form);
            } catch (\Exception $e) {
                $this->logger->error("Failed ASC login1 for {$credentials['email']}");
                $emailFilename = str_replace('@', '(at)', $credentials['email']);
                $this->errorWithScreenshot("failed form1: $emailFilename");
                throw $e;
            }
            sleep(4);
            $this->debugWithScreenshot('01C1 post login');
        } elseif ($formHasEmailInput) {
            $this->logger->info('Filling in email only');
            $formNode = $this->crawler->filter('form[name="signIn"]');
            $formData = ['email' => $credentials['email']];

            try {
                $form = $formNode->form($formData);
                $this->crawler = $this->client->submit($form);
            } catch (\Exception $e) {
                $this->logger->error("Failed ASC login2 for {$credentials['email']}");
                $emailFilename = str_replace('@', '(at)', $credentials['email']);
                $this->errorWithScreenshot("failed form2A: $emailFilename");
                throw $e;
            }
            sleep(4);
            $this->debugWithScreenshot('01C2 post login');

            $formHasPasswordInput = $this->crawler->filter('input[name="password"].auth-required-field')->count();
            if ($formHasPasswordInput) {
                $this->logger->info('Filling in password only');
                $formNode = $this->crawler->filter('form[name="signIn"]');
                $formData = ['password' => $credentials['password']];

                try {
                    $form = $formNode->form($formData);
                    $this->crawler = $this->client->submit($form);
                } catch (\Exception $e) {
                    $this->logger->error("Failed ASC login2 for {$credentials['email']}");
                    $emailFilename = str_replace('@', '(at)', $credentials['email']);
                    $this->errorWithScreenshot("failed form2B: $emailFilename");
                    throw $e;
                }
                sleep(4);
                $this->debugWithScreenshot('01C3 post login');
            } else {
                $this->errorWithScreenshot("failed form3");
                throw new \RuntimeException("Failed ASC login3 for {$credentials['email']} (no password field)");
            }
        }

        $passedCaptcha = $this->manageCaptcha($profile);
        if (!$passedCaptcha) {
            $this->logger->error("Defeated by CAPTCHA... sad times");
            $this->setLoginTrackerToNotLoggedIn();
            return false;
        }

        // $this->crawler = $this->client->waitForEnabled('input[name="otpCode"]', 5);
        if ($this->crawler->filter('input[name="otpCode"]')->count()) {
            $this->logger->info("Supplying OTP");
            $this->submitOtp($this->generateOtp($credentials['otpSeed']));
        }

        $this->loggedInUsername = $credentials['email'];
        return true;
    }

    /**
     * @return list<string>
     */
    protected function getCaptchaSelectors(): array
    {
        return [
            '#auth-captcha-image',
            '.cvf-captcha-img img[alt="captcha"]',
            '#auth-captcha-image-container',
            'form[action="/errors/validateCaptcha"] img'
        ];
    }

    protected function getCaptchaImageSrc(): ?string
    {
        $captchaImageSrc = null;
        foreach ($this->getCaptchaSelectors() as $selector) {
            $captchaNode = $this->crawler->filter($selector);
            if ($captchaNode->count() === 0) {
                continue;
            }
            try {
                $captchaImageSrc = $captchaNode->attr('src');
            } catch (\InvalidArgumentException) {
                // just continue if no src attribute
            }
            if ($captchaImageSrc) {
                break;
            }
        }
        return $captchaImageSrc;
    }

    protected function submitCaptcha(AccountProfileSpa $profile, string $captcha): void
    {
        $captchaType = $this->evaluateCaptchaType();
        if (is_null($captchaType) || empty($captchaType['type'])) {
            $this->logger->info("No CAPTCHA type found...");
        } else {
            $credentials = $profile->getCrawlerCredentials($this->credentials);
            $this->logger->info("Submitting {$captchaType['type']} CAPTCHA...");
            $formNode = $captchaType['formNode'];

            if (self::CAPTCHA_TYPE_SIGN_IN === $captchaType['type']) {
                $form = $formNode->form([
                    'email'    => $credentials['email'],
                    'password' => $credentials['password'],
                    'guess'    => $captcha,
                ]);
            } elseif (self::CAPTCHA_TYPE_CVF === $captchaType['type']) {
                $captcha = strtolower($captcha);
                $form = $formNode->form([
                    'cvf_captcha_input' => $captcha,
                ]);
            } elseif (self::CAPTCHA_TYPE_HARD_HIT === $captchaType['type']) {
                $captcha = strtoupper($captcha);
                $form = $formNode->form([
                    'field-keywords' => $captcha,
                ]);
            } else {
                $this->logger->info("No submission form found...");
                return;
            }

            $this->logger->info("Using CAPTCHA $captcha as solution");
            $this->crawler = $this->client->submit($form);
        }
        return;
    }

    /**
     * @return array{type: string, formNode: Crawler}|null
     */
    protected function evaluateCaptchaType(): ?array
    {
        $formNode = $this->crawler->filter('form[name="signIn"]');
        if ($formNode->count()) {
            return ['type' => self::CAPTCHA_TYPE_SIGN_IN, 'formNode' => $formNode];
        }

        $formNode = $this->crawler->filter('form.cvf-widget-form');
        if ($formNode->count()) {
            return ['type' => self::CAPTCHA_TYPE_CVF, 'formNode' => $formNode];
        }

        $formNode = $this->crawler->filter('form[action="/errors/validateCaptcha"]');
        if ($formNode->count()) {
            return ['type' => self::CAPTCHA_TYPE_HARD_HIT, 'formNode' => $formNode];
        }

        return null;
    }

    protected function manageCaptcha(AccountProfileSpa $profile): bool
    {
        $maxAttempts = 20;
        for ($thisAttempt = 1; $thisAttempt < $maxAttempts; $thisAttempt++) {
            $captchaUrl = $this->getCaptchaImageSrc();
            if (empty($captchaUrl)) {
                return true;
            }

            $this->saveCaptchaImage($captchaUrl);
            $captchaSubmitted = $this->manageCaptchaViaTwoCaptcha($profile, $captchaUrl);
            if (false === $captchaSubmitted) {
                continue;
            } elseif (is_null($captchaSubmitted)) {
                // try a different method
                $captchaSubmitted = $this->manageCaptchaViaSlack($profile, $captchaUrl);
            }

            if (false === $captchaSubmitted) {
                continue;
            } elseif (is_null($captchaSubmitted)) {
                // no more different methods to try
                break;
            }

            if ($this->checkIfCaptchaIsSolved()) {
                return true;
            }
        }

        $this->logger->error('Failed to solve CAPTCHA after multiple attempts');
        return false;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string|null $captchaUrl
     * @return bool|null true = submitted a solution, false = failed to submit (but try again), null = do not try again
     */
    protected function manageCaptchaViaTwoCaptcha(AccountProfileSpa $profile, ?string $captchaUrl): ?bool
    {
        if (empty($captchaUrl)) {
            return true;
        }

        if (!$this->twoCaptchaManager->isEnabled()) {
            $this->logger->info("TwoCaptcha disabled...");
            return null;
        }
        $this->logger->info("Using TwoCaptcha solver...");

        $solution = $this->twoCaptchaManager->fetchCaptchaSolution($captchaUrl);
        $this->logger->info("TwoCaptcha Result:" . json_encode($solution));

        if (is_null($solution)) {
            return false;
        }

        if ($captcha = $solution->code) {
            $this->submitCaptcha($profile, $captcha);
            return true;
        }

        return false;
    }

    protected function manageCaptchaViaSlack(AccountProfileSpa $profile, ?string $captchaUrl): ?bool
    {
        if (empty($captchaUrl)) {
            return true;
        }
        $this->logger->info("Using Slack captcha solver...");

        $this->publishCaptchaViaSlack($profile, $captchaUrl);
        $this->logger->info("CAPTCHA awaiting Slack response...");
        for ($i = 0; $i < 90; $i++) {
            sleep(9);
            if ($captcha = $this->getCaptchaSolutionViaSlack($profile)) {
                $this->crawler = $this->client->getCrawler();
                usleep(100000 * rand(1, 50));
                $this->logger->info("Using CAPTCHA $captcha as solution");
                $this->submitCaptcha($profile, $captcha);
                if ($this->lastSlackMessage) {
                    $this->slackMessenger->deleteMessage($this->lastSlackMessage, self::SLACK_CHANNEL_CAPTCHA);
                    $this->lastSlackMessage = null;
                }

                return true;
            }
        }

        if ($this->lastSlackMessage) {
            $this->slackMessenger->deleteMessage($this->lastSlackMessage, self::SLACK_CHANNEL_CAPTCHA);
            $this->lastSlackMessage = null;
        }
        $this->logger->info("CAPTCHA slack timed out...");
        return null;
    }

    protected function publishCaptchaViaSlack(AccountProfileSpa $profile, ?string $captchaUrlOrNullToRefresh): void
    {
        if (is_null($captchaUrlOrNullToRefresh)) {
            $captchaUrl = $this->getCaptchaImageSrc();
        } else {
            $captchaUrl = $captchaUrlOrNullToRefresh;
        }

        if (empty($captchaUrl)) {
            $this->logger->info("No CAPTCHA image found...");
            foreach ($this->getCaptchaSelectors() as $selector) {
                $node = $this->crawler->filter($selector);
                if ($node->count() && strlen($node->html()) > 1) {
                    $this->warningWithScreenshot("02 captcha found: known captcha form has no image (from $selector) {$node->html()}");
                    return;
                }
            }
            $node = $this->crawler->filter('form');
            if ($node->count()) {
                $this->errorWithScreenshot("02 captcha not found: unknown captcha form {$node->html()}");
                $this->logger->info($node::class);
                $this->logger->info($this->crawler->html());
            }
            return;
        }
        $captchaMessage = "{$profile->getAlias()} CAPTCHA: $captchaUrl";
        $this->logger->info($captchaMessage);
        $messageResponse = $this->slackMessenger->sendMessageToChannel($captchaMessage, self::SLACK_CHANNEL_CAPTCHA);
        $this->lastSlackMessage = $messageResponse?->getMessage();
    }

    protected function saveCaptchaImage(?string $captchaUrl): void
    {
        if (!empty($captchaUrl)) {
            $this->logger->info('Trying to save Captcha image');

            $captchaType = $this->evaluateCaptchaType();
            if (!empty($captchaType['type'])) {
                $filePrefix = $captchaType['type'] . '-';
            } else {
                $filePrefix = '';
            }

            $fileSystem = $this->getFileSystem();
            $imageContent = file_get_contents($captchaUrl);

            $filename = "captcha/$filePrefix" . GravitiqTools::buildRandomString() . BaseApiBridge::FILE_EXTENSION_JPG;
            try {
                $fileSystem->write($filename, $imageContent);
                $this->logger->info("Saved captcha file $filename to filesystem");

            } catch (FilesystemException $e) {
                $message = "Error saving captcha file $filename to filesystem";
                $this->logger->error("$message: {$e->getMessage()}");
                throw new \InvalidArgumentException($message, 0, $e);
            }
        }
    }

    protected function getCaptchaSolutionViaSlack(AccountProfileSpa $profile): ?string
    {
        $this->refresh($profile->getAccountAmazon());
        if ($captcha = $profile->getAccountAmazon()->getCrawlCaptcha()) {
            $profile->getAccountAmazon()->setCrawlCaptcha(null);
            $this->flushChanges();
            return $captcha;
        }
        if (is_null($this->lastSlackMessage)) {
            $this->publishCaptchaViaSlack($profile, null);
            if (is_null($this->lastSlackMessage)) {
                throw new \InvalidArgumentException("No lastSlackMessage, tried twice...");
            }
        }
        if ($message = $this->slackMessenger->getNthReplyFromThread($this->lastSlackMessage, self::SLACK_CHANNEL_CAPTCHA)) {
            foreach ($message as $timestamp => $captcha) {
                if (strlen($captcha) === 6) {
                    $this->slackMessenger->addReactionThumbsUpToMessage($timestamp, self::SLACK_CHANNEL_CAPTCHA);
                    return $captcha;
                }
            }
        }
        return null;
    }

    protected function checkIfCaptchaIsSolved(): bool
    {
        sleep(4);
        if (!is_null($this->getCaptchaImageSrc())) {
            $this->logger->warning("CAPTCHA still not solved... try again");
            return false;
        } else {
            $this->logger->info("CAPTCHA solved");
            return true;
        }
    }

    public function generateOtp(?string $seed = null): string
    {
        $seed = $seed ?? $this->credentials['otpSeed'];
        $otp = TOTP::create($seed);
        if (2 >= $otp->expiresIn()) {
            $microseconds = 2.2E6;
            usleep((int)$microseconds);
        }
        return $otp->now();
    }

    public function submitOtp(string $otp): void
    {
        // $this->crawler = $this->client->waitForEnabled('form#auth-mfa-form', 5);
        $form = $this->crawler->filter('form#auth-mfa-form')->form([
            'otpCode' => $otp,
        ]);
        $this->crawler = $this->client->submit($form);
        sleep(2);
        $this->debugWithScreenshot('02 post-otp');
    }

    /**
     * @param AccountProfileSpa|int $profileOrId
     * @return static
     * @throws \Exception
     */
    protected function changeAccountToMatchProfile(AccountProfileSpa|int $profileOrId): self
    {
        if ($profileOrId instanceof AccountProfileSpa) {
            $profile = $profileOrId;
        } else {
            /** @var AccountProfileSpaRepository $profileRepo */
            $profileRepo = $this->getRepository(AccountProfileSpa::class);
            $profile = $profileRepo->find($profileOrId);
        }

        if (empty($profile)) {
            throw new \InvalidArgumentException("Cannot load profile in changeAccountToMatchProfile()");
        }

        if ($this->checkCorrectAccountIsActiveForProfile($profile)) {
            return $this;
        }

        $this->resetBaseUrl();
        $this->currentCountry = $profile->getCountryCode();
        $this->currentRegion = $profile->getAccountAmazon()->getRegion();
        $maxRetries = 2;

        if ($this->performAccountSelectionWithRetries($profile, $maxRetries)) {
            return $this;
        }

        $message = 'Failed to switch account after multiple attempts';
        $this->logger->error($message);
        throw new \Exception($message);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param int $maxRetries
     * @param string $context
     * @return bool
     * @throws \Exception
     */
    protected function performAccountSelectionWithRetries(AccountProfileSpa $profile, int $maxRetries, string $context = ''): bool
    {
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $this->logger->info("Attempting account selection{$context} (Attempt $attempt of $maxRetries)");

            try {
                if ($this->attemptAccountSelection($profile)) {
                    $this->logger->info("Account selection succeeded on attempt $attempt{$context}");
                    return true;
                }
            } catch (\Exception $e) {
                throw new \Exception("Critical failure (account selection) on attempt $attempt: " . $e->getMessage());
            }

            if ($attempt < $maxRetries) {
                $this->logger->warning("Account selection failed on attempt $attempt{$context}. Retrying after delay...");
                $delaySeconds = pow(2, $attempt);
                sleep($delaySeconds);
            } else {
                $this->logger->error("Account selection failed after $maxRetries attempts{$context}");
            }
        }

        return false;
    }

    /**
     * Attempts to select the account and marketplace using V2 or V1 methods
     */
    protected function attemptAccountSelection(AccountProfileSpa $profile): bool
    {
        return $this->selectAccountAndMarketplaceForProfileV2($profile)
            || $this->selectAccountAndMarketplaceForProfileV1($profile);
    }

    protected function selectAccountAndMarketplaceForProfileV1(AccountProfileSpa $profile): bool
    {
        try {
            $this->openAccountPickerPage($profile);
            $this->selectAccountFromPickerForm($profile);
            return true;
        } catch (\Exception $e) {
            if ($this->isExceptionForNoMarketplaceFound($e, $profile)) {
                $this->logger->warning('V1 Marketplace does not seem to exist: ' . $e->getMessage());
                throw new \InvalidArgumentException($e->getMessage());
            }
            $this->logger->warning('V1 account picker failed: ' . $e->getMessage());
        }
        $this->errorWithScreenshot('V1 account picker failed');
        return false;
    }

    protected function isExceptionForNoMarketplaceFound(\Exception $e, AccountProfileSpa $profile): bool
    {
        $marketplaceName = $this->convertCountryCodeToFullText($profile->getCountryCode());
        return str_contains($e->getMessage(), 'No marketplaces found under account')
            || str_contains($e->getMessage(), 'Marketplace ' . $marketplaceName . ' not found under account');
    }

    protected function isExceptionForFailedToSwitchAccount(\Exception $e): bool
    {
        return str_contains($e->getMessage(), 'Failed to switch account');
    }

    /**
     * @throws \Exception
     */
    protected function openAccountPickerPage(AccountProfileSpa $profile): void
    {
        $this->logger->info("Choosing Account");
        $url = $this->localiseUrl(self::URL_GLOBAL_PICKER);
        $this->getNewUrl($url, 5);

        $passedCaptcha = $this->manageCaptcha($profile);
        if (!$passedCaptcha) {
            $this->setLoginTrackerToNotLoggedIn();
            $this->errorWithScreenshot('09Z Defeated by pre-picker CAPTCHA... sad times');
            $this->logger->error($this->client->getCrawler()->html());
        }
    }

    /**
     * @throws \Exception
     */
    protected function selectAccountFromPickerForm(AccountProfileSpa $profile): void
    {
        $amazonAccountName = $this->getAmazonSellerAccountNameByProfile($profile);
        $marketplaceName = $this->convertCountryCodeToFullText($profile->getCountryCode());

        $this->crawler = $this->client->getCrawler();
        $pickerColumns = $this->crawler->filter('div.picker-view-column');
        $numColumns = $pickerColumns->count();
        if (1 > $numColumns) {
            $errorFilename = "unexpected column picker view {$profile->getAlias()}";
            $errorMessage = "Found $numColumns columns on {$profile->getAlias()} account picker screen... but expected to find 3";
            $this->errorWithScreenshot("$errorFilename: $errorMessage");
            file_put_contents("$errorFilename.html", $this->crawler->html());
            throw new \Exception($errorMessage);
        }

        for ($i = 0; $i < 3; ++$i) {
            $this->debugWithScreenshot('column picker view');
            $pickerColumns = $this->crawler->filter('div.picker-view-column'); // refresh because it can be changed inside the loop
            $pickerCol = $pickerColumns->eq($i);
            $pickerOptions = $pickerCol->filter('div.picker-item-column > div');
            $optionToChoose = ($i < 2 ? $amazonAccountName : $marketplaceName);
            if ($numOptions = $pickerOptions->count()) {
                for ($j = 0; $j < $numOptions; ++$j) {
                    $pickerOption = $pickerOptions->eq($j)->filter('div.picker-name');
                    $colPlusOne = $i + 1;
                    $optionPlusOne = $j + 1;
                    $optionSelector = "div.picker-view-column:nth-child($colPlusOne) div.picker-item-column > div:nth-child($optionPlusOne) button";

                    $this->scrollIntoView($optionSelector);

                    if ($pickerOption->text() === $optionToChoose) {
                        $this->logger->info("{$pickerOption->text()} === $optionToChoose");
                        $this->clickElementAndWait($optionSelector, 2);
                        break;
                    } else {
                        $this->logger->debug("{$pickerOption->text()} !== $optionToChoose");
                    }
                }
            }
        }

        usleep(rand(350, 950));
        $this->logger->info("Selected marketplace...");
        $this->checkCorrectAccountIsSelectedV1($amazonAccountName, $marketplaceName);
        $this->clickElementAndWait('div.picker-footer button', 5);
        $this->logger->info("...and submitted");
        usleep(rand(350, 950));
        $this->debugWithScreenshot('03 login complete');
        $this->resetBaseUrl();
        usleep(rand(350, 950));
        $this->checkCorrectAccountIsActive($profile);
    }

    /**
     * @throws \Exception
     */
    protected function selectAccountAndMarketplaceForProfileV2(AccountProfileSpa $profile): bool
    {
        try {
            $this->openAccountSwitcherPage($profile);
            $this->selectAccountFromSwitcherForm($profile);
            return true;
        } catch (\Exception $e) {
            if ($this->isExceptionForNoMarketplaceFound($e, $profile)) {
                $this->logger->warning('V2 Marketplace does not seem to exist: ' . $e->getMessage());
                throw new \InvalidArgumentException($e->getMessage());
            }
            $this->errorWithScreenshot("V2 account switcher failed: {$e->getMessage()}");
        }
        return false;
    }

    /**
     * @throws \Exception
     */
    protected function openAccountSwitcherPage(AccountProfileSpa $profile): void
    {
        $this->logger->info("Choosing Account");
        $url = $this->localiseUrl(self::URL_ACCOUNT_SWITCHER);
        $this->getNewUrl($url, 5);
        $this->debugWithScreenshot('02 account-switcher');

        $passedCaptcha = $this->manageCaptcha($profile);
        if (!$passedCaptcha) {
            $this->loggedIn = false;
            $this->errorWithScreenshot('09Z Defeated by pre-switcher CAPTCHA... sad times');
            $this->logger->error($this->client->getCrawler()->html());
        }
    }

    /**
     * @throws \Exception
     */
    protected function selectAccountFromSwitcherForm(AccountProfileSpa $profile): void
    {
        $amazonAccountName = $this->getAmazonSellerAccountNameByProfile($profile);
        $marketplaceName = $this->convertCountryCodeToFullText($profile->getCountryCode());

        $this->crawler = $this->client->getCrawler();
        $this->debugWithScreenshot('switcher view');

        // Get the top-level accounts
        $accounts = $this->crawler->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_ACCOUNT_NODE);

        if ($accounts->count() < 1) {
            $message = "No accounts found in switcher form";
            $this->logger->error($message);
            throw new \Exception($message);
        }

        $accountFound = false;
        for ($i = 0; $i < $accounts->count(); $i++) {
            $account = $accounts->eq($i);
            $accountButtonNode = $account->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_BUTTON_RELATIVE);
            $accountLabelNode = $accountButtonNode->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_LABEL_RELATIVE);

            if ($accountLabelNode->count() > 0) {
                $accountLabel = trim($accountLabelNode->text());
                $accountLabelClean = preg_replace('/\s*\(current\)$/', '', $accountLabel);

                if ($accountLabelClean == $amazonAccountName) {
                    $accountFound = true;
                    $this->logger->info("Found account: $accountLabel");

                    // check if node is already expanded
                    if (!str_contains($accountLabelNode->attr('class'), 'full-page-account-switcher-account-label-expanded')) {
                        // click to expand the element
                        $buttonSelector = self::SELECTOR_ACCOUNT_SWITCHER_V2_ACCOUNT_NODE . ":nth-child(" . ($i + 1) . ') > ' . self::SELECTOR_ACCOUNT_SWITCHER_V2_BUTTON_RELATIVE;
                        $this->scrollIntoView($buttonSelector);
                        $this->clickElementAndWait($buttonSelector, 2);
                        $this->debugWithScreenshot('account expanded');
                        $this->crawler = $this->client->refreshCrawler();

                        // Refresh the account node after expanding
                        $accounts = $this->crawler->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_ACCOUNT_NODE);
                        $account = $accounts->eq($i);
                    }

                    // Get the marketplaces again
                    $marketplaces = $account->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_NODE_RELATIVE_TO_PARENT);

                    if ($marketplaces->count() < 1) {
                        $message = "No marketplaces found under account $amazonAccountName";
                        $this->logger->error($message);
                        throw new \Exception($message);
                    }

                    $marketplaceFound = false;
                    for ($j = 0; $j < $marketplaces->count(); $j++) {
                        $marketplace = $marketplaces->eq($j);
                        $marketplaceLabelNode = $marketplace->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_BUTTON_RELATIVE . ' > ' . self::SELECTOR_ACCOUNT_SWITCHER_V2_LABEL_RELATIVE);
                        if ($marketplaceLabelNode->count() > 0) {
                            $marketplaceLabel = trim($marketplaceLabelNode->text());
                            $marketplaceLabelClean = preg_replace('/\s*\(current\)$/', '', $marketplaceLabel);

                            if ($marketplaceLabelClean === $marketplaceName) {
                                $marketplaceFound = true;
                                $this->logger->info("Found marketplace: $marketplaceLabel");

                                // Selector for the marketplace button
                                $marketplaceButtonSelector = self::SELECTOR_ACCOUNT_SWITCHER_V2_ACCOUNT_NODE . ":nth-child(" . ($i + 1) . ') ' . self::SELECTOR_ACCOUNT_SWITCHER_V2_NODE_RELATIVE_TO_PARENT . ':nth-child(' . ($j + 1) . ") > " . self::SELECTOR_ACCOUNT_SWITCHER_V2_BUTTON_RELATIVE;

                                $this->scrollIntoView($marketplaceButtonSelector);
                                $this->clickElementAndWait($marketplaceButtonSelector, 5);
                                $this->debugWithScreenshot('marketplace selected');
                                $this->crawler = $this->client->refreshCrawler();
                                break;
                            }
                        }
                    }

                    if (!$marketplaceFound) {
                        $message = "Marketplace $marketplaceName not found under account $amazonAccountName";
                        $this->logger->warning($message);
                        throw new \Exception($message);
                    }
                    break;
                } else {
                    $this->logger->debug("Account label '$accountLabelClean' does not match '$amazonAccountName'");
                }
            }
        }

        if (!$accountFound) {
            $message = "Account $amazonAccountName not found in switcher form";
            $this->logger->error($message);
            throw new \Exception($message);
        }

        usleep(rand(350, 950));
        $this->logger->info("Selected marketplace...");
        $this->checkCorrectAccountIsSelectedV2($amazonAccountName, $marketplaceName);
        $this->debugWithScreenshot('account and marketplace selected');

        $this->crawler = $this->client->refreshCrawler();
        $submitButtonSelector = 'div.full-page-account-switcher-footer button.kat-button--primary';
        if ($this->crawler->filter($submitButtonSelector)->count() > 0) {
            $this->scrollIntoView($submitButtonSelector);
            $this->clickElementAndWait($submitButtonSelector, 5);
            $this->logger->info("...and submitted");
            usleep(rand(350, 950));
            $this->debugWithScreenshot('03 picker login complete');
            $this->resetBaseUrl();
            usleep(rand(350, 950));
            $this->checkCorrectAccountIsActive($profile);
        } else {
            $message = "No submit button found in selectAccountFromSwitcherForm()";
            $this->logger->error($message);
            throw new \Exception($message);
        }
    }

    /**
     * @throws \Exception
     *
     * Checks that the correct account and marketplace are selected in the picker view
     */
    protected function checkCorrectAccountIsSelectedV1(string $amazonAccountName, string $marketplaceName): void
    {
        $this->crawler = $this->client->refreshCrawler();
        $pickerColumns = $this->crawler->filter('div.picker-view-column');
        $numColumns = $pickerColumns->count();
        if (1 > $numColumns) {
            $message = "unexpected column picker view v1: found $numColumns columns when checking account picker screen... but expected to find 3";
            $this->errorWithScreenshot($message);
            $this->logger->error($this->crawler->html());
            throw new \Exception($message);
        }

        for ($i = 0; $i < 3; ++$i) {
            $this->debugWithScreenshot('03C - column picker view');
            $pickerColumns = $this->crawler->filter('div.picker-view-column');
            $pickerCol = $pickerColumns->eq($i);
            $optionToChoose = ($i < 2 ? $amazonAccountName : $marketplaceName);
            $optionSelected = $pickerCol->filter('button.picker-button.pickerselected .picker-name')->text();
            if ($optionSelected !== $optionToChoose) {
                $message = "wrong account or marketplace selected: checked (v1) for account $amazonAccountName in $marketplaceName... but found $optionSelected";
                $this->errorWithScreenshot($message);
                throw new \Exception($message);
            }
        }
    }

    /**
     * @throws \Exception
     */
    protected function checkCorrectAccountIsSelectedV2(string $amazonAccountName, string $marketplaceName): void
    {
        $this->crawler = $this->client->refreshCrawler();
        $this->scrollIntoView(self::SELECTOR_ACCOUNT_SWITCHER_V2_SELECTED_COUNTRY);

        $selectedCountryNode = $this->crawler->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_SELECTED_COUNTRY);
        $selectedCountryNode = $selectedCountryNode->closest('button')->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_LABEL_RELATIVE);
        $selectedCountry = trim(str_replace(' (current)', '', $selectedCountryNode->text()));
        if ($selectedCountry !== $marketplaceName) {
            $message = "Checked (v2) for marketplace '$marketplaceName', but found '$selectedCountry'";
            $this->errorWithScreenshot("wrong marketplace selected: $message");
            throw new \Exception($message);
        }
        $this->debugWithScreenshot('checkCorrectAccountIsSelectedV2');

        $accountCtnr = $selectedCountryNode->closest('.full-page-account-switcher-account > ' . self::SELECTOR_ACCOUNT_SWITCHER_V2_NODE_RELATIVE_TO_PARENT);
        $accountLabel = $accountCtnr->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_LABEL_RELATIVE)->text();
        $accountLabelClean = trim(str_replace('(current)', '', $accountLabel));

        if ($accountLabelClean !== $amazonAccountName) {
            $message = "Checked (v2) for account '$amazonAccountName', but found '$accountLabelClean'";
            $this->errorWithScreenshot("wrong account selected: $message");
            throw new \Exception($message);
        }

        $this->logger->info("Confirmed selected account '$amazonAccountName' in marketplace '$marketplaceName'");
    }

    protected function checkCorrectAccountIsActiveForProfile(AccountProfileSpa $profile): bool
    {
        try {
            $this->checkCorrectAccountIsActive($profile);
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * @throws \Exception
     *
     * Checks that the correct account and marketplace are active in the top menu bar
     */
    protected function checkCorrectAccountIsActive(AccountProfileSpa $profile): void
    {
        // if .full-page-account-switcher-accounts-wrapper is on the current page
        $this->crawler = $this->client->refreshCrawler();
        $wrapper = $this->crawler->filter(self::SELECTOR_ACCOUNT_SWITCHER_V2_WRAPPER);
        if ($wrapper->count() > 0) {
            try {
                $this->logger->info("Account switcher wrapper found... checking account and marketplace");
                $this->selectAccountFromSwitcherForm($profile);
            } catch (\Exception $e) {
                $this->errorWithScreenshot("Error handling switcher in checkCorrectAccountIsActive()");
                $this->logger->error($e->getMessage());
                throw $e;
            }
        }

        $amazonAccountName = $this->getAmazonSellerAccountNameByProfile($profile);
        $marketplaceName = $this->convertCountryCodeToFullText($profile->getCountryCode());

        $this->crawler = $this->client->refreshCrawler();
        $this->sleepForSeconds(3);
        list($brand, $country) = $this->extractBrandAndCountryFromNewSwitcher();
        if ($brand !== $amazonAccountName || $country !== $marketplaceName) {
            $this->client->takeScreenshot('ERROR - wrong account or marketplace.jpg');
            $message = "Current account checked for $amazonAccountName in $marketplaceName... but found $brand in $country";
            $this->logger->info($message);
            throw new \Exception($message);
        } else {
            $this->logger->info("Confirmed logged in as $amazonAccountName in $marketplaceName");
        }
    }

    /**
     * @return string[]
     * @throws \Exception
     */
    protected function extractBrandAndCountryFromNewSwitcher(): array
    {
        $this->logger->info('Trying to get brand and country from new switcher...');
        try {
            $selector = '.dropdown-account-switcher-header-label-global';
            $brand = $this->crawler->filter($selector)->text();
            $selector = '.dropdown-account-switcher-header-label-regional';
            $country = $this->crawler->filter($selector)->text();
            $result = [$brand, $country];
        } catch (\Exception $e) {
            $this->warningWithScreenshot("Exception caught ($selector): {$e->getMessage()}");
            $result = $this->extractBrandAndCountryFromOldSwitcher();
        }

        $this->logger->info("...found {$result[0]} in {$result[1]}");
        return $result;
    }

    /**
     * @return string[]
     * @throws \Exception
     */
    protected function extractBrandAndCountryFromOldSwitcher(): array
    {
        $this->logger->info('Trying to get brand and country from old switcher...');
        try {
            $text = $this->crawler->filter('#partner-switcher .partner-dropdown-button span')->text();
            return explode(' | ', $text);
        } catch (\Exception $e) {
            $this->client->takeScreenshot('ERROR - could not extract brand and country.jpg');
            $message = "Could not extract brand and country from partner switcher: {$e->getMessage()}";
            $this->logger->warning($message);
            throw new \Exception($message);
        }
    }

    protected function getAmazonSellerAccountNameByProfile(AccountProfileSpa $profile): string
    {
        $amazonAccountName = $profile->getAccountAmazon()->getAccountNameInSellerCentral();
        if (empty($amazonAccountName)) {
            throw new \InvalidArgumentException("Cannot load profile without SellerCentral account name");
        }
        return $amazonAccountName;
    }

    protected function getAmazonAdvertisingAccountNameByProfile(AccountProfileSpa $profile): string
    {
        $amazonAccountName = $profile->getAccountAmazon()->getAaaSellProfileForCountry($profile->getCountryCode())->getAccountNameInAdvertisingCentral();
        if (empty($amazonAccountName)) {
            $amazonAccountName = $this->getAmazonSellerAccountNameByProfile($profile);
        }
        return $amazonAccountName;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param \DateTimeInterface $endDate
     * @param \DateTimeInterface $startDate
     * @return void
     * @throws \Exception
     */
    protected function scrapeSqpBrandDataFromBrandAnalytics(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface $startDate, \DateTimeInterface $endDate): void
    {
        // $this->client->takeScreenshot('89 scrapeSqpBrandDataFromBrandAnalytics.jpg');
        $ajaxResponses = $this->initSqpSessionAndGetAjaxResponses($profile, $countryCode);
        if (empty($ajaxResponses)) {
            $this->logger->error("No AJAX response... abandoning run");
            return;
        }

        $brands = $this->extractBrandsFromAjaxMetadataResponse($ajaxResponses);
        if (empty($brands)) {
            $this->client->takeScreenshot('90 no-brands.jpg');
            $this->logger->error("No brands found... abandoning run");
            return;
        }
        $reportDates = $this->extractReportDatesFromAjaxMetadataResponse($ajaxResponses);
        if (empty($reportDates)) {
            $this->logger->error("No report dates found... abandoning run");
            return;
        } else {
            $lastDate = end($reportDates['brands']);
            $firstDate = reset($reportDates['brands']);
            $this->logger->debug("Found brand report dates from $firstDate to $lastDate ({$this->datePeriod})");
        }

        $urlTemplate = $this->localiseUrl(self::URL_BRAND_ANALYTICS_SQ_PERF_BRAND_RESULTS_WEEKLY, $profile->getAccountAmazon()->getRegion());
        foreach ($brands as $brandName => $brandId) {
            foreach ($reportDates['brands'] as $reportDateString) {
                $extractedData = [];

                if (!$this->isDateWithinRange($reportDateString, $startDate, $endDate)) {
                    continue;
                }

                if ($this->alreadyRequestedSqpBrandAndDate($countryCode, $brandId, $reportDateString)) {
                    $this->logger->info("Already requested SQP for $brandId ($brandName) on $reportDateString ({$this->datePeriod})");
                    continue;
                }

                $params = [
                    '%brand_id%'        => $brandId,
                    '%report_end_date%' => $reportDateString,
                    '%country_code%'    => $this->convertCountryCodeToUrlCountry($countryCode)
                ];
                $url = strtr($urlTemplate, $params);
                $this->logger->info("Opening SQP brand results page: $url");
                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqpBrandData = $this->extractBrandSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $brandName, $brandId);

                if (empty($sqpBrandData)) {
                    $this->logger->info("No SQP data found for brand $brandName");
                    continue;
                }
                if (empty($sqpBrandData['rows']) && !empty($sqpBrandData['meta']['status']) && $sqpBrandData['meta']['status'] == 200) {
                    $this->logger->info("Definitive zero SQP data found for brand $brandName");
                    $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $brandId, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_BRAND, true);
                    continue;
                }

                if ($sqpBrandData['meta']['totalItems'] > $sqpBrandData['meta']['pageSize'] && $sqpBrandData['meta']['pageSize'] < 100) {
                    $this->logger->info("SQP brand data has more items than page size. Changing page size to 100");
                    $this->changeSqpPaginationTo100PerPage($this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                    $ajaxResponses = $this->getStashedAjaxResponses();
                    $sqpBrandData = $this->extractBrandSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $brandName, $brandId);
                }

                if (empty($sqpBrandData['rows'])) {
                    $this->logger->info("No SQP data found for brand $brandName after changing page size to 100");
                    continue;
                }

                $extractedData[$brandName] = [];
                $extractedData[$brandName][$reportDateString] = [];
                $extractedData[$brandName][$reportDateString][$sqpBrandData['meta']['pageNumber']] = $sqpBrandData;

                $numPages = ceil($sqpBrandData['meta']['totalItems'] / $sqpBrandData['meta']['pageSize']);
                if (1 < $numPages) {
                    $pagesToLoad = range(2, $numPages);
                    $failedPages2 = null;
                    $failedPages = $this->extractBrandSqpDataFromPages($extractedData, $reportDateString, $brandName, $brandId, $pagesToLoad);
                    if (count($failedPages)) {
                        $this->logger->info("Failed to load SQP data for brand $brandName pages " . implode(', ', $failedPages) . " (retrying)");
                        $this->sleepForSeconds(10, 15);
                        $this->initSqpSessionAndGetAjaxResponses($profile, $countryCode);
                        $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                        $failedPages2 = $this->extractBrandSqpDataFromPages($extractedData, $reportDateString, $brandName, $brandId, $failedPages);
                    }

                    if (!empty($failedPages2)) {
                        $this->logger->error("Failed to load SQP data for brand $brandName pages " . implode(', ', $failedPages2) . " (not saving incomplete result)");
                        continue;
                    }
                }

                $crawlRequest = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $brandId, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_BRAND);
                if (!empty($crawlRequest)) {
                    $this->saveDataToFileLinkedToParsableEntity($extractedData, $crawlRequest);
                    $this->flushChanges();
                }
            }
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param int|null $maxAgeDays
     * @return void
     * @throws \Exception
     */
    protected function scrapeExperimentsDataFromBrandAnalytics(AccountProfileSpa $profile, string $countryCode, ?int $maxAgeDays): void
    {
        $now = new \DateTimeImmutable();
        $reportDate = $now->format('Y-m-d H:i:s');
        $this->createPersistAndFlushRequest($profile, $countryCode, $reportDate, '', ApiRequestCrawlAsc::REQUEST_TYPE_EXPERIMENT_START, true);

        $experiments = $this->initExperimentsAndGetAjaxResponses($profile);
        if (empty($experiments)) {
            $this->client->takeScreenshot('EXP100 no-experiments.jpg');
            $this->logger->error("No experiments found... abandoning run");
            return;
        }

        $urlTemplate = $this->localiseUrl(self::URL_EXPERIMENT_RESULTS, $profile->getAccountAmazon()->getRegion());
        $recentlyCrawledExperiments = $this->getApiRequestRepository()->retrieveRecentlyCrawledRequestByType($profile->getId(), ApiRequestCrawlAsc::REQUEST_TYPE_EXPERIMENT_RESULTS, $maxAgeDays);
        foreach ($experiments as $experiment) {
            $experimentId = $experiment['experimentId'];

            if (isset($recentlyCrawledExperiments[$experimentId])) {
                $existingExperiment = $recentlyCrawledExperiments[$experimentId];
                $startDate = new \DateTime($experiment['timestampMetadata']['startDate']);
                $currentDate = new \DateTime();
                if ($startDate > $currentDate) {
                    $this->logger->info("Experiment $experimentId: data already requested. Start date is in the future.");
                    continue;
                } else {
                    $endDate = new \DateTime($experiment['timestampMetadata']['endDate']);
                    $lastRequestDate = new \DateTime($existingExperiment['lastRequestDate']);

                    if ($experiment['timestampMetadata']['terminationTime'] !== null) {
                        $terminationDate = new \DateTime($experiment['timestampMetadata']['terminationTime']);
                        $minDate = min($terminationDate, $endDate);
                    } else {
                        $minDate = $endDate;
                    }

                    if ($minDate <= $lastRequestDate) {
                        $this->logger->info("Experiment $experimentId: data already requested. Minimum date is before or equal to last request date.");
                        continue;
                    }
                }
            }

            $this->logger->info("Crawling data for experiment: $experimentId");
            $params = [
                '%experiment_id%' => $experimentId
            ];
            $url = strtr($urlTemplate, $params);
            $ajaxExperimentDetailsUrl = strtr(self::AJAX_PATH_EXPERIMENT_DETAILS, $params);
            $ajaxExperimentResultsUrl = strtr(self::AJAX_PATH_EXPERIMENT_RESULTS, $params);
            $this->logger->info("Opening experiment results page: $url");

            $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor($ajaxExperimentDetailsUrl));
            $ajaxResponses = $this->getStashedAjaxResponses();
            $experimentDetailData = $this->extractExperimentDetailsFromAjaxMetadataResponse($ajaxResponses, $experimentId);

            $requiredFields = ['treatments', 'targets'];
            foreach ($requiredFields as $requiredField) {
                if (empty($experimentDetailData[$requiredField])) {
                    $this->logger->error("No $requiredField found for experiment $experimentId in data: " . json_encode($experimentDetailData));
                    continue 2;
                }
            }

            $experiment['treatments'] = $this->processTreatmentsFromExperimentDetails($experimentDetailData['treatments'], $this->fileSystemUrlAccessible);
            $experiment['targets'] = $this->processTargetsFromExperimentDetails($experimentDetailData['targets'], $this->fileSystemUrlAccessible);

            $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor($ajaxExperimentResultsUrl));
            $ajaxResponses = $this->getStashedAjaxResponses();
            $experimentResultsData = $this->extractExperimentResultsFromAjaxMetadataResponse($ajaxResponses, $experimentId);

            $extractedData = [
                'experiment' => $experiment,
                'results'    => $experimentResultsData
            ];

            $crawlRequest = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDate, $experimentId, ApiRequestCrawlAsc::REQUEST_TYPE_EXPERIMENT_RESULTS);
            if (!empty($crawlRequest)) {
                $this->saveDataToFileLinkedToParsableEntity($extractedData, $crawlRequest);
                $this->flushChanges();
            }

        }
    }

    /**
     * @param list<array<string, mixed>> $treatments
     * @param string $fileSystemName
     * @return list<array<string, mixed>>
     */
    private function processTreatmentsFromExperimentDetails(array $treatments, string $fileSystemName): array
    {
        $httpClient = HttpClient::create();
        $fileSystem = $this->getFileSystem($fileSystemName);
        $processedTreatments = [];

        foreach ($treatments as $treatmentArray) {
            if (empty($treatmentArray['preview']['imagePreviewUrl'])) {
                $processedTreatments[] = $treatmentArray;
                continue;
            }

            $randomString = GravitiqTools::buildRandomString();
            $timestamp = (new \DateTime())->format('Ymd');
            $filename = "treatments/{$timestamp}_{$randomString}" . BaseApiBridge::FILE_EXTENSION_JPG;

            try {
                $response = $httpClient->request('GET', $treatmentArray['preview']['imagePreviewUrl']);
                $imageContent = $response->getContent();
            } catch (ClientExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface|TransportExceptionInterface $e) {
                $message = "Error getting image from URL {$treatmentArray['preview']['imagePreviewUrl']}: " . $e->getMessage();
                $this->logger->error($message);
                throw new \RuntimeException($message, 0, $e);
            }

            try {
                $fileSystem->write($filename, $imageContent);
                $this->logger->info("Saved file $filename to filesystem {$fileSystemName}");

                $treatmentArray['preview']['imagePreviewFileSystem'] = $fileSystemName;
                $treatmentArray['preview']['imagePreviewUrl'] = $filename;
                $processedTreatments[] = $treatmentArray;
            } catch (FilesystemException $e) {
                $message = "Error saving file $filename to filesystem {$fileSystemName}";
                $this->logger->error("$message: {$e->getMessage()}");
                throw new \InvalidArgumentException($message, 0, $e);
            }
        }

        return $processedTreatments;
    }

    /**
     * @param list<array<string,mixed>> $targets
     * @param string $fileSystemName
     * @return list<array<string,mixed>>
     */
    private function processTargetsFromExperimentDetails(array $targets, string $fileSystemName): array
    {
        $httpClient = HttpClient::create();
        $fileSystem = $this->getFileSystem($fileSystemName);
        $processedTargets = [];

        foreach ($targets as $targetArray) {
            if (empty($targetArray['targetInformation']['imageURL'])) {
                $processedTargets[] = $targetArray;
                continue;
            }

            $randomString = GravitiqTools::buildRandomString();
            $timestamp = (new \DateTime())->format('Ymd');
            $filename = "targets/{$timestamp}_{$targetArray['target']}_{$randomString}" . BaseApiBridge::FILE_EXTENSION_JPG;

            try {
                $response = $httpClient->request('GET', $targetArray['targetInformation']['imageURL']);
                $imageContent = $response->getContent();
            } catch (ClientExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface|TransportExceptionInterface $e) {
                $message = "Error getting image from URL {$targetArray['targetInformation']['imageURL']}";
                $this->logger->error("$message: {$e->getMessage()}");
                throw new \RuntimeException($message, 0, $e);
            }


            try {
                $fileSystem->write($filename, $imageContent);
                $this->logger->info("Saved file $filename to filesystem {$fileSystemName}");

                $targetArray['targetInformation']['imageFileSystem'] = $fileSystemName;
                $targetArray['targetInformation']['imageURL'] = $filename;
                $processedTargets[] = $targetArray;
            } catch (FilesystemException $e) {
                $message = "Error saving file $filename to filesystem {$fileSystemName}: " . $e->getMessage();
                $this->logger->error($message);
                throw new \InvalidArgumentException($message);
            }
        }

        return $processedTargets;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $experimentId
     * @return array<string, mixed>
     */
    protected function extractExperimentDetailsFromAjaxMetadataResponse(array $ajaxResponses, string $experimentId): array
    {
        $jsonResponse = $this->getJsonResponse($ajaxResponses, str_replace(['%experiment_id%'], [$experimentId], self::AJAX_PATH_EXPERIMENT_DETAILS), true, true);
        if ($jsonResponse === null) {
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractExperimentDetailsFromAjaxMetadataResponse");
            return [];
        }

        return $dataArray;
    }

    /**
     * @param array<string, array<string, array<int, array<string, mixed>>>> $extractedData
     * @param string $reportDateString
     * @param string $brandName
     * @param int|string $brandId
     * @param list<int> $pagesToLoad
     * @return list<int>
     * @throws \Exception
     */
    protected function extractBrandSqpDataFromPages(array &$extractedData, string $reportDateString, string $brandName, int|string $brandId, array $pagesToLoad): array
    {
        $failedPages = [];
        $numPages = count($pagesToLoad);
        foreach ($pagesToLoad as $i => $p) {
            $pageFound = false;
            for ($attempt = 1; $attempt <= 3; ++$attempt) {
                $cycle = $i + 1;
                $this->logger->info("Opening SQP brand results page $p ($cycle of $numPages)");
                $this->changeSqpPaginationToPage($p, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqpBrandData = $this->extractBrandSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $brandName, $brandId);

                if (empty($sqpBrandData['rows'])) {
                    $this->logger->info("No SQP data found for brand $brandName page $p in attempt $attempt - waiting 5 seconds for secondary AJAX response");
                    sleep(5);
                    $ajaxResponses = $this->getStashedAjaxResponses();
                    $sqpBrandData = $this->extractBrandSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $brandName, $brandId);
                }

                if (empty($sqpBrandData['rows'])) {
                    $this->logger->warning("No SQP data found for brand $brandName page $p in attempt $attempt");
                    sleep(12);
                    continue;
                } else {
                    $pageFound = true;
                }
                break;
            }
            if ($pageFound) {
                $extractedData[$brandName][$reportDateString][$sqpBrandData['meta']['pageNumber']] = $sqpBrandData;
                sleep(1);
            } else {
                $this->logger->error("Failed to load SQP data for brand $brandName page $p");
                $failedPages[] = $p;
            }
        }
        return $failedPages;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @param bool $ignorePreLaunchAsins
     * @return void
     * @throws \Exception
     */
    protected function scrapeSqpAsinDataFromBrandAnalytics(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface $startDate, \DateTimeInterface $endDate, bool $ignorePreLaunchAsins): void
    {
        $ajaxResponses = $this->initSqpSessionAndGetAjaxResponses($profile, $countryCode);
        if (empty($ajaxResponses)) {
            $this->logger->error("No AJAX response... abandoning run");
            return;
        }

        $asins = $this->extractAsinsFromAjaxMetadataResponse($ajaxResponses);
        if (empty($asins)) {
            $this->logger->info("No ASINs found in Metadata... using database instead");
            $asins = $this->retrieveAsinsFromListingData($profile, $countryCode);
            if (empty($asins)) {
                $this->logger->info("No ASINs found");
                return;
            }
        }
        $numAsins = count($asins);
        $asinsCreateDate = $this->retrieveFirstCreateDatePerAsinInCountry($asins, $countryCode);
        $asinsFirstReportDate = $this->retrieveFirstReportDatePerAsinInCountry($asins, $countryCode);
        $this->logger->info("$numAsins ASINs found");

        $reportDates = $this->extractReportDatesFromAjaxMetadataResponse($ajaxResponses);
        $lastDate = end($reportDates['asins']);
        $firstDate = reset($reportDates['asins']);
        $this->logger->debug("Found asin report dates from $firstDate to $lastDate ({$this->datePeriod})");
        $this->createCacheListOfAlreadyRequestedSqpAsinAndDate($countryCode, $startDate, $endDate, $asins);
        $numCacheHits = count($this->cachedListOfAlreadyRequestedSqpAsinAndDate);
        $this->logger->info("Found $numCacheHits existing SqpAsin requests");
        $urlTemplate = $this->localiseUrl(self::URL_BRAND_ANALYTICS_SQ_PERF_ASIN_RESULTS_WEEKLY);
        foreach ($reportDates['asins'] as $reportDateString) {
            if (!$this->isDateWithinRange($reportDateString, $startDate, $endDate)) {
                $this->logger->debug("Skipping SQP ASIN results for $reportDateString ({$this->datePeriod})");
                continue;
            }

            $asinLoop = 0;
            foreach ($asins as $asin) {
                ++$asinLoop;
                $extractedDataSqp = [];

                if ($this->alreadyRequestedSqpAsinAndDate($countryCode, $asin, $reportDateString)) {
                    $this->logger->info("Already requested SQP for $asin on $reportDateString ({$this->datePeriod})");
                    continue;
                }

                if ($ignorePreLaunchAsins) {
                    if (!$this->hasSuccessfulRequestBefore($asinsFirstReportDate, $asin, $reportDateString)) {
                        if ($this->isAsinCreatedAfterDatePeriod($asinsCreateDate, $asin, $reportDateString)) {
                            $this->logger->info("Skipping requested SQP for $asin on $reportDateString because it was not created until {$asinsCreateDate[$asin]['createDate']} ({$this->datePeriod})");
                            continue;
                        }
                    }
                }

                $numAsinQueries = 0;
                $params = [
                    '%asin%'            => $asin,
                    '%report_end_date%' => $reportDateString,
                    '%country_code%'    => $this->convertCountryCodeToUrlCountry($countryCode)
                ];
                $url = strtr($urlTemplate, $params);
                $this->logger->info("Opening SQP ASIN results page: $url ($asinLoop of $numAsins)");
                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                // $this->client->takeScreenshot('54 SQP ASIN results.jpg');
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);

                if (empty($sqpAsinData['rows'])) {
                    if (isset($sqpAsinData['meta']['totalItems'])) {
                        $this->logger->info("Definitive zero results of SQP data for asin $asin");
                        $request = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $asin, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_ASIN, true);
                        if (is_null($request)) {
                            throw new \Exception("Failed to create request for SQP ASIN $asin (1)");
                        }
                        continue;
                    } else {
                        $this->sleepForSeconds(10);
                        $ajaxResponses = $this->getStashedAjaxResponses();
                        $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);

                        if (empty($sqpAsinData['rows'])) {
                            $this->logger->info("Failed to get definitive SQP data result for asin $asin... loading a different page then coming back in 5s...");
                            $urlTemp = $this->localiseUrl(self::URL_DASHBOARD, $profile->getAccountAmazon()->getRegion());
                            $this->getNewUrl($urlTemp, 5);
                            $this->client->takeScreenshot('55 homepage.jpg');

                            $this->logger->info("Reloading URL of interest...");
                            $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                            $ajaxResponses = $this->getStashedAjaxResponses();
                            $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);
                            $this->client->takeScreenshot('56 sqp page.jpg');
                        }

                        if (empty($sqpAsinData['rows'])) {
                            if (isset($sqpAsinData['meta']['totalItems'])) {
                                $this->logger->info("Definitive zero results of SQP data for asin $asin");
                                $request = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $asin, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_ASIN, true);
                                if (is_null($request)) {
                                    throw new \Exception("Failed to create request for SQP ASIN $asin (2)");
                                }
                                continue;
                            } else {
                                $this->logoutWaitAndLogBackIn($profile);
                                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                                $ajaxResponses = $this->getStashedAjaxResponses();
                                $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);

                                if (empty($sqpAsinData['rows'])) {
                                    if (isset($sqpAsinData['meta']['totalItems'])) {
                                        $this->logger->info("Definitive zero results of SQP data for asin $asin");
                                        $request = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $asin, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_ASIN, true);
                                        if (is_null($request)) {
                                            throw new \Exception("Failed to create request for SQP ASIN $asin (3)");
                                        }
                                    } else {
                                        $this->logger->info("Failed to get definitive SQP data result for asin $asin ... giving up for now");
                                    }
                                    continue;
                                }
                            }
                        }
                    }
                }

                if ($sqpAsinData['meta']['totalItems'] > $sqpAsinData['meta']['pageSize'] && $sqpAsinData['meta']['pageSize'] < 100) {
                    $this->logger->info("SQP asin data has more items than page size. Changing page size to 100");
                    $this->changeSqpPaginationTo100PerPage($this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                    $ajaxResponses = $this->getStashedAjaxResponses();
                    $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);
                }

                if (empty($sqpAsinData['rows'])) {
                    $this->logger->info("No SQP data found for asin $asin");
                    continue;
                } else {
                    $numAsinQueries += count($sqpAsinData['rows']);
                }

                $extractedDataSqp[$asin] = [];
                $extractedDataSqp[$asin][$reportDateString] = [];
                $extractedDataSqp[$asin][$reportDateString][$sqpAsinData['meta']['pageNumber']] = $sqpAsinData;

                $numPages = ceil($sqpAsinData['meta']['totalItems'] / $sqpAsinData['meta']['pageSize']);
                for ($i = 2; $i <= $numPages; $i++) {
                    $this->logger->info("Opening SQP asin results page $i of $numPages");
                    $this->changeSqpPaginationToPage($i, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQP));
                    $ajaxResponses = $this->getStashedAjaxResponses();
                    $sqpAsinData = $this->extractAsinSqpDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin);
                    if (empty($sqpAsinData['rows'])) {
                        $this->logger->warning("No SQP data found for $asin page $i");
                        continue;
                    }
                    $numAsinQueries += count($sqpAsinData['rows']);
                    $extractedDataSqp[$asin][$reportDateString][$sqpAsinData['meta']['pageNumber']] = $sqpAsinData;
                }

                $this->logger->info("$numAsinQueries queries found for asin $asin");

                $queries = [];
                foreach ($extractedDataSqp[$asin][$reportDateString] as $pageNumber => $sqpAsinData) {
                    foreach ($sqpAsinData['rows'] as $queryData) {
                        $queries[] = $queryData['qp-asin-query'];
                    }
                }
                if (!empty($queries)) {
                    $this->processNewTimedOutSqdQueries($profile, $countryCode, $reportDateString, $asin, $queries);
                } else {
                    $this->logger->warning("No SQD queries found for asin $asin");
                }

                $crawlRequestSqp = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDateString, $asin, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_ASIN);
                if (is_null($crawlRequestSqp)) {
                    throw new \Exception("Failed to create request for SQP ASIN $asin (4)");
                }
                $this->saveDataToFileLinkedToParsableEntity($extractedDataSqp, $crawlRequestSqp);
                $this->flushChanges();

                $this->rebootClientIfMemoryUsageIsHigh($profile);
            }
        }
    }

    /**
     * @param array<string, array{filter: string, firstReportDate: string}> $firstReportDatePerAsin
     * @param string $asin
     * @param string $reportDateString
     * @return bool
     * @throws \Exception
     */
    protected function hasSuccessfulRequestBefore(array $firstReportDatePerAsin, string $asin, string $reportDateString): bool
    {
        if (isset($firstReportDatePerAsin[$asin])) {
            $reportDateTime = GravitiqTools::castToDateTime($reportDateString, 'Y-m-d');
            if (GravitiqTools::castToDateTime($firstReportDatePerAsin[$asin]['firstReportDate']) < $reportDateTime) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param array<string, array{asin: string, createDate: string}> $asinsCreateDate
     * @param string $asin
     * @param string $reportDateString
     * @return bool
     * @throws \Exception
     */
    protected function isAsinCreatedAfterDatePeriod(array $asinsCreateDate, string $asin, string $reportDateString): bool
    {
        if (isset($asinsCreateDate[$asin])) {
            $weekAfterReportDate = GravitiqTools::castToDateTime($reportDateString, 'Y-m-d');
            $weekAfterReportDate->modify('+1 week');

            if (GravitiqTools::castToDateTime($asinsCreateDate[$asin]['createDate']) > $weekAfterReportDate) {
                return true;
            }
        }
        return false;
    }

    protected function retryTimedOutSqdDataFromBrandAnalytics(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface $startDate, \DateTimeInterface $endDate): void
    {
        $timedOutSqdRequests = $this->retrieveTimedOutSqdRequests($profile, $countryCode, $startDate, $endDate);
        if (empty($timedOutSqdRequests)) {
            $this->logger->info("No timed out SQD requests found for {$profile->getAlias()} in $countryCode");
            return;
        } else {
            $this->logger->info("Found " . count($timedOutSqdRequests) . " timed out SQD requests to process");
        }

        sleep(3);
        $doneCount = 0;
        foreach ($timedOutSqdRequests as $timedOutSqdRequest) {
            $this->logger->info("Retrying timed out SQD request #{$timedOutSqdRequest->getId()} for {$timedOutSqdRequest->getFilter()}");
            $extractedDataSqd = $this->scrapeSqdDataFromBrandAnalytics($profile, $countryCode, $timedOutSqdRequest);
            if (!empty($extractedDataSqd)) {
                $this->logger->info("Updating timed out SQD request...");
                $timedOutSqdRequest->setStatusToDone();
                $this->saveDataToFileLinkedToParsableEntity($extractedDataSqd, $timedOutSqdRequest);
                $this->flushChanges();
            }

            ++$doneCount;
            if ($doneCount % 100 == 0) {
                $this->logger->info("# Done $doneCount of " . count($timedOutSqdRequests) . " timed out SQD requests");
            }

            $this->rebootClientIfMemoryUsageIsHigh($profile);
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param string|ApiRequestCrawlAsc $reportDateStringOrRequestObject
     * @param string|null $asin
     * @param list<string>|null $queries
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    protected function scrapeSqdDataFromBrandAnalytics(
        AccountProfileSpa         $profile,
        string                    $countryCode,
        string|ApiRequestCrawlAsc $reportDateStringOrRequestObject,
        ?string                   $asin = null,
        ?array                    $queries = null
    ): array
    {
        if ($reportDateStringOrRequestObject instanceof ApiRequestCrawlAsc) {
            $requestObject = $reportDateStringOrRequestObject;
            $reportDateString = $requestObject->getReportDateString();
            $asin = $requestObject->getFilter();
            $queries = [$requestObject->getQuery()];
        } else {
            $requestObject = null;
            $reportDateString = $reportDateStringOrRequestObject;
            if (is_null($asin)) {
                throw new \InvalidArgumentException("ASIN must be provided");
            }
            if (is_null($queries)) {
                throw new \InvalidArgumentException("Queries must be provided");
            }
        }

        $urlTemplateSqDetails = $this->localiseUrl(self::URL_BRAND_ANALYTICS_SQ_DETAILS_ASIN_RESULTS_WEEKLY, $profile->getAccountAmazon()->getRegion());
        $queryLoop = 0;
        $extractedDataSqd = [];
        $timedOutQueries = [];
        $numAsinQueries = count($queries);
        foreach ($queries as $query) {
            ++$queryLoop;
            $this->logger->info("Processing query '$query' for $asin ($queryLoop of $numAsinQueries)");
            $paramsSqDetails = [
                '%url_encoded_query%' => urlencode($query),
                '%asin%'              => $asin,
                '%report_end_date%'   => $reportDateString,
                '%country_code%'      => $this->convertCountryCodeToUrlCountry($countryCode)
            ];
            $url = strtr($urlTemplateSqDetails, $paramsSqDetails);
            $this->logger->debug("Opening SQ details page");
            $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQD));
            $ajaxResponses = $this->getStashedAjaxResponses();
            $sqdData = $this->extractSQDDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin, $query);

            if (empty($sqdData)) {
                $this->client->takeScreenshot('50 waiting for fetch.jpg');
                $this->sleepForSeconds(10);
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqdData = $this->extractSQDDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin, $query);
            }

            if (empty($sqdData) && strlen($query) > self::MAX_SQD_QUERY_LENGTH_TO_RETRY) {
                $this->logger->warning("Failed to retrieve Long Query SQD data for ASIN $asin query ($query) on $reportDateString, setting request to fatal");
                $requestObject->setStatusToFatal();
                $this->flushChanges();
                continue;
            }

            if (empty($sqdData)) {
                $this->logger->info("Failed to get SQD data... try loading a different page then take a nap before retrying...");
                $urlTemp = $this->localiseUrl(self::URL_BRAND_ANALYTICS_SQ_PERF_ASIN_RESULTS_DOWNLOAD);
                $this->getNewUrl($urlTemp, 5);
                $this->client->takeScreenshot('55 homepage.jpg');

                $signOutLinkSelector = '.cvf-account-switcher-sign-out-link';
                if ($this->crawler->filter($signOutLinkSelector)->count() > 0) {
                    $this->logger->info("Clicking sign out link...");
                    $this->clickElementAndWait($signOutLinkSelector);
                }

                $this->sleepForSeconds(40, 80);

                $this->logger->info("Reloading URL of interest...");
                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQD));
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqdData = $this->extractSQDDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin, $query);
                $this->client->takeScreenshot('56 sqd page.jpg');
            }

            if (empty($sqdData)) {
                $this->logger->info("Going to try waiting one more time before logging out...");

                $this->sleepForSeconds(40, 80);

                $this->logger->info("Reloading URL of interest (v2)...");
                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQD));
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqdData = $this->extractSQDDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin, $query);
                $this->client->takeScreenshot('57 sqd page.jpg');
            }

            if (empty($sqdData)) {
                $this->logoutWaitAndLogBackIn($profile);

                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(self::AJAX_PATH_SQD));
                sleep(2);
                $ajaxResponses = $this->getStashedAjaxResponses();
                $sqdData = $this->extractSQDDataFromAjaxReportsResponse($ajaxResponses, $reportDateString, $asin, $query);
            }

            if (empty($sqdData['rows'])) {
                if (!empty($sqdData['meta']['status']) && $sqdData['meta']['status'] === 'ERROR' && !is_null($requestObject)) {
                    $this->logger->error("Failed to retrieve SQD data for ASIN $asin query ($query) on $reportDateString, setting request to fatal");
                    $requestObject->setStatusToFatal();
                    $this->flushChanges();
                } elseif (!empty($sqdData['meta']['status']) && $sqdData['meta']['status'] == '200' && !is_null($requestObject)) {
                    $this->logger->info("Definitive zero results of SQD data for asin $asin query ($query) on $reportDateString");
                    $requestObject->setStatusToDone();
                    $this->flushChanges();
                } else {
                    $this->logger->warning("SQD data timed out for ASIN $asin query ($query) on $reportDateString");
                    $timedOutQueries[] = $query;
                }
                continue;
            }

            if ($sqdData['meta']['totalItems'] > $sqdData['meta']['pageSize']) {
                $this->logger->error("SQD data has more items than page size.");
            }

            $extractedDataSqd[] = $sqdData;
        }
        $this->rebootClientIfMemoryUsageIsHigh($profile);

        if (!empty($timedOutQueries)) {
            $this->processNewTimedOutSqdQueries($profile, $countryCode, $reportDateString, $asin, $timedOutQueries);
        }
        return $extractedDataSqd;
    }

    /**
     * @param int|string|list<array<string, mixed>> $sleepSecondsOrElement
     * @throws \Exception
     */
    protected function changeSqpPaginationTo100PerPage(int|string|array $sleepSecondsOrElement = 2): bool
    {
        // window.ajaxContent stores ajax responses. We clear it first, so it will only contain post-pagination responses.
        $script = /** @lang JavaScript */
            <<<SCRIPT
{
    window.ajaxContent = [];
    const paginationElement = document.querySelector('kat-pagination'); 
    if (paginationElement === null) {
        return false;
    }
    const catSelect = paginationElement.nextElementSibling;
    if (catSelect === null) {
        return false;
    }
    catSelect.value = 100;
    const changeEvent = new Event('change');
    catSelect.dispatchEvent(changeEvent);
    return true;
}
SCRIPT;
        $paginationSuccess = $this->client->executeScript($script);
        if (!$paginationSuccess) {
            return $this->retryPaginationTo100PerPage($sleepSecondsOrElement);
        }

        $paginationSuccess = $this->waitFor($sleepSecondsOrElement);
        if (!$paginationSuccess) {
            return $this->retryPaginationTo100PerPage($sleepSecondsOrElement);
        }

        return true;
    }

    /**
     * @throws \Exception
     */
    protected function retryPaginationTo100PerPage(int|string $sleepSecondsOrElement): bool
    {
        if ($this->nestedPaginationDepth < 4) {
            $this->logger->warning("Failed to change SQP pagination to 100 per page");
            $napTime = $this->sleepForSeconds(2, 7);
            $this->forceReload($napTime, 3);
            $this->nestedPaginationDepth++;
            $response = $this->changeSqpPaginationTo100PerPage($sleepSecondsOrElement);
            $this->nestedPaginationDepth--;
            return $response;
        } else {
            $this->logger->error("Failed to change SQP pagination to 100 per page ... giving up");
            return false;
        }
    }

    /**
     * @param int $pageNumber
     * @param int|string|list<array<string, mixed>> $sleepSecondsOrElement
     * @return bool
     * @throws \Exception
     */
    protected function changeSqpPaginationToPage(int $pageNumber, int|string|array $sleepSecondsOrElement = 2): bool
    {
        // window.ajaxContent stores ajax responses. We clear it first, so it will only contain post-pagination responses.
        $script = /** @lang JavaScript */
            <<<SCRIPT
{
    window.ajaxContent = [];
    let paginationElement = document.querySelector('kat-pagination');
    if (paginationElement === null) {
        return false;
    }
    paginationElement = paginationElement.shadowRoot.querySelector('li[part="pagination-page-{$pageNumber}"]');
    if (paginationElement === null) {
        return false;
    }
    paginationElement.click();
    return true;
}
SCRIPT;
        $paginationSuccess = $this->client->executeScript($script);
        if (!$paginationSuccess) {
            $this->logger->debug('Failed to run pagination script');
            return $this->retryPagination($pageNumber, $sleepSecondsOrElement);
        }

        $paginationSuccess = $this->waitFor($sleepSecondsOrElement);
        if (!$paginationSuccess) {
            $this->logger->debug("Failed to wait for $sleepSecondsOrElement");
            return $this->retryPagination($pageNumber, $sleepSecondsOrElement);
        }

        return true;
    }

    /**
     * @param int $pageNumber
     * @param int|string|list<array<string, mixed>> $sleepSecondsOrElement
     * @return bool
     * @throws \Exception
     */
    protected function retryPagination(int $pageNumber, int|string|array $sleepSecondsOrElement): bool
    {
        if ($this->nestedPaginationDepth < 2) {
            $this->logger->warning("Failed to change SQP pagination to page $pageNumber");
            $this->sleepForSeconds(2, 15);

            $this->forceReload(10, 3);
            $this->nestedPaginationDepth++;
            $response = $this->changeSqpPaginationToPage($pageNumber, $sleepSecondsOrElement);
            $this->nestedPaginationDepth--;
            return $response;
        } else {
            $this->logger->error("Failed to change SQP pagination to page $pageNumber ... giving up");
            return false;
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function initSqpSessionAndGetAjaxResponses(AccountProfileSpa $profile, string $countryCode): array
    {
        if (is_null($this->datePeriod)) {
            throw new \InvalidArgumentException("Date period must be set before calling initSqpSessionAndGetAjaxResponses");
        }
        $this->logger->info("Opening SQP ASIN start page for $countryCode");
        $url = $this->localiseUrl(self::URL_BRAND_ANALYTICS_SQ_PERF_ASIN_START, $profile->getAccountAmazon()->getRegion());
        $url = str_replace('%country_code%', $this->convertCountryCodeToUrlCountry($countryCode), $url);
        $this->logger->info("URL is $url");
        $waitTime = 5 + (2 * $this->nestedCallDepth);
        $this->getNewUrl($url, $waitTime, false);
        $ajaxResponses = $this->getStashedAjaxResponses();

        if (empty($ajaxResponses)) {
            if (3 > $this->nestedCallDepth) {
                $this->nestedCallDepth++;
                $this->logger->info("No AJAX responses found... forcing reload #{$this->nestedCallDepth}");
                $ajaxResponses = $this->initSqpSessionAndGetAjaxResponses($profile, $countryCode);
                $this->nestedCallDepth--;
            } else {
                $this->logger->error("No AJAX responses found... and cannot retry any more");
            }
        }

        return $ajaxResponses;
    }

    /**
     * @param AccountProfileSpa $profile
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function initExperimentsAndGetAjaxResponses(AccountProfileSpa $profile): array
    {
        $region = $profile->getAccountAmazon()->getRegion();
        $this->logger->info("Opening experiment homepage for {$profile->getAlias()} / $region");
        $url = $this->localiseUrl(self::URL_EXPERIMENT_DASHBOARD, $region);
        $this->logger->info("URL is $url");
        $waitTime = 2 + (2 * $this->nestedCallDepth);
        $this->getNewUrl($url, $waitTime);

        $experimentUrl = $this->localiseUrl(self::AJAX_PATH_EXPERIMENTS, $region);
        $this->getNewUrl($experimentUrl);
        $content = $this->client->getWebDriver()->getPageSource();

        return $this->extractJsonArrayFromContent($content);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param ?int $minHoursBetweenRequests
     * @param bool $onlyGetNewDeals
     * @param bool $fullRefresh
     * @param int $startPage
     * @return void
     * @throws \Exception
     */
    protected function scrapeDealsFromAdvertising(AccountProfileSpa $profile, string $countryCode, ?int $minHoursBetweenRequests, bool $onlyGetNewDeals, bool $fullRefresh, int $startPage = 1): void
    {
        $page = $startPage;
        $dealsSeen = 50 * ($page - 1);
        $urlTemplate = $this->localiseUrl(self::URL_DEAL_DETAILS, $profile->getAccountAmazon()->getRegion());
        $now = new \DateTimeImmutable();
        $reportDate = $now->format('Y-m-d H:i:s');
        do {
            $fetchResponses = $this->openDealsPageAndGetFetchResponses($profile, $page);
            if (empty($fetchResponses)) {
                $this->logger->error("No FETCH response... abandoning run");
                return;
            }

            $deals = $this->extractDealsFromFetchMetadataResponse($fetchResponses, $page);
            if (empty($deals)) {
                $this->client->takeScreenshot('100 no-deals.jpg');
                $this->logger->error("No deals found... abandoning run");
                return;
            }
            $deals = $deals['viewModel']['searchCampaignsQueryResult'];

            $campaignIds = [];
            foreach ($deals['searchCampaigns'] as $dealData) {
                $campaignIds[] = $dealData['campaignReference']['id'];
            }
            $this->logger->info("Found " . count($campaignIds) . " deals on page $page");

            if ($fullRefresh) {
                $recentlyCrawledDealRequests = [];
            } else {
                $recentlyCrawledDealRequests = $this->getApiRequestRepository()->retrieveRecentlyCrawledRequestsByFilterAndRequestType($campaignIds, ApiRequestCrawlAsc::REQUEST_TYPE_DEALS);
            }

            $stopBecauseWeHaveAlreadyCrawledThisFar = false;
            foreach ($deals['searchCampaigns'] as $dealData) {
                if (!isset($dealData['campaignReference']['id'])) {
                    $this->logger->error("No campaign id found for deal in data: " . json_encode($dealData));
                    continue;
                }

                $campaignId = $dealData['campaignReference']['id'];

                $currentDateTime = new \DateTime();
                $dealEndDate = GravitiqTools::castToDateTimeImmutable($dealData['endDate']);
                $dealEndDateString = $dealEndDate?->format('c') ?? '(no date)';
                $this->logger->info("Deal $campaignId ends on $dealEndDateString");
                if (!is_null($dealEndDate) && ($dealEndDate < $currentDateTime) && $dealData['campaignStatus'] === 'DRAFT') {
                    $this->logger->error("Deal $campaignId is a draft and ended in the past, setting request to fail");
                    $this->createAndPersistFailedRequest($profile, $countryCode, $reportDate, $campaignId, ApiRequestCrawlAsc::REQUEST_TYPE_DEALS);
                    continue;
                }

                if (isset($recentlyCrawledDealRequests[$campaignId])) {
                    $lastRequestDate = GravitiqTools::castToDateTime($recentlyCrawledDealRequests[$campaignId]['lastRequestDate']);
                    $hoursSinceLastRequest = GravitiqTools::hoursBetweenDates($lastRequestDate, null);
                    if ($hoursSinceLastRequest <= $minHoursBetweenRequests) {
                        $this->logger->info("Skipping deal that was crawled {$hoursSinceLastRequest} hours ago (must be more than {$minHoursBetweenRequests}).");
                        continue;
                    }

                    if (!is_null($dealEndDate)) {
                        $dealReportCompleteDate = $dealEndDate->add(new \DateInterval('P' . self::NUM_DAYS_TO_FILL_IN_DEAL_REPORTING_DATA . 'D'));
                        if ($dealReportCompleteDate < $lastRequestDate) {
                            $this->logger->info("Skipping deal that ended before last request date (#$campaignId {$dealReportCompleteDate->format('Y-m-d')} < {$lastRequestDate->format('Y-m-d')})");
                            if ($onlyGetNewDeals && !$stopBecauseWeHaveAlreadyCrawledThisFar) {
                                $stopBecauseWeHaveAlreadyCrawledThisFar = true;
                                $this->logger->info("Only getting new deals... so will end run at the end of this page");
                            }
                            continue;
                        }
                    }
                }

                $this->logger->info("Crawling data for deal: $campaignId");

                $params = [
                    '%campaign_id%' => $campaignId
                ];
                $url = strtr($urlTemplate, $params);
                $dealDetailsUrl = strtr(self::FETCH_PATH_DEAL_DETAILS, $params);
                $this->logger->info("Opening deals detail page: $url");

                $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(null, $dealDetailsUrl));
                $fetchResponses = $this->getStashedFetchResponses();
                $dealDetailData = $this->extractDealDetailsFromFetchMetadataResponse($fetchResponses, $campaignId);

                if (isset($dealDetailData['error']) && $dealDetailData['error'] === self::SERVICE_UNAVAILABLE) {
                    $this->handleServiceUnavailable($profile);

                    $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(null, $dealDetailsUrl));
                    sleep(2);
                    $fetchResponses = $this->getStashedFetchResponses();
                    $dealDetailData = $this->extractDealDetailsFromFetchMetadataResponse($fetchResponses, $campaignId);
                }

                if (empty($dealDetailData)) {
                    $this->client->takeScreenshot('60 waiting for fetch deal details.jpg');
                    $this->sleepForSeconds(10);
                    $fetchResponses = $this->getStashedFetchResponses();
                    $dealDetailData = $this->extractDealDetailsFromFetchMetadataResponse($fetchResponses, $campaignId);
                }

                if (empty($dealDetailData)) {
                    $this->logger->warning("Failed to get deal detail data... trying to load deals homepage then coming back in 5s...");
                    $urlTemp = $this->localiseUrl(self::URL_DEALS_START, $profile->getAccountAmazon()->getRegion());
                    $urlTemp = str_replace(['%page%'], [$page], $urlTemp);
                    $this->getNewUrl($urlTemp, 5);
                    $this->client->takeScreenshot('65 deals homepage.jpg');

                    $signOutLinkSelector = '.cvf-account-switcher-sign-out-link';
                    if ($this->crawler->filter($signOutLinkSelector)->count() > 0) {
                        $this->logger->info("Clicking sign out link...");
                        $this->clickElementAndWait($signOutLinkSelector);
                    }

                    $this->sleepForSeconds(40, 80);

                    $this->logger->info("Reloading URL of deal details...");
                    $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(null, $dealDetailsUrl));
                    $fetchResponses = $this->getStashedFetchResponses();
                    $dealDetailData = $this->extractDealDetailsFromFetchMetadataResponse($fetchResponses, $campaignId);
                    $this->client->takeScreenshot('66 deal detail page.jpg');
                }

                if (empty($dealDetailData)) {
                    $this->logoutWaitAndLogBackIn($profile);

                    $this->getNewUrl($url, $this->buildArrayOfDynamicRequestsToWaitFor(null, $dealDetailsUrl));
                    sleep(2);
                    $fetchResponses = $this->getStashedFetchResponses();
                    $dealDetailData = $this->extractDealDetailsFromFetchMetadataResponse($fetchResponses, $campaignId);
                }

                if (empty($dealDetailData)) {
                    $this->logger->error("No deal detail data found for $campaignId, setting request to fail");
                    $this->createAndPersistFailedRequest($profile, $countryCode, $reportDate, $campaignId, ApiRequestCrawlAsc::REQUEST_TYPE_DEALS);
                    continue;
                }

                $extractedData = array_merge($dealData, $dealDetailData);

                $crawlRequest = $this->createPersistAndFlushRequest($profile, $countryCode, $reportDate, $campaignId, ApiRequestCrawlAsc::REQUEST_TYPE_DEALS);
                if (!empty($crawlRequest)) {
                    $this->saveDataToFileLinkedToParsableEntity($extractedData, $crawlRequest);
                    $this->flushChanges();
                }
            }
            if ($stopBecauseWeHaveAlreadyCrawledThisFar) {
                $this->logger->info("Stopping because we've already crawled this far");
                break;
            }
            ++$page;
            $dealsSeen += 50;
        } while ($dealsSeen < $deals['totalCount']);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param int $page
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function openDealsPageAndGetFetchResponses(AccountProfileSpa $profile, int $page = 1): array
    {
        $this->logger->info("Opening deals page $page");
        $url = $this->localiseUrl(self::URL_DEALS_START, $profile->getAccountAmazon()->getRegion());
        $url = str_replace(['%page%'], [$page], $url);
        $this->logger->info("URL is $url");
        $waitTime = 5 + (2 * $this->nestedCallDepth);
        $this->getNewUrl($url, $waitTime);
        $fetchResponses = $this->getStashedFetchResponses();

        if (empty($fetchResponses)) {
            if (3 > $this->nestedCallDepth) {
                $this->nestedCallDepth++;
                $this->logger->info("No FETCH responses found... forcing reload #{$this->nestedCallDepth}");
                $fetchResponses = $this->openDealsPageAndGetFetchResponses($profile, $page);
                $this->nestedCallDepth--;
            } else {
                $this->logger->error("No FETCH responses found... and cannot retry any more");
            }
        }

        return $fetchResponses;
    }

    /**
     * @param list<array<string, mixed>> $fetchResponses
     * @param int $page
     * @return array<string, mixed>
     */
    protected function extractDealsFromFetchMetadataResponse(array $fetchResponses, int $page): array
    {
        $dealPath = str_replace(['%page%'], [$page], self::FETCH_PATH_DEALS);
        $jsonResponse = $this->getJsonResponse($fetchResponses, $dealPath, false);
        if ($jsonResponse === null) {
            $this->client->takeScreenshot('901 no deal data.jpg');
            $this->logger->error("No deal data ($dealPath) found in fetchResponses: " . json_encode($fetchResponses));
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractDealsFromFetchMetadataResponse");
            return [];
        }
        return $dataArray;
    }

    /**
     * @param list<array<string, mixed>> $fetchResponses
     * @param string $campaignId
     * @return array<string, mixed>
     */
    protected function extractDealDetailsFromFetchMetadataResponse(array $fetchResponses, string $campaignId): array
    {
        $jsonResponse = $this->getJsonResponse($fetchResponses, str_replace(['%campaign_id%'], [$campaignId], self::FETCH_PATH_DEAL_DETAILS), false);
        if ($jsonResponse === null) {
            return [];
        }

        if (str_contains($jsonResponse, 'Service Unavailable')) {
            return [
                'error' => self::SERVICE_UNAVAILABLE
            ];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractDealDetailsFromFetchMetadataResponse");
            return [];
        }

        if (empty($dataArray['viewModel'])) {
            $this->logger->error("No viewModel found in JSON response in extractDealDetailsFromFetchMetadataResponse: $jsonResponse");
            return [];
        }

        $dataArray = $dataArray['viewModel'];
        unset($dataArray['imageUrl'], $dataArray['internalDescription']);

        return $dataArray;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param ?int $minHoursBetweenRequests
     * @param list<DataSpaBrowseTreeNode> $browseNodes
     * @return void
     * @throws \Exception
     */
    protected function scrapePoeNiches(AccountProfileSpa $profile, ?int $minHoursBetweenRequests, array $browseNodes): void
    {
        $urlPoeDashboard = $this->localiseUrl(self::URL_POE_DASHBOARD);

        $reportDate = new \DateTimeImmutable();
        $urlTemplateNicheProducts = $this->localiseUrl(self::URL_POE_NICHE_PRODUCTS);
        foreach ($browseNodes as $browseNode) {
            $nicheResponses = $this->scrapeNichesForNode($profile, $reportDate, $browseNode);
            if (empty($nicheResponses)) {
                continue;
            }

            $nicheIds = [];
            foreach ($nicheResponses as $nicheData) {
                $nicheIds[] = $nicheData['nicheId'];
            }
            $recentlyCrawledNicheRequests = $this->getApiRequestRepository()->retrieveRecentlyCrawledRequestsByFilterAndRequestType($nicheIds, ApiRequestCrawlAsc::REQUEST_TYPE_NICHE, $minHoursBetweenRequests);

            foreach ($nicheResponses as $nicheData) {
                $nicheId = $nicheData['nicheId'];

                if (isset($recentlyCrawledNicheRequests[$nicheId])) {
                    $this->logger->info("Skipping niche $nicheId that was crawled recently");
                    continue;
                }

                $this->logger->info("Crawling data for niche: $nicheId");

                $params = [
                    '%niche_id%' => $nicheId
                ];
                $urlNicheProducts = strtr($urlTemplateNicheProducts, $params);
                $this->logger->info("Opening niche products page: $urlNicheProducts");

                $this->getNewUrl($urlNicheProducts, 5);
                $fetchResponses = $this->getStashedFetchResponses(false, true);
                $nicheItemsData = $this->extractNicheProductsFromFetchMetadataResponse($fetchResponses);

                if (empty($nicheItemsData)) {
                    $this->client->takeScreenshot('70 waiting for fetch niche products.jpg');
                    $this->sleepForSeconds(10);
                    $fetchResponses = $this->getStashedFetchResponses(false, true);
                    $nicheItemsData = $this->extractNicheProductsFromFetchMetadataResponse($fetchResponses);
                }

                if (empty($nicheItemsData)) {
                    $this->logger->warning("Failed to get niche products data... trying to load opportunity explorer homepage then coming back in 5s...");
                    $this->getNewUrl($urlPoeDashboard, 5);
                    $this->client->takeScreenshot('75 opportunity explorer homepage.jpg');

                    $signOutLinkSelector = '.cvf-account-switcher-sign-out-link';
                    if ($this->crawler->filter($signOutLinkSelector)->count() > 0) {
                        $this->logger->info("Clicking sign out link...");
                        $this->clickElementAndWait($signOutLinkSelector);
                    }

                    $this->sleepForSeconds(40, 80);

                    $this->logger->info("Reloading URL of niche product...");
                    $this->getNewUrl($urlNicheProducts, 5);
                    $fetchResponses = $this->getStashedFetchResponses(false, true);
                    $nicheItemsData = $this->extractNicheProductsFromFetchMetadataResponse($fetchResponses);
                    $this->client->takeScreenshot('76 niche products page.jpg');
                }

                if (empty($nicheItemsData)) {
                    $this->logoutWaitAndLogBackIn($profile);
                    $this->getNewUrl($urlNicheProducts, 5);
                    $fetchResponses = $this->getStashedFetchResponses(false, true);
                    $nicheItemsData = $this->extractNicheProductsFromFetchMetadataResponse($fetchResponses);
                }

                if (empty($nicheItemsData)) {
                    $this->logger->error("No niche products data found for niche $nicheId, setting request to fail");
                    $this->createAndPersistFailedRequest($profile, $browseNode->getCountryCode(), $reportDate, $nicheId, ApiRequestCrawlAsc::REQUEST_TYPE_NICHE);
                    continue;
                }

                $nicheItemsData = $nicheItemsData['data']['niche'];
                $nicheData['asinMetrics'] = $nicheItemsData['asinMetrics'];
                $nicheData['launchPotential'] = $nicheItemsData['launchPotential'];
                $nicheData['nichePdr'] = $nicheItemsData['nichePdr'];
                $nicheData['searchTermMetrics'] = $nicheItemsData['searchTermMetrics'];
                $nicheData['trendsMetrics'] = $nicheItemsData['trendsMetrics'];
                $nicheData['lastUpdatedTime'] = $nicheItemsData['lastUpdatedTimeISO8601'];

                $crawlRequest = $this->createPersistAndFlushRequest($profile, $browseNode->getCountryCode(), $reportDate, $nicheId, ApiRequestCrawlAsc::REQUEST_TYPE_NICHE);
                if (!empty($crawlRequest)) {
                    $this->saveDataToFileLinkedToParsableEntity($nicheData, $crawlRequest);
                    $this->flushChanges();
                }
                $this->rebootClientIfMemoryUsageIsHigh($profile);
            }
        }
    }

    /**
     * @param ?int $minHoursBetweenRequests
     * @param int|string|list<int|string> $rootNodeIds
     * @param int|string|list<int|string> $branchNodeIds
     * @return list<DataSpaBrowseTreeNode>
     */
    private function retrieveListOfStaleBrowseNodesToCrawl(?int $minHoursBetweenRequests, int|string|array $rootNodeIds, int|string|array $branchNodeIds = []): array
    {
        // @todo CRITICAL - cannot scrape non-US POEs... needs an extra call to enable it
        // in the short term, countryCode is always forced to US:
        $allowedCountryCode = 'US';

        /** @var DataSpaBrowseTreeNodeRepository $repo */
        $repo = $this->getRepository(DataSpaBrowseTreeNode::class);

        $browseNodes = $repo->retrieveByRootAndBranchNodeIds($rootNodeIds, $branchNodeIds);
        if (empty($browseNodes)) {
            $this->logger->error("No browse nodes found from db... abandoning run");
            return [];
        } else {
            $nodeCount = count($browseNodes);
            $this->logger->info("Found $nodeCount browse nodes to check");
            $browseNodeBrowseNodeIds = [];
            foreach ($browseNodes as $key => $browseNode) {
                // @todo - should sort browseNodes into country groups, then process one country at a time, changing session to new country between each batch
                if ($browseNode->getCountryCode() !== $allowedCountryCode) {
                    $this->logger->info("Skipping browse node {$browseNode->getId()} that is not in $allowedCountryCode");
                    unset ($browseNodes[$key]);
                    continue;
                }
                $browseNodeBrowseNodeIds[] = $browseNode->getBrowseNodeId();
            }
        }

        if (empty($minHoursBetweenRequests)) {
            return $browseNodes;
        } else {
            $browseNodesToCrawl = [];
            $recentlyCrawledNodeRequests = $this->getApiRequestRepository()->retrieveRecentlyCrawledRequestsByFilterAndRequestType($browseNodeBrowseNodeIds, ApiRequestCrawlAsc::REQUEST_TYPE_POE_NODE, $minHoursBetweenRequests);
            foreach ($browseNodes as $browseNode) {
                if (isset($recentlyCrawledNodeRequests[$browseNode->getBrowseNodeId()])) {
                    $this->logger->info("Skipping browse node {$browseNode->getId()} that was recently crawled");
                    continue;
                }
                $browseNodesToCrawl[] = $browseNode;
            }
            return $browseNodesToCrawl;
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @param \DateTimeInterface $reportDate
     * @param DataSpaBrowseTreeNode $browseNode
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function scrapeNichesForNode(AccountProfileSpa $profile, \DateTimeInterface $reportDate, DataSpaBrowseTreeNode $browseNode): array
    {
        /** @var DataCrawlAscPoeNodeNicheRepository $nicheNodeRepo */
        $nicheNodeRepo = $this->getRepository(DataCrawlAscPoeNodeNiche::class);

        $fetchResponses = $this->openNichePageAndGetFetchResponses($profile, $browseNode->getBrowseNodeId(), $browseNode->getBrowseNodeName());
        if (empty($fetchResponses)) {
            $this->logger->error("No FETCH response... abandoning run");
            return [];
        }

        $nicheResponses = $this->extractNichesFromFetchMetadataResponse($fetchResponses);
        $apiRequestNodeNiches = $this->retrieveOrCreateNodeNicheRequest($profile, $browseNode, $reportDate);
        if (empty($nicheResponses) || empty($nicheResponses['data']['niches'])) {
            $apiRequestNodeNiches->setStatusToTimedOut()->setParseDateToDoNotParse();
            $this->client->takeScreenshot('110 no-niches.jpg');
            $this->logger->error("No niches found... abandoning this node");
            return [];
        }

        $nicheResponses = $nicheResponses['data']['niches'];
        $this->saveDataToFileLinkedToParsableEntity($nicheResponses, $apiRequestNodeNiches);
        $apiRequestNodeNiches->setStatusToDone();

        // start inline parsing of nodeNiches response
        $nicheIds = [];
        foreach ($nicheResponses as $nicheResponse) {
            $nicheIds[] = $nicheResponse['nicheId'];
        }
        $existingNodeNiches = $nicheNodeRepo->retrieveByNodeAndNicheIds($browseNode, $nicheIds);

        foreach ($nicheIds as $azNicheId) {
            if (isset($existingNodeNiches[$azNicheId])) {
                $existingNodeNiches[$azNicheId]->markAsSeen();
            } else {
                $nodeNiche = new DataCrawlAscPoeNodeNiche();
                $nodeNiche
                    ->setNode($browseNode)
                    ->setAzNicheId($azNicheId)
                    ->markAsSeen()
                ;
                $this->persist($nodeNiche);
            }
        }
        $apiRequestNodeNiches->setRecordCount(count($nicheResponses));
        $apiRequestNodeNiches->setParseDateToNow();
        // end inline parsing of nodeNiches response

        $this->flushChanges();
        try {
            $this->em->clear(DataCrawlAscPoeNodeNiche::class);
        } catch (MappingException $e) {
            throw new \RuntimeException("Error clearing entity manager: " . $e->getMessage());
        }

        return $nicheResponses;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $browseNodeId
     * @param string $browseNodeName
     * @return array<string, mixed>
     * @throws \Exception
     */
    private function openNichePageAndGetFetchResponses(AccountProfileSpa $profile, string $browseNodeId, string $browseNodeName): array
    {
        $this->logger->info("Opening niche page for $browseNodeName");
        $url = $this->localiseUrl(self::URL_POE_NODE_NICHES, $profile->getAccountAmazon()->getRegion());
        $replacements = [
            '%browseNodeId%'   => $browseNodeId,
            '%browseNodeName%' => urlencode($browseNodeName),
        ];
        $url = str_replace(array_keys($replacements), array_values($replacements), $url);
        $this->logger->info("URL is $url");
        $waitTime = 5 + (2 * $this->nestedCallDepth);
        $this->getNewUrl($url, $waitTime);
        $fetchResponses = $this->getStashedFetchResponses(false, true);

        if (empty($fetchResponses)) {
            if (3 > $this->nestedCallDepth) {
                $this->nestedCallDepth++;
                $this->logger->info("No FETCH responses found... forcing reload #{$this->nestedCallDepth}");
                $fetchResponses = $this->openNichePageAndGetFetchResponses($profile, $browseNodeId, $browseNodeName);
                $this->nestedCallDepth--;
            } else {
                $this->logger->error("No FETCH responses found... and cannot retry any more");
            }
        }

        return $fetchResponses;
    }

    /**
     * @param array<string, mixed> $fetchResponses
     * @return array<string, mixed>
     */
    protected function extractNichesFromFetchMetadataResponse(array $fetchResponses): array
    {
        foreach ($fetchResponses as $response) {
            if (isset($response['options']['body']) && str_contains($response['options']['body'], 'getNiches')) {
                $dataArray = json_decode($response['response'], true);
                if (is_null($dataArray)) {
                    $this->logErrorInChunks($response, 80000, "Unable to decode JSON response in extractNichesFromFetchMetadataResponse");
                    return [];
                }
                return $dataArray;
            }
        }

        return [];
    }

    /**
     * @param list<array<string, mixed>> $fetchResponses
     * @return array<string, mixed>
     */
    protected function extractNicheProductsFromFetchMetadataResponse(array $fetchResponses): array
    {
        foreach ($fetchResponses as $response) {
            if (isset($response['options']['body']) && str_contains($response['options']['body'], 'getNicheWithPurchaseDrivers')) {
                $dataArray = json_decode($response['response'], true);
                if (is_null($dataArray)) {
                    $this->logErrorInChunks($response['response'], 80000, "Unable to decode JSON response in extractNicheItemsFromFetchMetadataResponse");
                    return [];
                }
                return $dataArray;
            }
        }

        return [];
    }

    /**
     * @param AccountProfileSpa $profile
     * @return void
     * @throws \Exception
     */
    protected function scrapeInventoryDashboard(AccountProfileSpa $profile): void
    {
        $reportDate = new \DateTimeImmutable();

        $ajaxResponses = $this->openInventoryDashboardAndGetAjaxResponses($profile);
        if (empty($ajaxResponses)) {
            $this->logger->error("No AJAX response... abandoning run");
            return;
        }

        $planInventoryData = $this->extractInventoryDashboardPlanInventoryFromAjaxResponse($ajaxResponses);
        if (empty($planInventoryData) || empty($planInventoryData['data']['planInventoryCard'])) {
            $this->client->takeScreenshot('210 no-plan-inventory.jpg');
            $this->logger->error("No plan inventory found... abandoning run");
            return;
        }

        $storageUtilizationData = $this->extractInventoryDashboardStorageUtilizationFromAjaxResponse($ajaxResponses);
        if (empty($storageUtilizationData) || empty($storageUtilizationData['data']['storageUtilizationCard'])) {
            $this->client->takeScreenshot('220 no-storage-utilization.jpg');
            $this->logger->error("No storage utilization found... abandoning run");
            return;
        }

        $capacityMonitorData = $this->initCapacityMonitorAndExtractJsonArray($profile);
        if (empty($capacityMonitorData)) {
            $this->client->takeScreenshot('230 no-capacity-monitors.jpg');
            $this->logger->error("No capacity monitors found... abandoning run");
            return;
        }

        $extractedData = [
            'planInventory'      => $planInventoryData['data']['planInventoryCard'],
            'storageUtilization' => $storageUtilizationData['data']['storageUtilizationCard'],
            'capacityMonitor'    => $capacityMonitorData
        ];

        $request = $this->createPersistAndFlushRequest($profile, $profile->getCountryCode(), $reportDate, $profile->getAlias(), ApiRequestCrawlAsc::REQUEST_TYPE_INVENTORY_DASHBOARD);
        if (!empty($request)) {
            $this->saveDataToFileLinkedToParsableEntity($extractedData, $request);
            $this->flushChanges();
        }
    }

    /**
     * @param AccountProfileSpa $profile
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function openInventoryDashboardAndGetAjaxResponses(AccountProfileSpa $profile): array
    {
        $region = $profile->getAccountAmazon()->getRegion();
        $this->logger->info("Opening inventory dashboard for {$profile->getAlias()} / $region");
        $url = $this->localiseUrl(self::URL_INVENTORY_DASHBOARD, $region);
        $this->logger->info("URL is $url");
        $waitTime = 2 + (2 * $this->nestedCallDepth);
        $this->getNewUrl($url, $waitTime);
        $this->client->takeScreenshot('201 inventory dashboard.jpg');
        $ajaxResponses = $this->getStashedAjaxResponsesV2();

        if (empty($ajaxResponses)) {
            if (3 > $this->nestedCallDepth) {
                $this->nestedCallDepth++;
                $this->logger->info("No AJAX responses found... forcing reload #{$this->nestedCallDepth}");
                $ajaxResponses = $this->openInventoryDashboardAndGetAjaxResponses($profile);
                $this->nestedCallDepth--;
            } else {
                $this->logger->error("No AJAX responses found... and cannot retry any more");
            }
        }

        return $ajaxResponses;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @return array<string, mixed>
     */
    public function extractInventoryDashboardPlanInventoryFromAjaxResponse(array $ajaxResponses): array
    {
        foreach ($ajaxResponses as $response) {
            if (!empty($response['response'])) {
                $dataArray = json_decode($response['response'], true);
                if (isset($dataArray['data']['planInventoryCard'])) {
                    return $dataArray;
                }
            }
        }

        $this->logErrorInChunks(print_r($ajaxResponses, true), 80000, "extractInventoryDashboardPlanInventoryFromAjaxResponse unable to find planInventoryCard in JSON responses");
        return [];
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @return array<string, mixed>
     */
    public function extractInventoryDashboardStorageUtilizationFromAjaxResponse(array $ajaxResponses): array
    {
        foreach ($ajaxResponses as $response) {
            if (!empty($response['response'])) {
                $dataArray = json_decode($response['response'], true);
                if (isset($dataArray['data']['storageUtilizationCard'])) {
                    return $dataArray;
                }
            }
        }

        $this->logErrorInChunks(print_r($ajaxResponses, true), 80000, "extractInventoryDashboardStorageUtilizationFromAjaxResponse unable to find storageUtilizationCard in JSON responses");
        return [];
    }

    /**
     * @param AccountProfileSpa $profile
     * @return list<array<string, mixed>>
     * @throws \Exception
     */
    private function initCapacityMonitorAndExtractJsonArray(AccountProfileSpa $profile): array
    {
        $region = $profile->getAccountAmazon()->getRegion();
        $capacityMonitorUrl = $this->localiseUrl(self::FETCH_PATH_CAPACITY_MONITOR, $region);
        $this->getNewUrl($capacityMonitorUrl);
        $content = $this->client->getWebDriver()->getPageSource();

        return $this->extractJsonArrayFromContent($content);
    }

    /**
     * @param AccountProfileSpa[] $profiles
     * @throws \Exception
     */
    public function manageDownloadOfAscNews(array $profiles, \DateTime $endDate, int $numDays): void
    {
        /** @var \DateTime $startDate */
        $startDate = GravitiqTools::getDateXDaysBefore($endDate, $numDays);
        $startDate->setTime(0, 0, 0);

        $this->setLoginTrackerToNotLoggedIn();
        $this->datePeriod = ApiRequestCrawlAsc::DATE_PERIOD_NONE;

        foreach ($profiles as $profile) {
            $this->logger->info("Doing scape of ASC news for $profile");
            if (!$this->handleLoginAndAccountSwitchWithCleanupOnException($profile)) {
                continue;
            }

            try {
                $this->scrapeAscNews($profile, $startDate, $endDate);
            } catch (\Exception $e) {
                $this->logger->error($e->getTraceAsString());
                continue;
            }
        }

        $this->logOutAndTidyUpAtEndOfRun();
    }

    /**
     * @param AccountProfileSpa $profile
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @return void
     * @throws \Exception
     */
    protected function scrapeAscNews(AccountProfileSpa $profile, \DateTime $startDate, \DateTime $endDate): void
    {
        $ascNewsData = $this->fetchAscNewsListFromPageSource(500, $profile->getAccountAmazon()->getRegion());
        $ascNewsWithinDateWindows = $this->filterNewsByDateRangeAndFetchNewOnes($ascNewsData, $startDate, $endDate, $profile);
        if (empty($ascNewsWithinDateWindows)) {
            $this->logger->warning("No new ASC news found within date window of {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
        }
    }

    /**
     * @return list<AscNewsResponseArrayShape>
     * @throws \Exception
     */
    protected function fetchAscNewsListFromPageSource(int $newsNum, string $region): array
    {
        $ascNewsFetchUrl = $this->localiseUrl(self::FETCH_PATH_ASC_NEWS, $region);
        $ascNewsFetchUrl = str_replace('%num%', (string)$newsNum, $ascNewsFetchUrl);
        $this->logger->info("Fetching ASC news from url: $ascNewsFetchUrl");
        $this->getNewUrl($ascNewsFetchUrl, 5);
        $ascNewsPageSource = $this->client->getWebDriver()->getPageSource();
        $ascNewsData = $this->extractJsonArrayFromContent($ascNewsPageSource);

        if (empty($ascNewsData['publicArticles'])) {
            throw new \Exception("No ASC news found in page source");
        }

        return $ascNewsData['publicArticles'];
    }

    /**
     * @param list<AscNewsResponseArrayShape> $ascNewsData
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @param AccountProfileSpa $profileSpa
     * @return list<AscNewsResponseArrayShape>
     * @throws \Exception
     */
    protected function filterNewsByDateRangeAndFetchNewOnes(array $ascNewsData, \DateTime $startDate, \DateTime $endDate, AccountProfileSpa $profileSpa): array
    {
        $this->logger->info("Filtering ASC news within date window of {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

        /** @var AscNewsRepository $ascNewsRepo */
        $ascNewsRepo = $this->em->getRepository(AscNews::class);
        $existingAscNewsPublicIdsForCountry = $ascNewsRepo->retrievePublicIdsByCountryAndDateRange($startDate, $endDate, $profileSpa->getCountryCode());

        $ascNewsWithinDateWindows = [];
        foreach ($ascNewsData as $ascNews) {
            if ($this->isDateWithinRange((string)$ascNews['startDate'], $startDate, $endDate)) {
                if (in_array($ascNews['publicId'], $existingAscNewsPublicIdsForCountry)) {
                    $this->logger->info("Skipping ASC news {$ascNews['publicId']} that was already crawled");
                    continue;
                }

                $newsContent = $this->fetchNewsContentFromPageSource($ascNews['publicId'], $profileSpa->getAccountAmazon()->getRegion());
                $ascNews['content'] = $newsContent['content'];
                $ascNewsWithinDateWindows[] = $ascNews;

                $crawlRequest = $this->createPersistAndFlushRequest($profileSpa, $profileSpa->getCountryCode(), new \DateTime(), $ascNews['publicId'], ApiRequestCrawlAsc::REQUEST_TYPE_ASC_NEWS);
                $this->saveDataToFileLinkedToParsableEntity($ascNews, $crawlRequest);
            }
        }

        return $ascNewsWithinDateWindows;
    }

    /**
     * @param string $publicId
     * @param string $region
     * @return array<string, mixed>
     * @throws \Exception
     */
    protected function fetchNewsContentFromPageSource(string $publicId, string $region): array
    {
        $newsUrl = $this->localiseUrl(self::FETCH_PATH_ASC_NEWS_CONTENT, $region);
        $newsUrl = str_replace('%id%', $publicId, $newsUrl);
        $this->logger->info("Fetching news content from url: $newsUrl");
        $this->getNewUrl($newsUrl, 5);
        $newsPageSource = $this->client->getWebDriver()->getPageSource();
        $newsContent = $this->extractJsonArrayFromContent($newsPageSource);

        if (empty($newsContent)) {
            throw new \Exception("No news content found in page source");
        }

        return $newsContent;
    }

    /**
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @param list<int>|null $newsId
     * @param bool $announce
     * @return void
     */
    public function managePublishOfAscNewsToSlack(\DateTime $startDate, \DateTime $endDate, ?array $newsId = null, bool $announce = true): void
    {
        $this->logger->info('Starting Amazon Seller Central news management and publishing to Slack');

        /** @var AscNewsRepository $newsRepo */
        $newsRepo = $this->getRepository(AscNews::class);

        if (!is_null($newsId)) {
            $ascNewsData = $newsRepo->retrieveByIds($newsId);
        } else {
            $ascNewsData = $newsRepo->retrieveNewsNotPublishedToSlackByDateRange($startDate, $endDate);
        }

        if (empty($ascNewsData)) {
            $this->logger->info("No Amazon Seller Central news available for Slack publication");
            return;
        }

        foreach ($ascNewsData as $ascNews) {
            $baseUrl = $this->localiseUrl('https://%ROOT_URL%/', $ascNews->getSpaProfile()->getAccountAmazon()->getRegion());
            $this->amazonScNewsManager->publishAscNewsToSlack($ascNews, $baseUrl, $announce);
        }
    }

    /**
     * @param array<string, mixed>|list<array<string, mixed>> $fetchResponses
     * @return array<string, mixed>
     */
    protected function extractDataFromFetchMetadataResponseByUrl(array $fetchResponses, string $url): array
    {
        foreach ($fetchResponses as $response) {
            if (is_string($response['url']) && str_contains($response['url'], $url)) {
                $dataArray = json_decode($response['response'], true);
                if (is_null($dataArray)) {
                    $this->logErrorInChunks($response, 80000, "Unable to decode JSON response in extractDataFromFetchMetadataResponseByUrl for $url");
                    return [];
                }
                return $dataArray;
            }
        }

        return [];
    }

    /**
     * @param string $content
     * @return list<array<string, mixed>> | array{publicArticles: list<array<string, mixed>>} | array<string, mixed>
     * @throws \Exception
     */
    protected function extractJsonArrayFromContent(string $content): array {
        $jsonData = [];
        preg_match('/<pre>(.*?)<\/pre>/', $content, $matches);

        if (isset($matches[1])) {
            $jsonString = $matches[1];

            $jsonData = json_decode($jsonString, true);
        } elseif (str_starts_with($content, '{') || str_starts_with($content, '[')) {
            $jsonData = json_decode($content, true);
        } else {
            $this->logger->error("Response does not look like JSON, starts with: " . substr($content, 0, 100));
        }

        if ($jsonData === null) {
            $this->logger->error("Error decoding JSON");
            return [];
        }

        return $jsonData;
    }

    /**
     * Checks if given date is within the range, but strictly LESS than the end date.
     * @param string|\DateTimeInterface $dateToCheck
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @return bool
     */
    protected function isDateWithinRange(string|\DateTimeInterface $dateToCheck, \DateTimeInterface $startDate, \DateTimeInterface $endDate): bool
    {
        try {
            $reportDate = GravitiqTools::castToDateTime($dateToCheck);
        } catch (\Exception) {
            $reportDate = null;
        }
        if (is_null($reportDate)) {
            $this->logger->error("Could not parse reportDate string: $dateToCheck");
            return false;
        }

        return ($reportDate >= $startDate && $reportDate < $endDate);
    }

    protected function createPersistAndFlushRequest(
        AccountProfileSpa $profile,
        string $countryCode,
        \DateTimeInterface|string $reportDate,
        string $filter,
        string $requestType,
        bool $isEmpty = false
    ): ?ApiRequestCrawlAsc
    {
        $crawlRequest = $this->createAndPersistApiRequest($profile, $countryCode, $reportDate, $filter, $requestType, $isEmpty);
        if (is_null($crawlRequest)) {
            return null;
        }

        return $this->flushAndRefreshObject($crawlRequest);
    }
    protected function createAndPersistApiRequest(
        AccountProfileSpa $profile,
        string $countryCode,
        \DateTimeInterface|string $reportDate,
        string $filter,
        string $requestType,
        bool $isEmpty = false
    ): ?ApiRequestCrawlAsc
    {
        $crawlRequest = $this->createAndPersistInProgressApiRequest($requestType, $profile, $countryCode, $reportDate, null, $filter);
        if (is_null($crawlRequest)) {
            return null;
        }

        $crawlRequest->setStatusToDone();
        if ($isEmpty) {
            $crawlRequest->setRecordCount(0);
            $crawlRequest->setSavedFileSize(0);
            $crawlRequest->setParseDate(AmazonSpaBridge::getDoNotParseDateMarker());
        }

        return $crawlRequest;
    }
    protected function createAndPersistInProgressApiRequest(
        string $requestType,
        AccountProfileSpa $profile,
        string $countryCode,
        \DateTimeInterface|string $startDate,
        \DateTimeInterface|string|null $endDate = null,
        string $filter = ''
    ): ?ApiRequestCrawlAsc
    {
        $crawlRequest = new ApiRequestCrawlAsc();
        $crawlRequest
            ->setRequestType($requestType)
            ->setAccountProfile($profile)
            ->setCountry($countryCode)
            ->setReportDate(GravitiqTools::castToDateTime($startDate))
            ->setFilter($filter)
            ->setRequestDate(new \DateTimeImmutable())
            ->setDatePeriod($this->datePeriod)
            ->setStatusToInProgress()
        ;

        if ($endDate) {
            $crawlRequest->setReportEndDate(GravitiqTools::castToDateTime($endDate));
        }

        try {
            $this->persist($crawlRequest);
        } catch (\Exception $e) {
            $this->logger->error("Could not persist ApiRequestCrawlAsc: " . $e->getMessage());
            return null;
        }

        return $crawlRequest;
    }

    protected function createAndPersistTimedOutRequest(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface|string $reportDate, string $filter, string $query, string $requestType): ?ApiRequestCrawlAsc
    {
        $crawlRequest = $this->createAndPersistApiRequest($profile, $countryCode, $reportDate, $filter, $requestType, true);
        $crawlRequest
            ->setQuery($query)
            ->setStatusToTimedOut()
            ->setParseDate(null)
        ;

        try {
            $this->flushChanges();
            $this->refresh($crawlRequest);
        } catch (\Exception $e) {
            $this->logger->error("Could not update ApiRequestCrawlAsc: " . $e->getMessage());
            return null;
        }

        return $crawlRequest;
    }

    protected function createAndPersistFailedRequest(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface|string $reportDate, string $filter, string $requestType): ?ApiRequestCrawlAsc
    {
        $crawlRequest = $this->createAndPersistApiRequest($profile, $countryCode, $reportDate, $filter, $requestType, true);
        $crawlRequest->setStatusToFatal();
        $crawlRequest->setParseDate(null);

        try {
            $this->flushChanges();
            $this->refresh($crawlRequest);
        } catch (\Exception $e) {
            $this->logger->error("Could not update ApiRequestCrawlAsc: " . $e->getMessage());
            return null;
        }

        return $crawlRequest;
    }

    /**
     * Manages timed out queries, creating new timed out ApiRequestCrawlAsc records for them if they are not already stored.
     *
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param \DateTimeInterface|string $reportDate
     * @param string $filter
     * @param list<string> $queries
     * @return void
     * @throws \Exception
     */
    protected function processNewTimedOutSqdQueries(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface|string $reportDate, string $filter, array $queries): void
    {
        $reportDate = GravitiqTools::castToDateTime($reportDate);
        $existingTimeouts = $this->getApiRequestRepository()->findMatchingTimedOutOrDoneRequests($countryCode, $reportDate, $filter, $queries, ApiRequestCrawlAsc::REQUEST_TYPE_SQD_ASIN);

        $queriesAlreadyDone = [];
        foreach ($queries as $query) {
            // if query exists in existingTimeouts then skip it (only create new timeouts if they don't already exist)
            foreach ($existingTimeouts as $existingTimeout) {
                if ($existingTimeout->getQuery() === $query) {
                    continue 2;
                }
            }
            if (in_array($query, $queriesAlreadyDone)) {
                continue;
            }

            $this->createAndPersistTimedOutRequest($profile, $countryCode, $reportDate, $filter, $query, ApiRequestCrawlAsc::REQUEST_TYPE_SQD_ASIN);
            $queriesAlreadyDone[] = $query;
        }
    }

    protected function logOutFromSellerCentral(): void
    {
        $this->logger->info('Logging out from ASC');
        $url = $this->localiseUrl(self::URL_LOGOUT);
        try {
            $this->getNewUrl($url);
        } catch (WebDriverException $e) {
            $this->logger->error("Error logging out (going to continue anyway): " . $e->getMessage());
        }
        $this->client->takeScreenshot('99 post-logout.jpg');
        $this->baseUrl = '';
        $this->logger->info('Logged out from ASC');
        $this->setLoginTrackerToNotLoggedIn();
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @return list<string>
     */
    protected function extractAsinsFromAjaxMetadataResponse(array $ajaxResponses): array
    {
        $asins = [];

        $jsonResponse = $this->getJsonResponse($ajaxResponses, self::AJAX_PATH_SQP_METADATA);
        if ($jsonResponse === null) {
            return $asins;
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractAsinsFromAjaxMetadataResponse:");
            return $asins;
        }

        foreach ($dataArray['metadata']['viewsRoot']['views'] as $view) {
            if ($view['id'] === 'query-performance-asin-view') {
                $this->logger->info("Using view {$view['id']}");
                foreach ($view['filters'] as $filter) {
                    if ($filter['id'] === 'asin') {
                        $values = $filter['values'] ?? null;
                        if (!$values) {
                            $this->logger->error('No ASIN values found in metadata asin response');
                            $this->logger->error(print_r($filter, true));
                            continue;
                        }

                        foreach ($filter['values'] as $value) {
                            $asins[] = $value['value'];
                        }
                    }
                }
                break;
            } else {
                $this->logger->info("Skipping view {$view['id']} (looking for ASINs)");
            }
        }

        return $asins;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @return list<string>
     */
    protected function retrieveAsinsFromListingData(AccountProfileSpa $profile, string $countryCode): array
    {
        /** @var DataSpaListListingRepository $listingRepo */
        $listingRepo = $this->getRepository(DataSpaListListing::class);
        return $listingRepo->getDistinctAsinsForAccount($profile, $countryCode);
    }

    /**
     * @param array<string> $asins
     * @param string $countryCode
     * @return array<string, array{asin: string, createDate: string}>   indexed by ASIN
     */
    protected function retrieveFirstCreateDatePerAsinInCountry(array $asins, string $countryCode): array
    {
        /** @var DataSpaListListingRepository $listingRepo */
        $listingRepo = $this->getRepository(DataSpaListListing::class);
        return $listingRepo->retrieveFirstCreateDatePerAsinInCountry($asins, $countryCode);
    }

    /**
     * @param array<string> $asins
     * @param string $countryCode
     * @return array<string, array{filter: string, firstReportDate: string}>
     */
    protected function retrieveFirstReportDatePerAsinInCountry(array $asins, string $countryCode): array
    {
        /** @var ApiRequestCrawlAscRepository $repo */
        $repo = $this->getRepository(ApiRequestCrawlAsc::class);
        return $repo->retrieveFirstReportDatePerAsinInCountry($asins, $countryCode);
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @return array<string, string|int>
     */
    protected function extractBrandsFromAjaxMetadataResponse(array $ajaxResponses): array
    {
        $brands = [];

        $jsonResponse = $this->getJsonResponse($ajaxResponses, self::AJAX_PATH_SQP_METADATA);
        if ($jsonResponse === null) {
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractBrandsFromAjaxMetadataResponse");
            return [];
        }

        foreach ($dataArray['metadata']['viewsRoot']['views'] as $view) {
            if ($view['id'] === 'query-performance-brands-view') {
                $this->logger->info("Using view {$view['id']}");
                foreach ($view['filters'] as $filter) {
                    if ($filter['id'] === 'brand') {
                        if (empty($filter['values'])) {
                            throw new \InvalidArgumentException('No Brands found');
                        }

                        foreach ($filter['values'] as $brandArray) {
                            $brands[$brandArray['localizedDisplayValue']] = $brandArray['value'];
                        }
                        break;
                    }
                }
                break;
            } else {
                $this->logger->info("Skipping view {$view['id']} (looking for brands)");
            }
        }

        return $brands;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $experimentId
     * @return list<array<string, mixed>>
     */
    protected function extractExperimentResultsFromAjaxMetadataResponse(array $ajaxResponses, string $experimentId): array
    {
        $results = [];

        $jsonResponse = $this->getJsonResponse($ajaxResponses, str_replace(['%experiment_id%'], [$experimentId], self::AJAX_PATH_EXPERIMENT_RESULTS), true, true);
        if ($jsonResponse === null) {
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractExperimentResultsFromAjaxMetadataResponse");
            return [];
        }

        foreach ($dataArray as $data) {
            $results[] = $data;
        }

        return $results;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @return array<string, list<string>>
     */
    protected function extractReportDatesFromAjaxMetadataResponse(array $ajaxResponses): array
    {
        $reportDates = [
            'brands' => [],
            'asins' => [],
        ];
        $jsonResponse = $this->getJsonResponse($ajaxResponses, self::AJAX_PATH_SQP_METADATA);
        if ($jsonResponse === null) {
            return $reportDates;
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractReportDatesFromAjaxMetadataResponse");
            return $reportDates;
        }

        foreach ($dataArray['metadata']['viewsRoot']['views'] as $view) {
            if ($view['id'] === 'query-performance-asin-view') {
                $reportDates['asins'] = $this->extractDatesFromMetaDataFilterArray($view['filters']);
            } elseif ($view['id'] === 'query-performance-brands-view') {
                $reportDates['brands'] = $this->extractDatesFromMetaDataFilterArray($view['filters']);
            } else {
                $this->logger->info("Skipping view {$view['id']} (looking for brands or asins)");
            }
        }

        return $reportDates;
    }

    /**
     * @param list<array<string, mixed>> $filterArray
     * @return list<string>
     */
    private function extractDatesFromMetaDataFilterArray(array $filterArray): array
    {
        if (ApiRequestCrawlAsc::DATE_PERIOD_WEEKLY === $this->datePeriod) {
            $expectedFilterValue = 'weekly';
            $expectedFilterChildId = 'weekly-week';
        } else {
            throw new \InvalidArgumentException("Unexpected date period ({$this->datePeriod})");
        }

        $reportDates = [];
        foreach ($filterArray as $filter) {
            if ($filter['id'] === 'reporting-range') {
                if (empty($filter['values'])) {
                    throw new \InvalidArgumentException('No Reporting Range found');
                }

                foreach ($filter['values'] as $groupValue) {
                    if ($groupValue['value'] === $expectedFilterValue) {
                        if ($groupValue['child']['id'] !== $expectedFilterChildId) {
                            throw new \InvalidArgumentException("Unexpected filter child id ({$groupValue['child']['id']}), when expected {$expectedFilterChildId}");
                        }
                        foreach ($groupValue['child']['values'] as $value) {
                            $reportDates[] = $value['value'];
                        }
                        break;
                    }
                }
            }
        }
        return $reportDates;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $responsePath
     * @param string $reportId
     * @param string $reportDate
     * @return array{meta?: array<string, string|int>, rows?: string|int}
     */
    protected function extractTableDataFromAjaxReportsResponse(array $ajaxResponses, string $responsePath, string $reportId, string $reportDate): array
    {
        $jsonResponse = $this->getJsonResponse($ajaxResponses, $responsePath);
        if ($jsonResponse === null) {
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logErrorInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractTableDataFromAjaxReportsResponse");
            return [];
        }

        $tableData = [];
        foreach ($dataArray['reportsV2'] as $report) {
            if ($report['id'] !== $reportId) {
                continue;
            }

            if (1 > $report['totalItems']) {
                $this->logger->debug('No queries found');
                if (empty($tableData['meta'])) {
                    $tableData['meta'] = [
                        'status' => $dataArray['status'],
                        'partial' => $dataArray['partial'],
                        'country' => $dataArray['country'],
                        'totalItems' => $report['totalItems'],
                        'pageNumber' => $report['pageNumber'],
                        'pageSize' => $report['pageSize'],
                        'reportDate' => $reportDate,
                        'datePeriod' => $this->datePeriod,
                    ];
                }
                continue;
            }

            $tableData = [
                'rows' => $report['rows'],
                'meta' => [
                    'status' => $dataArray['status'],
                    'partial' => $dataArray['partial'],
                    'country' => $dataArray['country'],
                    'totalItems' => $report['totalItems'],
                    'pageNumber' => $report['pageNumber'],
                    'pageSize' => $report['pageSize'],
                    'reportDate' => $reportDate,
                    'datePeriod' => $this->datePeriod,
                ],
            ];
            break;
        }

        return $tableData;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $reportDate
     * @param string $asin
     * @return array{meta?: array<string, string|int>, rows?: mixed}
     */
    protected function extractAsinSqpDataFromAjaxReportsResponse(array $ajaxResponses, string $reportDate, string $asin): array
    {
        $dataArray = $this->extractTableDataFromAjaxReportsResponse($ajaxResponses, self::AJAX_PATH_SQP, 'query-performance-asin-report-table', $reportDate);
        if (isset($dataArray['meta'])) {
            $dataArray['meta']['asin'] = $asin;
        }
        return $dataArray;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $reportDate
     * @param string $brandName
     * @param string $brandId
     * @return array{meta?: array<string, string|int>, rows?: mixed}
     */
    protected function extractBrandSqpDataFromAjaxReportsResponse(array $ajaxResponses, string $reportDate, string $brandName, string $brandId): array
    {
        $dataArray = $this->extractTableDataFromAjaxReportsResponse($ajaxResponses, self::AJAX_PATH_SQP, 'query-performance-brand-report-table', $reportDate);
        if (isset($dataArray['meta'])) {
            $dataArray['meta']['brandName'] = $brandName;
            $dataArray['meta']['brandId'] = $brandId;
        }
        return $dataArray;
    }

    /**
     * @param list<array<string, mixed>> $ajaxResponses
     * @param string $reportDate
     * @param string $asin
     * @param string $query
     * @return array{meta?: array<string, string|int>, rows?: mixed}
     */
    protected function extractSQDDataFromAjaxReportsResponse(array $ajaxResponses, string $reportDate, string $asin, string $query): array
    {
        $jsonResponse = $this->getJsonResponse($ajaxResponses, self::AJAX_PATH_SQD);
        if ($jsonResponse === null) {
            return [];
        }

        $dataArray = json_decode($jsonResponse, true);
        if (is_null($dataArray)) {
            $this->logWarningInChunks($jsonResponse, 80000, "Unable to decode JSON response in extractSQDDataFromAjaxReportsResponse");
            $sqdData = [
                'rows' => [],
                'meta' => [
                    'status' => 'ERROR',
                    'reportDate' => $reportDate,
                    'datePeriod' => $this->datePeriod,
                    'asin' => $asin,
                    'query' => $query,
                ],
            ];

            return $sqdData;
        }

        try {
            $this->validateReportData($dataArray);
        } catch (\InvalidArgumentException $e) {
            $this->logger->error($e->getMessage());
            return [];
        }

        foreach ($dataArray['reportsV2'] as $report) {
            if ($report['id'] === 'query-detail-asin-report-table') {
                $sqdData = [
                    'rows' => $report['rows'],
                    'meta' => [
                        'status' => $dataArray['status'],
                        'partial' => $dataArray['partial'],
                        'country' => $dataArray['country'],
                        'totalItems' => $report['totalItems'],
                        'pageNumber' => $report['pageNumber'],
                        'pageSize' => $report['pageSize'],
                        'reportDate' => $reportDate,
                        'datePeriod' => $this->datePeriod,
                        'asin' => $asin,
                        'query' => $query,
                    ],
                ];

                return $sqdData;
            }
        }

        $this->logger->error('No SQD data found... even after validation');
        return [];
    }

    /**
     * @param array<string, array<string, mixed>> $dataArray
     * @return void
     * @throws \InvalidArgumentException
     */
    private function validateReportData(array $dataArray): void
    {
        if (!isset($dataArray['reportsV2'])) {
            throw new \InvalidArgumentException(__METHOD__ . ' No reportsV2 index found');
        }

        foreach ($dataArray['reportsV2'] as $report) {
            if (!isset($report['id'])) {
                throw new \InvalidArgumentException(__METHOD__ . ' No id index found');
            }

            if ($report['id'] === 'query-detail-search-query-report-summary') {
                if (!isset($report['metrics'])) {
                    throw new \InvalidArgumentException(__METHOD__ . ' No metrics index found');
                }

                if (!isset($report['metrics']['qd-query-metrics-search-query'])) {
                    throw new \InvalidArgumentException(__METHOD__ . ' No search query index found');
                }

                if (!isset($report['metrics']['qd-asin-metrics-product'])) {
                    throw new \InvalidArgumentException(__METHOD__ . ' No asin index found');
                }

            }

            if ($report['id'] === 'query-detail-asin-report-table' && !isset($report['rows'])) {
                throw new \InvalidArgumentException(__METHOD__ . ' No rows index found');
            }
        }
    }

    protected function alreadyRequestedSqpBrandAndDate(string $countryCode, string $brandId, string|\DateTimeInterface $reportDate): bool
    {
        $reportDate = GravitiqTools::castToDateTime($reportDate);
        return $this->getApiRequestRepository()->hasAlreadyMadeRequestMatchingCriteria($countryCode, $brandId, $reportDate, $this->datePeriod, ApiRequestCrawlAsc::REQUEST_TYPE_SQP_BRAND);
    }

    /**
     * @param string $country
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @param list<string>|null $asins
     * @return void
     */
    protected function createCacheListOfAlreadyRequestedSqpAsinAndDate(string $country, \DateTimeInterface $startDate, \DateTimeInterface $endDate, ?array $asins): void
    {
        $this->cachedListOfAlreadyRequestedSqpAsinAndDate = $this->getApiRequestRepository()->getArrayOfFiltersAndDatesAlreadyRequested(
            ApiRequestCrawlAsc::REQUEST_TYPE_SQP_ASIN, $country, $startDate, $endDate, $asins
        );
    }

    protected function alreadyRequestedSqpAsinAndDate(string $countryCode, string $asin, string|\DateTimeInterface $reportDate): bool
    {
        $reportDate = GravitiqTools::castToDateTime($reportDate);
        $repo = $this->getApiRequestRepository();

        $key = $repo->buildKeyFromFilterAndDate($asin, $reportDate->format('Y-m-d'));
        return isset($this->cachedListOfAlreadyRequestedSqpAsinAndDate[$key]);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param string $countryCode
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @return ApiRequestCrawlAsc[]
     */
    public function retrieveTimedOutSqdRequests(AccountProfileSpa $profile, string $countryCode, \DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->getApiRequestRepository()->retrieveTimedOutSqdRequests($profile, $countryCode, $startDate, $endDate, $this->datePeriod);
    }

    protected function retrieveOrCreateNodeNicheRequest(AccountProfileSpa $profile, DataSpaBrowseTreeNode $browseNode, \DateTimeInterface $reportDate): ApiRequestCrawlAsc
    {
        $repo = $this->getApiRequestRepository();
        $existingRequest = $repo->retrieveByUniqueKeyElements(ApiRequestCrawlAsc::REQUEST_TYPE_POE_NODE, $reportDate, ApiRequestCrawlAsc::DATE_PERIOD_NONE, $browseNode->getCountryCode(), $browseNode->getBrowseNodeId(), $browseNode->getBrowseNodeName());
        if ($existingRequest) {
            $existingRequest->setRequestDate(new \DateTime());
            return $existingRequest;
        }

        $newRequest = $this->createAndPersistApiRequest($profile, $browseNode->getCountryCode(), $reportDate, $browseNode->getBrowseNodeId(), ApiRequestCrawlAsc::REQUEST_TYPE_POE_NODE);
        $newRequest->setQuery($browseNode->getBrowseNodeName());
        $this->flushChanges();
        return $newRequest;
    }

    protected function getApiRequestRepository(): ApiRequestCrawlAscRepository
    {
        /** @var ApiRequestCrawlAscRepository */
        return $this->getRepository(ApiRequestCrawlAsc::class);
    }

    /**
     * @throws \Exception
     */
    private function handleServiceUnavailable(AccountProfileSpa $profile): void
    {
        $this->logger->info("Service unavailable...");
        $this->sleepForSeconds(60, 120);

        $this->logoutWaitAndLogBackIn($profile);
    }

    /**
     * @throws \Exception
     */
    protected function logoutWaitAndLogBackIn(AccountProfileSpa $profile): void
    {
        $this->client->takeScreenshot('99 - pre logout bounce.jpg');
        $this->logger->info('saved screenshot: 99 - pre logout bounce.jpg');
        $this->logger->info("Failed to get result... log out and wait for 20-60 secs before trying again");
        $this->logOutFromSellerCentral();
        $this->sleepForSeconds(10, 30, 'pause 1/2');
        $this->sleepForSeconds(10, 30, 'pause 2/2');
        $this->rebootClient($profile);
    }

    protected function resetBaseUrl(): void
    {
        $currentUrl = $this->client->getCurrentURL();
        $this->baseUrl = substr($currentUrl, 0, strpos($currentUrl, '/', 10));
        $this->logger->info("Base URL: {$this->baseUrl}");
    }

    /**
     * @throws \Exception
     */
    protected function rebootClient(AccountProfileSpa|AccountGeneric $account): void
    {
        if ($this->loggedIn) {
            $this->logOutFromSellerCentral();
        }
        $this->closeClient();

        $this->logInToSellerCentral($account);
        if (!$this->loggedIn) {
            throw new \RuntimeException('Could not log back into seller central');
        }
        $this->changeAccountToMatchProfile($account);
    }

    protected function convertCountryCodeToUrlCountry(string $countryCode): string
    {
        $urlCountry = strtolower($countryCode);
        return (('uk' === $urlCountry) ? 'gb' : $urlCountry);
    }

    protected function setLoginTrackerToNotLoggedIn(): void
    {
        $this->loggedIn = false;
        $this->loggedInUsername = '';
    }
    protected function setLoginTrackerToLoggedInAs(AccountProfileSpa $profile): void
    {
        $this->loggedIn = true;
        $credentials = $profile->getCrawlerCredentials($this->credentials);
        $this->loggedInUsername = $credentials['email'];
    }
    protected function alreadyLoggedInWithMatchingCredentials(AccountProfileSpa $profile): bool
    {
        $credentials = $profile->getCrawlerCredentials($this->credentials);
        return ($this->loggedIn && $this->loggedInUsername === $credentials['email']);
    }

    /**
     * @throws \Exception|GuzzleException
     */
    protected function downloadAndSaveReportFileFromUrl(string $downloadUrl, ApiRequestCrawlAsc $apiRequest, string $fileExtension = BaseApiBridge::FILE_EXTENSION_XLSX): bool
    {
        try {
            $tmpFile = tmpfile();
            $metaData = stream_get_meta_data($tmpFile);
            $localPathFilename = $metaData['uri'];

            $client = GravitiqTools::createGuzzleClient();
            $client->get($downloadUrl, ['sink' => $localPathFilename]);

            $filename = $apiRequest->buildFilename($fileExtension);
            $apiRequest->setFilename($filename);
            $apiRequest->setFileSystemName($this->defaultFileSystemName);

            $this->writeStreamToFileSystem($apiRequest, $tmpFile);

            $apiRequest->setStatusToDone();
            $this->flushChanges();

            fclose($tmpFile);

            $this->logger->info("Saved file $filename to filesystem $this->defaultFileSystemName");

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Error downloading the report: " . $e->getMessage());
            $apiRequest->setStatusToFatal();
            $this->flushChanges();
            return false;
        }
    }
}