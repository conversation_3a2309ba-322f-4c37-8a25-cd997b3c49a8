<?php

declare(strict_types=1);

namespace App\Service\Internal;

use App\Domain\GoogleDriveManager;
use App\Entity\ChannelSku;
use App\Repository\ChannelSkuRepository;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Psr\Log\LoggerInterface;

class ExternalDataFileRenamer
{
    protected ObjectManager $em;
    protected string $googleDrivePath;
    /** @var array<string, string> $targetFilesCreated */
    protected array $targetFilesCreated = [];

    public const string FOLDER_PATH_STP_DATAROVA_INPUT = 'Data/Amazon Ads/Search Term Performance/Input Downloads/Datarova/';
    public const string FOLDER_PATH_STP_DATAROVA_OUTPUT = 'Data/Amazon Ads/Datarova/%brand_code%/';

    public function __construct(
        protected LoggerInterface    $logger,
        string                       $googleDriveInt,
        ManagerRegistry              $doctrine,
        protected GoogleDriveManager $googleDriveManager,
    )
    {
        $this->googleDrivePath = $googleDriveInt;
        $this->em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function processDatarovaFilesForStpReport(): void
    {
        $this->logger->info("Processing Datarova files for Advertising Report");
        $filenames = $this->getListOfFilesInFolder(self::FOLDER_PATH_STP_DATAROVA_INPUT);
        foreach ($filenames as $filename) {
            $this->renameAndMoveDatarovaFile($filename);
        }
    }

    /**
     * @param string $folderPath
     * @return list<string>
     */
    protected function getListOfFilesInFolder(string $folderPath): array
    {
        $remotePath = $this->googleDrivePath . $folderPath;
        $this->logger->info("Retrieving list of files in $remotePath");
        $files = $this->googleDriveManager->listFilesOnGDriveFolder($remotePath);
        if (is_string($files)) {
            throw new \RuntimeException("Error retrieving list of files in $remotePath: $files");
        }

        return $files;
    }

    protected function renameAndMoveDatarovaFile(string $sourceFilename): bool
    {
        $newFileName = $this->getNewNameForDatarovaFile($sourceFilename);
        if (empty($newFileName)) {
            $this->logger->warning("Could not determine new name for $sourceFilename");
            return false;
        }

        $suffix = 2;
        $newFileNameRoot = str_replace('.csv', '', $newFileName);
        while ($suffix<10 && !empty($this->targetFilesCreated[$newFileName])) {
            $this->logger->warning("Already created a file called $newFileName (from $sourceFilename)");
            $newFileName = "{$newFileNameRoot}_{$suffix}.csv";
            ++$suffix;
        }
        if (10 <= $suffix) {
            $this->logger->error("Cannot find usable name for $sourceFilename (tried up to $newFileName)");
            return false;
        }

        $sourceFilename = self::FOLDER_PATH_STP_DATAROVA_INPUT . $sourceFilename;
        $result = $this->googleDriveManager->moveFileOnGDrive($sourceFilename, $newFileName, $this->googleDrivePath, $this->googleDrivePath, true);
        if (!empty($result)) {
            $this->logger->warning("Error renaming $sourceFilename to $newFileName: $result");
            return false;
        }

        $this->logger->info("Renamed $sourceFilename to $newFileName");
        $this->targetFilesCreated[$newFileName] = $sourceFilename;

        return true;
    }

    /**
     * @param string $sourceFilename - something like Keywords-Tab-B012345678-230606-140704.csv
     * @return string|null - null if failed, otherwise something like ZZ_UK_DTRV_B012345678_2023-06-06.csv
     */
    protected function getNewNameForDatarovaFile(string $sourceFilename): ?string
    {
        if (empty($sourceFilename)) {
            $this->logger->warning("Empty source filename");
            return null;
        }

        $country = $this->extractCountryFromDatarovaFile($sourceFilename);
        $outputPath = self::FOLDER_PATH_STP_DATAROVA_OUTPUT;

        $filenameParts = [];
        if (preg_match('/\W([0-9B][0-9A-Z]{9})\W.*([23][0-9]{5})\W/i', $sourceFilename, $filenameParts)) {
            $dateParts = str_split($filenameParts[2], 2);
            $brandCode = $this->lookupBrandCodeForAsin($filenameParts[1]);
            $dateParts = implode('-', $dateParts);
        } elseif (preg_match('/_([0-9B][0-9A-Z]{9})_20(2[3-9]-[0-1][0-9]-[0-3][0-9])\./i', $sourceFilename, $filenameParts)) {
            $dateParts = $filenameParts[2];
            $brandCode = $this->lookupBrandCodeForAsin($filenameParts[1]);
        } else {
            $this->logger->warning("Could not extract ASIN and date from $sourceFilename");
        }

        if (!empty($brandCode) && !empty($dateParts)) {
            $outputPath = str_replace('%brand_code%', $brandCode, $outputPath);
            $newFilename = "{$outputPath}{$brandCode}_{$country}_DTRV_{$filenameParts[1]}_20{$dateParts}.csv";
        } else {
            $this->logger->warning("Missing brand code or date from $sourceFilename");
        }
        return $newFilename ?? null;
    }

    protected function lookupBrandCodeForAsin(string $asin): ?string
    {
        /** @var ChannelSkuRepository $csRepo */
        $csRepo = $this->em->getRepository(ChannelSku::class);
        $merchantAlias = $csRepo->retrieveMerchantAliasSellingUid($asin);
        $this->logger->info("Merchant aliases for $asin: $merchantAlias");
        if (!is_null($merchantAlias)) {
            $merchantAlias = substr($merchantAlias, 0, 2);
        }
        return $merchantAlias;
    }

    protected function extractCountryFromDatarovaFile(string $filename): string
    {
        $tempFile = tmpfile();
        $tempFilePath = GravitiqTools::getFilePath($tempFile, 'inputExcel');
        $remotePath = self::FOLDER_PATH_STP_DATAROVA_INPUT . $filename;
        $this->googleDriveManager->copyFileFromGDrive($remotePath, $tempFilePath, $this->googleDrivePath);
        $fileHandle = fopen($tempFilePath, 'r');
        $line1 = str_getcsv(fgets($fileHandle));
        $line2 = str_getcsv(fgets($fileHandle));
        fclose($fileHandle);
        fclose($tempFile);

        // find 'Marketplace' in $line1
        $colNum = array_search('Marketplace', $line1);
        if ($colNum === false) {
            throw new \RuntimeException("Could not find 'Marketplace' in $filename");
        }

        return $line2[$colNum];
    }
}