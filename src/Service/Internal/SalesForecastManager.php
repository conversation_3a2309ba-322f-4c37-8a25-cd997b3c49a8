<?php

namespace App\Service\Internal;

use App\Admin\Exception\ErrorException;
use App\Admin\Exception\InfoException;
use App\Admin\Tracker\SalesForecastGroupAdmin;
use App\Domain\AmazonSpaPool;
use App\Domain\Enum\SupplyChainCategory;
use App\Entity\Channel;
use App\Entity\ChannelSku;
use App\Entity\MerchantSku;
use App\Entity\Sonata\User;
use App\Entity\Tracker\Blueprint;
use App\Entity\Tracker\SalesForecastDetail;
use App\Entity\Tracker\SalesForecastGroup;
use App\Repository\ChannelRepository;
use App\Repository\ChannelSkuRepository;
use App\Repository\MerchantSkuRepository;
use App\Repository\Sonata\UserRepository;
use App\Repository\Tracker\BlueprintRepository;
use App\Repository\Tracker\SalesForecastGroupRepository;
use App\Service\Messaging\SlackMessenger;
use App\Service\Trait\HasEntityManagerTrait;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use PhpOffice\PhpSpreadsheet\Calculation\Exception;
use PhpOffice\PhpSpreadsheet\Exception as PhpSpreadsheetException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Psr\Log\LoggerInterface;
use Symfony\UX\Chartjs\Builder\ChartBuilderInterface;
use Symfony\UX\Chartjs\Model\Chart;

/**
 * @phpstan-type HistoricSalesDataArrayShape = array<int<0,max>|"recentDsr", float|array{"7d": float, "30d": float}>
 */
class SalesForecastManager
{
    use HasEntityManagerTrait;

    private LoggerInterface $logger;

    /** @var array<string, Channel> $channelsForSfg */
    protected array $channelsForSfg = [];
    /** @var list<string> $countriesForSfg */
    protected array $countriesForSfg = [];
    /** @var array<string, list<float>> $forecasts */
    protected array $forecasts = [];
    protected ?User $userForSfg = null;

    public const string DATA_KEY_DEFAULT = 'default';
    public const string DATA_KEY_FORECAST = 'forecast';
    public const string DATA_KEY_HISTORY = 'history';
    public const string DATA_KEY_HISTORY_2YR = '2_years_ago';
    public const string DATA_KEY_RECENT = 'recent';
    public const string DATA_KEY_RECENT_DSR_7D = 'last 7 days';
    public const string DATA_KEY_RECENT_DSR_30D = 'last 30 days';

    public const array FORECAST_KEYS = [
        'LOW' => 'Forecast Lowest  YoY',
        'AVG' => 'Forecast Average YoY',
        'HIGH' => 'Forecast Highest YoY',
        'SAME' => 'Same as last year',
        'DSR7' => 'Forecast DSR7d',
        'DSR30' => 'Forecast DSR30d',
    ];

    protected const array LINE_COLOR = [
        self::DATA_KEY_DEFAULT => 'rgb( 255,  99, 132)',
        self::DATA_KEY_FORECAST => 'rgb(  99,  99,  99, 0.75)',
        self::DATA_KEY_HISTORY => 'rgb( 132, 132, 255)',
        self::DATA_KEY_HISTORY_2YR => 'rgba(132, 132, 255, 0.5)',
        self::DATA_KEY_RECENT => 'rgba( 66, 132,  66, 0.75)',
        self::DATA_KEY_RECENT_DSR_7D => 'rgba(100, 100, 120, 1)',
        self::DATA_KEY_RECENT_DSR_30D => 'rgba(120, 100, 100, 1)',
        self::FORECAST_KEYS['LOW'] => 'rgba(180, 180, 100, 0.5)',
        self::FORECAST_KEYS['AVG'] => 'rgba(180, 100, 180, 0.5)',
        self::FORECAST_KEYS['HIGH'] => 'rgba(100, 180, 100, 0.5)',
        self::FORECAST_KEYS['DSR7'] => 'rgba(100, 100, 120, 0.5)',
        self::FORECAST_KEYS['DSR30'] => 'rgba(120, 100, 100, 0.5)',
    ];

    protected const string DATA_LABEL_X_LABELS = 'x-axis-labels';

    public function __construct(
        ManagerRegistry                  $doctrine,
        LoggerInterface                  $logger,
        protected string                 $gravitiqSfgDefaultUsername,
        protected SalesRateReportBuilder $salesRateReportBuilder,
        protected ChartBuilderInterface  $chartBuilder,
        protected SlackMessenger         $slackMessenger,
        protected string                 $slackChannelNotifySupplyChain
    )
    {
        $this->setEmFromDoctrine($doctrine);
        $this->logger = $logger;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
        $this->slackMessenger->setLogger($logger);
    }

    /**
     * @param list<string> $countryCodes
     * @return list<string>
     */
    public static function convertCountryCodesToSfRegions(array $countryCodes): array
    {
        $sfRegions = [];
        foreach ($countryCodes as $countryCode) {
            $sfRegion = self::convertCountryCodeToSfRegion($countryCode);
            if (!is_null($sfRegion)) {
                $sfRegions[] = $sfRegion;
            }
        }
        return array_unique($sfRegions);
    }

    public static function convertCountryCodeToSfRegion(string $countryCode): ?string
    {
        if (in_array($countryCode, ['US','CA','UK','AU','JP'])) {
            return $countryCode;
        }
        if (in_array($countryCode, AmazonSpaPool::POTENTIAL_PAN_EU_COUNTRIES)) {
            return 'EUR';
        }
        return null;
    }

    /*
     * 1. load list of DISTINCT MSKU.blueprints + region + channelFamily with no SFG in current month (limited to $limit)
     * 2. load list of SFG for previous month that match the list of blueprints
     * 3. loop through list of blueprints.
     *    (a) If SFG exists for last month, duplicate SFD linked to SFG, add new month's SFD and create new SFG linked to them
     *    (b) If SFG does not exist for last month, call createSfgForBlueprint
     */
    public function createSfgForListedBlueprintsWithoutThem(int $limit): void
    {
        /** @var MerchantSkuRepository $msRepo */
        $msRepo = $this->getRepository(MerchantSku::class);
        $blueprintRegionsMissingSfg = $msRepo->retrieveBlueprintRegionsWithoutSalesForecasts($limit);

        if (count($blueprintRegionsMissingSfg) === 0) {
            $this->logger->info("No blueprints missing SFG");
            return;
        }

        /** @var SalesForecastGroupRepository $sfgRepo */
        $sfgRepo = $this->getRepository(SalesForecastGroup::class);
        $lastMonth = new \DateTimeImmutable("midnight first day of last month");
        $lastMonthSfgs = $sfgRepo->retrieveForBlueprintRegionMonth($blueprintRegionsMissingSfg, $lastMonth);

        /** @var BlueprintRepository $blueprintRepo */
        $blueprintRepo = $this->getRepository(Blueprint::class);

        foreach ($blueprintRegionsMissingSfg as $blueRegion) {
            $key = $blueRegion['blueprintId'] . '~' . $blueRegion['sfRegion'] . '~' . $blueRegion['channelFamily'];
            if (empty($lastMonthSfgs[$key])) {
                $blueprint = $blueprintRepo->find($blueRegion['blueprintId']);
                $this->logger->info("Creating NEW SFG for blueprint {$blueprint->getIsku()}");
                $this->createSfgForBlueprint($blueprint, $blueRegion['sfRegion'], $blueRegion['channelFamily']);
            } else {
                $this->logger->info("Pulling SFG for blueprint {$key} forward to this month");
                $this->pullSfgForwardToThisMonth($lastMonthSfgs[$key]);
            }
        }

        $this->flushChanges();
    }

    protected function pullSfgForwardToThisMonth(SalesForecastGroup $sourceSfg): SalesForecastGroup
    {
        $newMonth = new \DateTimeImmutable("midnight first day of this month");

        if ($sourceSfg->getForecastMadeInMonth() === $newMonth) {
            throw new \InvalidArgumentException('Cannot duplicate a Sales Forecast Group for the same month');
        }

        $newSfg = new SalesForecastGroup();
        $newSfg->setBlueprint($sourceSfg->getBlueprint());
        $newSfg->setChannelFamily($sourceSfg->getChannelFamily());
        $newSfg->setSfRegion($sourceSfg->getSfRegion());
        $newSfg->setCreatedByUser($sourceSfg->getCreatedByUser());
        $newSfg->setSupplyChainCategory($sourceSfg->getSupplyChainCategory());
        $newSfg->setHasFirstSaleDate($sourceSfg->hasFirstSaleDate());

        $newSfg->setForecastMadeInMonth($newMonth);
        $newSfg->setConfirmedByUser(null);
        $newSfg->setForecastConfirmationDate(null);

        $sfdMonths = $this->getSfdMonthsForTargetMonth($newMonth);
        foreach ($sourceSfg->getSfDetails() as $oldSfd) {
            $key = array_search($oldSfd->getForecastIsForMonth(), $sfdMonths);
            if (false === $key) {
                continue;
            } else {
                unset($sfdMonths[$key]);
            }
            $newSfd = clone $oldSfd;
            $newSfd->setOrdinal($key);
            $newSfg->addSfDetail($newSfd);
        }

        foreach ($sfdMonths as $ordinal => $sfdMonth) {
            $sfd = new SalesForecastDetail();
            $sfd->setSfGroup($newSfg);
            $sfd->setForecastIsForMonth($sfdMonth);
            $sfd->setOrdinal($ordinal);
            $newSfg->addSfDetail($sfd);
        }

        $this->em->persist($newSfg);

        // Calculate and set the caFourMonthsDelta for the new SFG
        $this->updateCaFourMonthsDelta($newSfg);

        return $newSfg;
    }

    protected function createSfgForBlueprint(Blueprint $blueprint, ?string $onlyInRegion=null, ?string $familyCode=null): void
    {
        $channels = $this->getSfgEligibleChannels();
        $sfgUser = $this->getUserForSfg();
        $madeInMonth = new \DateTimeImmutable("midnight first day of this month");

        $sfdMonths = $this->getSfdMonthsForTargetMonth($madeInMonth);

        $regionsDone = [];
        foreach ($channels as $channel) {
            if (!empty($familyCode) && !$channel->isInFamily($familyCode)) {
                continue;
            }
            if (in_array($channel->getSfRegion(), $regionsDone)) {
                continue;
            }
            $regionsDone[] = $channel->getSfRegion();
            if (!is_null($onlyInRegion) && $channel->getSfRegion() !== $onlyInRegion) {
                continue;
            }

            $sfg = new SalesForecastGroup();
            $sfg->setBlueprint($blueprint);
            $sfg->setSfRegion($channel->getSfRegion());
            $sfg->setForecastMadeInMonth($madeInMonth);
            $sfg->setCreatedByUser($sfgUser);
            $sfg->setChannelFamily($channel->getFamilyCode());
            $this->em->persist($sfg);

            foreach ($sfdMonths as $ordinal => $sfdMonth) {
                $sfDetail = new SalesForecastDetail();
                $sfDetail->setForecastIsForMonth($sfdMonth);
                $sfDetail->setOrdinal($ordinal);
                $sfg->addSfDetail($sfDetail);
                $this->em->persist($sfDetail);
            }
        }
    }

    /**
     * @param \DateTimeInterface|null $targetMonth
     * @return array<int, \DateTimeImmutable>
     */
    protected function getSfdMonthsForTargetMonth(?\DateTimeInterface $targetMonth = null): array
    {
        try {
            if (is_null($targetMonth)) {
                $targetMonth = new \DateTimeImmutable("midnight first day of next month");
            } else {
                $targetMonth = new \DateTimeImmutable($targetMonth->format('c'));
            }
        } catch (\Exception $e) {
            throw new \InvalidArgumentException("Invalid target month for getSfdMonthsForTargetMonth", 0, $e);
        }

        return [
            1   => $targetMonth->add(new \DateInterval("P1M")),
            2   => $targetMonth->add(new \DateInterval("P2M")),
            3   => $targetMonth->add(new \DateInterval("P3M")),
            4   => $targetMonth->add(new \DateInterval("P4M")),
            5   => $targetMonth->add(new \DateInterval("P5M")),
            6   => $targetMonth->add(new \DateInterval("P6M")),
            7   => $targetMonth->add(new \DateInterval("P7M")),
            8   => $targetMonth->add(new \DateInterval("P8M")),
            9   => $targetMonth->add(new \DateInterval("P9M")),
            10  => $targetMonth->add(new \DateInterval("P10M")),
            11  => $targetMonth->add(new \DateInterval("P11M")),
            12  => $targetMonth->add(new \DateInterval("P12M")),
        ];
    }

    /**
     * @return array<string, Channel>
     */
    protected function getSfgEligibleChannels(): array
    {
        if (0 === count($this->channelsForSfg)) {
            /** @var ChannelRepository $channelRepo */
            $channelRepo = $this->getRepository(Channel::class);
            $this->channelsForSfg = $channelRepo->retrieveChannelsForSfg();
        }
        return $this->channelsForSfg;
    }
    /**
     * @return list<string>
     */
    protected function getCountriesForSfg(SalesForecastGroup $sfg): array
    {
        if (!isset($this->countriesForSfg[$sfg->getId()])) {
            $this->countriesForSfg[$sfg->getId()] = [];
            foreach ($this->getSfgEligibleChannels() as $channel) {
                if ($channel->getSfRegion() === $sfg->getSfRegion()) {
                    $this->countriesForSfg[$sfg->getId()][] = $channel->getPrimaryCountry();
                }
            }
        }
        return $this->countriesForSfg[$sfg->getId()];
    }

    /**
     * @return User
     */
    protected function getUserForSfg(): User
    {
        if (is_null($this->userForSfg)) {
            /** @var UserRepository $userRepo */
            $userRepo = $this->getRepository(User::class);
            $this->userForSfg = $userRepo->retrieveUserByUsername($this->gravitiqSfgDefaultUsername);
        }
        return $this->userForSfg;
    }

    public function buildChartForForecastModels(SalesForecastGroup $sfg): Chart
    {
        $salesHistory = $this->retrieveSalesHistoryForSfg($sfg, 27, true);
        $recentDsrs = $salesHistory['recentDsr'] ?? [];
        unset($salesHistory['recentDsr']);

        $fsr = $sfg->getSfDetailsWithGroupAsSfg();
        $padding = [null, null, null, null];
        $fsr = array_merge($padding, $fsr[SalesForecastManager::DATA_KEY_FORECAST]);
        $this->buildForecastFromHistory($salesHistory, $recentDsrs);

        $history1yr = array_slice($salesHistory, -16, 15);
        $history2yr = array_slice($salesHistory, 0, 15);
        $historyRecent = array_slice($salesHistory, -4);
        if (isset($recentDsrs['7d'])) {
            $recentDsr7d = [null, null, null, $recentDsrs['7d']];
        } else {
            $recentDsr7d = [];
        }
        if (isset($recentDsrs['30d'])) {
            $recentDsr30d = [null, null, null, $recentDsrs['30d']];
        } else {
            $recentDsr30d = [];
        }

        $dataLabels = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec','Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
        $thisMonthNumber = (new \DateTimeImmutable('now'))->format('n') - 4;
        $xTickLabels = GravitiqTools::arrayRotate($dataLabels, $thisMonthNumber);
        array_splice($xTickLabels, 15);
        $dataSeries = [
            self::DATA_LABEL_X_LABELS       => $xTickLabels,
            self::DATA_KEY_RECENT           => $historyRecent,
            self::DATA_KEY_RECENT_DSR_7D    => ['values'=>$recentDsr7d,  'options'=>['pointRadius' => 5, 'pointStyle' => 'rect']],
            self::DATA_KEY_RECENT_DSR_30D   => ['values'=>$recentDsr30d, 'options'=>['pointRadius' => 5, 'pointStyle' => 'triangle']],
            self::DATA_KEY_FORECAST         => $fsr,
            self::DATA_KEY_HISTORY          => $history1yr,
            self::DATA_KEY_HISTORY_2YR      => $history2yr,
        ];
        $padding = [null, null, null];
        $forecastDataSeriesOptions = [
            'borderDash' => [5, 5],
            'borderWidth' => 2,
            'tension' => 0.4,
        ];
        foreach ($this->forecasts as $label => $forecastData) {
            $dataSeries[$label] = ['values'=>array_merge($padding, $forecastData), 'options'=>$forecastDataSeriesOptions];
        }
        return $this->buildSonataEditChart($dataSeries);
    }

    /**
     * @param SalesForecastGroup $sfg
     * @return ChannelSku[]
     */
    public function buildChannelSkuListForForecastGroup(SalesForecastGroup $sfg): array
    {
        /** @var ChannelSkuRepository $csRepo */
        $csRepo = $this->getRepository(ChannelSku::class);
        return $csRepo->retrieveChannelSkusWithLinksForSfg($sfg);
    }

    /**
     * @param SalesForecastGroup $sfg
     * @param int $numMonths
     * @param bool $includeRecentDsr
     * @return HistoricSalesDataArrayShape
     */
    public function retrieveSalesHistoryForSfg(SalesForecastGroup $sfg, int $numMonths=11, bool $includeRecentDsr = false): array
    {
        $returnArray = array_fill(0, $numMonths+1, 0);

        $asinCountries = $sfg->getBlueprint()->getUidsWithChannelCountries($sfg->getChannelFamily());
        if (empty($asinCountries)) {
            return $returnArray;
        }

        $filteredAsinCountries = [];
        foreach ($asinCountries as $loopAsin => $loopCountries) {
            $filteredAsinCountries[$loopAsin] = array_intersect($loopCountries, $this->getCountriesForSfg($sfg));
            if (empty($filteredAsinCountries[$loopAsin])) {
                unset($filteredAsinCountries[$loopAsin]);
            }
        }
        if (empty($filteredAsinCountries)) {
            return $returnArray;
        }

        $sfg->setAsinsStringFromArray(array_keys($filteredAsinCountries));
        $dataArrayByAsin = $this->salesRateReportBuilder->buildSimpleDataArrayForAsinCountries($filteredAsinCountries, $numMonths, $includeRecentDsr);

        return $this->collapseMultiAsinArray($dataArrayByAsin, $includeRecentDsr, $returnArray);
    }

    /**
     * Combines values from a multi-asin array into a single array representing the sum of all asins
     *
     * @param array<string, HistoricSalesDataArrayShape> $dataArrayByAsin Keyed by ASIN_COUNTRY
     * @param bool $includeRecentDsr
     * @param array<int<0,max>, int|float> $returnArray
     * @return HistoricSalesDataArrayShape
     */
    private function collapseMultiAsinArray(array $dataArrayByAsin, bool $includeRecentDsr, array $returnArray): array
    {
        $recentDsr = [];
        foreach ($dataArrayByAsin as $dataArray) {
            if ($includeRecentDsr) {
                if (empty($recentDsr)) {
                    $recentDsr = $dataArray['recentDsr'];
                } else {
                    foreach ($recentDsr as $key => $value) {
                        if (!isset($dataArray['recentDsr'][$key])) {
                            throw new \RuntimeException("Mismatched recentDsr array lengths in retrieveSalesHistoryForSfg");
                        }
                        $recentDsr[$key] += $dataArray['recentDsr'][$key];
                    }
                }
                unset($dataArray['recentDsr']);
            }

            $loopValues = array_values($dataArray);
            if (count($returnArray) !== count($loopValues)) {
                throw new \RuntimeException("Mismatched array lengths in retrieveSalesHistoryForSfg");
            }
            foreach ($returnArray as $key => $value) {
                $returnArray[$key] += $loopValues[$key];
            }
        }

        if ($includeRecentDsr) {
            $returnArray['recentDsr'] = $recentDsr;
        }

        return $returnArray;
    }

    /**
     * @param array{"forecast": list<?float>, "sfg": SalesForecastGroup} $sfDetailsWithGroup
     * @return Chart
     * @noinspection PhpUnused - called from template
     */
    public function buildInlineChartFromSfDetailsWithGroupAsSfg(array $sfDetailsWithGroup): Chart
    {
        /** @phpstan-ignore-next-line Checks in case input is malformed */
        if (!isset($sfDetailsWithGroup['forecast']) || !isset($sfDetailsWithGroup['sfg'])) {
            throw new \InvalidArgumentException("Missing required data for buildInlineChartFromSfDetailsWithGroupAsSfg");
        }

        $dataSeries = [
            self::DATA_KEY_FORECAST => $sfDetailsWithGroup['forecast'],
        ];

        $salesHistory = $this->retrieveSalesHistoryForSfg($sfDetailsWithGroup['sfg']);
        if (!empty($salesHistory)) {
            $dataSeries[self::DATA_KEY_HISTORY] = $salesHistory;
        }

        return $this->buildInlineChart($dataSeries);
    }

    /**
     * @param array<string, array<string|int, float|int|null>> $dataSeries
     * @return Chart
     */
    public function buildInlineChart(array $dataSeries): Chart
    {
        $dataSeriesOptions = [
            'pointRadius' => 1,
        ];
        $chartOptions = [
            'scales' => [
                'x' => [
                    'display' => false,
                    'ticks' => [
                        'display' => false,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
            ],
        ];
        return $this->buildChart($dataSeries, $dataSeriesOptions, $chartOptions);
    }
    /**
     * @param array<string, array<string|int, float|int|null>> $dataSeries
     * @return Chart
     */
    public function buildSonataEditChart(array $dataSeries): Chart
    {
        $dataSeriesOptions = [
            'pointRadius' => 2,
        ];
        $chartOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'labels' => [
                        'usePointStyle' => true,
                    ]
                ],
            ],
            'aspectRatio' => 1.4,
        ];
        return $this->buildChart($dataSeries, $dataSeriesOptions, $chartOptions);
    }

    /**
     * @param array<string, mixed> $dataSeries
     * @param array<string, mixed> $dataSeriesOptions
     * @param array<string, mixed> $chartOptions
     * @return Chart
     */
    public function buildChart(array $dataSeries, array $dataSeriesOptions, array $chartOptions): Chart
    {
        $chart = $this->chartBuilder->createChart(Chart::TYPE_LINE);

        $dataSets = [];
        $smallest = null;
        $biggest = null;
        $biggestCount = 0;
        $xLabels = null;
        foreach ($dataSeries as $label => $data) {
            if (self::DATA_LABEL_X_LABELS === $label) {
                $xLabels = $data;
                continue;
            }

            if (isset($data['options'])) {
                $thisDataSeriesOptions = $data['options'];
                unset($data['options']);
            } else {
                $thisDataSeriesOptions = [];
            }
            if (isset($data['values'])) {
                $data = $data['values'];
            }

            $biggestCount = max($biggestCount, count($data));

            $color = $this->calculateColorForLabelledData($label, $data);
            if (empty($data)) {
                $dataMin = 0;
                $dataMax = 0;
            } else {
                $dataMin = min($data);
                $dataMax = max($data);
            }
            if (is_null($smallest) || $dataMin < $smallest) {
                $smallest = $dataMin;
            }
            if (is_null($biggest) || $dataMax > $biggest) {
                $biggest = $dataMax;
            }

            $dataSets[] = array_replace_recursive($dataSeriesOptions, [
                'data' => $data,
                'label' => $label,
                'backgroundColor' => $color,
                'borderColor' => $color,
            ], $thisDataSeriesOptions);
        }

        if (is_null($xLabels)) {
            $xLabels = range(1, $biggestCount);
        }
        $chart->setData([
            'labels' => $xLabels,
            'datasets' => $dataSets,
        ]);

        if ($smallest > 10) {
            $minKey = 'suggestedMin';
            $smallest = $smallest / 2;
        } else {
            $minKey = 'min';
            $smallest = 0;
        }
        $chart->setOptions(array_merge_recursive([
            'scales' => [
                'y' => [
                    $minKey => $smallest,
                    'suggestedMax' => $biggest * 1.1,
                ],
            ],
        ], $chartOptions));

        return $chart;
    }

    /**
     * @param string|int $label
     * @param array<string, mixed> $data
     * @return string
     */
    protected function calculateColorForLabelledData(string|int $label, array $data): string
    {
        if (isset($data['color'])) {
            return $data['color'];
        }
        return self::LINE_COLOR[$label] ?? self::LINE_COLOR['default'];
    }

    /**
     * @param array<string|int, float|int> $salesHistory
     * @param array<string|int, float|int> $recentDsrs
     * @return void
     */
    protected function buildForecastFromHistory(array $salesHistory, array $recentDsrs): void
    {
        $forecastMultipliers = [];
        $this->forecasts = [];
        $yoyDsrGrowthMonthMinus = [];
        $indexThisMonth = count($salesHistory) - 1;
        for ($monthMinus=1; $monthMinus<=3; $monthMinus++) {
            $dsrLastYear = $salesHistory[$indexThisMonth - $monthMinus - 12];
            if (0.05 > $dsrLastYear) {
                // $yoyDsrGrowthMonthMinus[$monthMinus] = 1;
                continue;
            }
            $dsrThisYear = $salesHistory[$indexThisMonth - $monthMinus];
            $yoyDsrGrowthMonthMinus[$monthMinus] = $dsrThisYear / $dsrLastYear;
        }

        if (2 <= count($yoyDsrGrowthMonthMinus)) {
            $forecastMultipliers[self::FORECAST_KEYS['LOW']]  = min($yoyDsrGrowthMonthMinus);
            $forecastMultipliers[self::FORECAST_KEYS['HIGH']] = max($yoyDsrGrowthMonthMinus);
        }
        if (1 <= count($yoyDsrGrowthMonthMinus)) {
            $forecastMultipliers[self::FORECAST_KEYS['AVG']] = array_sum($yoyDsrGrowthMonthMinus) / count($yoyDsrGrowthMonthMinus);
        }

        $dsrToUseWithRecentDsrMultiplier = $salesHistory[$indexThisMonth - 12];
        if (0 === $dsrToUseWithRecentDsrMultiplier) {
            $dsrToUseWithRecentDsrMultiplier = $salesHistory[$indexThisMonth];
        }
        if (0 === $dsrToUseWithRecentDsrMultiplier) {
            $dsrToUseWithRecentDsrMultiplier = $salesHistory[$indexThisMonth - 1];
        }
        if (0 < $dsrToUseWithRecentDsrMultiplier) {
            $forecastMultipliers[self::FORECAST_KEYS['DSR7']] = $recentDsrs['7d'] / $dsrToUseWithRecentDsrMultiplier;
            $forecastMultipliers[self::FORECAST_KEYS['DSR30']] = $recentDsrs['30d'] / $dsrToUseWithRecentDsrMultiplier;
        }

        foreach ($forecastMultipliers as $forecastKey => $multiplier) {
            $this->forecasts[$forecastKey] = [];
            for ($monthMinus=12; $monthMinus>=1; $monthMinus--) {
                $this->forecasts[$forecastKey][] = $salesHistory[$indexThisMonth - $monthMinus] * $multiplier;
            }
        }
    }

    /**
     * @return array<string, string>
     */
    public function getForecastTypeChoices(): array
    {
        $options = array_keys(self::FORECAST_KEYS);
        return array_combine($options, $options);
    }

    protected function getChannelRepository(): ChannelRepository
    {
        /** @var ChannelRepository $channelRepo */
        $channelRepo = $this->getRepository(Channel::class);
        return $channelRepo;
    }

    protected function getSalesForecastGroupRepository(): SalesForecastGroupRepository
    {
        /** @var SalesForecastGroupRepository $sfgRepo */
        $sfgRepo = $this->getRepository(SalesForecastGroup::class);
        return $sfgRepo;
    }

    /**
     * @param Spreadsheet $spreadsheet
     * @return string[]
     * @throws PhpSpreadsheetException
     */
    protected function getXlsxImportHeader(Spreadsheet $spreadsheet): array
    {
        $header = [];
        $expectedHeaderRow = 2;
        if ($spreadsheet->getActiveSheet()->getHighestRow() < $expectedHeaderRow) {
            throw new \RuntimeException("Header row is empty");
        }
        $headerRow = $spreadsheet->getActiveSheet()->getRowIterator($expectedHeaderRow)->current();
        foreach ($headerRow->getCellIterator() as $cell) {
            $value = $cell->getCalculatedValue();
            if (is_numeric($value) && Date::isDateTime($cell)) {
                $date = Date::excelToDateTimeObject($value);
                $cellValue = $date->format('Y-m');
            } else {
                $cellValue = $value;
            }
            if (is_null($cellValue)) {
                $cellCoOrd = $cell->getCoordinate();
                throw new \RuntimeException("Header contains null values in $cellCoOrd");
            }
            $header[] = $cellValue;
        }

        return $header;
    }

    /**
     * @param \DateTimeImmutable $forecastMadeInMonth
     * @param string[] $headers
     * @return bool
     * @noinspection PhpDocMissingThrowsInspection
     */
    private function isHeaderContainingExpectedDates(\DateTimeImmutable $forecastMadeInMonth, array $headers): bool
    {
        $startIndex = 4;
        $endIndex = 15;
        $loopDate = $forecastMadeInMonth;

        for ($i = $startIndex; $i <= $endIndex; $i++) {
            $loopDate = $loopDate->modify('+1 month');
            $expectedDate = $loopDate->format('Y-m');
            if ($headers[$i] !== $expectedDate) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param Spreadsheet $spreadsheet
     * @param string[] $header
     * @return list<array<string, string|null>>
     * @throws Exception
     */
    protected function extractImportedXlsxData(Spreadsheet $spreadsheet, array $header): array
    {
        $data = [];
        $expectedDataRowStart = 3;
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();
        if ($highestRow < $expectedDataRowStart) {
            throw new \RuntimeException("Spreadsheet contains no data");
        }
        $rowIterator = $sheet->getRowIterator($expectedDataRowStart);
        foreach ($rowIterator as $row) {
            $rowData = [];
            $index = 0;
            foreach ($row->getCellIterator() as $cell) {
                $rowData[$header[$index]] = $cell->getCalculatedValue();
                $index++;
            }

            $data[] = $rowData;
        }

        return $data;
    }

    protected function extractForecastMadeInMonth(\DateTimeImmutable $date, Spreadsheet $spreadsheet): \DateTimeImmutable
    {
        $sheet = $spreadsheet->getActiveSheet();
        $forecastMadeInMonth = $sheet->getCell('D1')->getCalculatedValue();
        $convertedForecastMadeInMonth = Date::excelToDateTimeObject($forecastMadeInMonth)->format('Y-m');

        if (is_null($forecastMadeInMonth)) {
            throw new \RuntimeException("Forecast made in month is null");
        }

        if ($convertedForecastMadeInMonth !== ($date->format('Y-m'))) {
            throw new \RuntimeException("Forecast made in month is not the same as the expected month");
        }

        return GravitiqTools::castToDateTimeImmutable($convertedForecastMadeInMonth . '-01'); // First day of the month
    }

    /**
     * @param array<string> $sfRegions
     * @param array<string, string|null> $row
     * @param int $index
     */
    protected function throwExceptionIfXlsxRowIsNotValid(array $sfRegions, array $row, int $index): void
    {
        $rowDescriptor = null;

        foreach ($row as $header => $value) {
            if ($value === null && $header !== "Is Confirmed") {
                throw new \InvalidArgumentException("Row {$index} ($rowDescriptor) contains null value in column $header");
            }
            if (is_null($rowDescriptor)) {
                $rowDescriptor = $value;
            }
        }

        if (!Channel::isChannelFamilyCodeValid($row['Channel'])) {
            throw new \InvalidArgumentException("Invalid channel {$row['Channel']} in row {$index}");
        }

        if (!in_array($row['Region'], $sfRegions)) {
            throw new \InvalidArgumentException("Invalid region {$row['Region']} in row {$index}");
        }

        if (!SupplyChainCategory::isValid($row['Category'])) {
            throw new \InvalidArgumentException("Invalid category {$row['Category']} in row {$index}");
        }
    }

    /**
     * @param SalesForecastDetail $sfDetail
     * @param array<string, mixed> $row
     * @param int $index
     * @return float
     */
    protected function fetchSfDetailMonthlyForecastInRow(SalesForecastDetail $sfDetail, array $row, int $index): float
    {
        $month = $sfDetail->getForecastIsForMonth()->format('Y-m');
        if (!isset($row[$month])) {
            throw new \RuntimeException("Row {$index} does not contain a forecast value for {$month}");
        }
        $forecast = $row[$month];
        if (!is_numeric($forecast)) {
            throw new \RuntimeException("Invalid forecast value for {$month} in row {$index}. Should be a number");
        }

        return $forecast;
    }

    /**
     * @param User $user
     * @param \DateTimeImmutable $date
     * @param string $filePath
     * @param SalesForecastGroupAdmin $admin
     * @return array{unchanged: list<string>, updated: list<string>}
     * @throws PhpSpreadsheetException
     */
    public function processImportedForecastXlsx(User $user, \DateTimeImmutable $date, string $filePath, SalesForecastGroupAdmin $admin): array
    {
        $unchanged = [];
        $updated = [];
        $spreadsheet = IOFactory::load($filePath);
        if ($spreadsheet->getActiveSheet()->getHighestRow() <= 1) {
            throw new \RuntimeException("Spreadsheet does not contain any data");
        }

        $headers = $this->getXlsxImportHeader($spreadsheet);
        if (!$this->isHeaderContainingExpectedDates($date, $headers)) {
            throw new \RuntimeException("Header contains unexpected dates. Should be within +1 month of current date and +13 months in the future");
        }
        $spreadSheetRowArray = $this->extractImportedXlsxData($spreadsheet, $headers);
        $sfRegions = $this->getChannelRepository()->retrieveChannelUniqueSfRegions();
        foreach ($spreadSheetRowArray as $index => $row) {
            $this->throwExceptionIfXlsxRowIsNotValid($sfRegions, $row, $index);
        }

        $forecastMadeInMonth = $this->extractForecastMadeInMonth($date, $spreadsheet);
        foreach ($spreadSheetRowArray as $index => $row) {
            $isUpdated = false;
            $sfg = $this->getSalesForecastGroupRepository()->retrieveSfgByIskuChannelFamilyRegionMonth($row['Blueprint'], $row['Channel'], $row['Region'], $forecastMadeInMonth);
            if (is_null($sfg)) {
                throw new \RuntimeException("No existing Sales Forecast Group found for blueprint {$row['Blueprint']}, channel {$row['Channel']} and region {$row['Region']} in row {$index}");
            }

            if (!$sfg->isPossibleToUnconfirm($admin->hasAccessUnconfirmEarly(), $admin->hasAccessUnconfirmAny())) {
                try {
                    $admin->throwExceptionIfSalesForecastGroupIsNotEditable($sfg);
                } catch (InfoException|ErrorException $e) {
                    throw new \RuntimeException($e->getMessage());
                }
            }

            $sfDetails = $sfg->getSfDetails();
            if ($sfDetails->count() !== SalesForecastGroup::EXPECTED_SALES_FORECAST_DETAIL_COUNT) {
                throw new \RuntimeException("Sales Forecast Group does not have the expected number of Sales Forecast Details ({$sfDetails->count()} <> " . SalesForecastGroup::EXPECTED_SALES_FORECAST_DETAIL_COUNT . ")");
            }

            foreach ($sfDetails as $sfDetail) {
                $fsr = $this->fetchSfDetailMonthlyForecastInRow($sfDetail, $row, $index);
                if ($fsr !== $sfDetail->getFsr()) {
                    $sfDetail
                        ->setFsr($fsr)
                        ->setDeltaCalc(0)
                        ->setDeltaPct(0)
                        ->setFsrPct(0);
                    $isUpdated = true;
                }
            }

            if ($sfg->getForecastMadeInMonth()->format('Y-m') !== $forecastMadeInMonth->format('Y-m')) {
                $sfg->setForecastMadeInMonth($forecastMadeInMonth);
                $isUpdated = true;
            }

            if ($sfg->getSfRegion() !== $row['Region']) {
                $sfg->setSfRegion($row['Region']);
                $isUpdated = true;
            }

            if ($sfg->getChannelFamily() !== $row['Channel']) {
                $sfg->setChannelFamily($row['Channel']);
                $isUpdated = true;
            }

            if ($sfg->getSupplyChainCategory()->value !== $row['Category']) {
                $sfg->setSupplyChainCategory(SupplyChainCategory::from($row['Category']));
                $isUpdated = true;
            }

            $isConfirmedValue = $row['Is Confirmed'] == 1;
            if ($sfg->isConfirmed() !== $isConfirmedValue) {
                if ($isConfirmedValue) {
                    $sfg->setConfirmedByUser($user);
                    $sfg->setForecastConfirmationDateToNow();
                } else {
                    $sfg->setConfirmedByUser(null);
                    $sfg->setForecastConfirmationDate(null);
                }
                $isUpdated = true;
            }

            if ($isUpdated) {
                $sfg->setCreatedByUser($user);
                // Recalculate caFourMonthsDelta after updates
                $this->updateCaFourMonthsDelta($sfg);
                $updated[] = "Updated $sfg";
            } else {
                $unchanged[] = "No changes for $sfg";
            }
        }

        $this->flushChanges();

        return [
            'updated' => $updated,
            'unchanged' => $unchanged,
        ];
    }

    public function sendSlackMessage(SalesForecastGroup $sfg, string $oldScCat, string $newScCat): void
    {
        $user = $sfg->getCreatedByUser()->getUsername();
        $isku = $sfg->getBlueprint()->getIsku();
        $sfRegion = $sfg->getSfRegion();

        $message = "$user changed scCat from $oldScCat to $newScCat for $isku in $sfRegion";

        $this->slackMessenger->sendMessageToChannel($message, $this->slackChannelNotifySupplyChain);
    }

    /**
     * @param SalesForecastGroup $sfg
     * @return float|null
     */
    public function calculateCaFourMonthsDelta(SalesForecastGroup $sfg): ?float
    {
        $sfDetails = $sfg->getSfDetails();
        if ($sfDetails->isEmpty()) {
            return null;
        }

        $currentForecastMadeInMonth = $sfg->getForecastMadeInMonth();
        $currentForecastWindowSum = $this->retrieveSfgFutureMonthsSum($sfg);

        $previousMonthDate = (clone $currentForecastMadeInMonth)->modify('-1 month');

        /** @var SalesForecastGroupRepository $sfgRepo */
        $sfgRepo = $this->getRepository(SalesForecastGroup::class);
        $previousMonthSfg = $sfgRepo->retrieveSfgByIskuChannelFamilyRegionMonth($sfg->getBlueprint()->getIsku(), $sfg->getChannelFamily(), $sfg->getSfRegion(), $previousMonthDate);

        $previousForecastWindowSum = 0.0;
        if ($previousMonthSfg) {
            $previousForecastWindowSum = $this->retrieveSfgFutureMonthsSum($previousMonthSfg);
        }

        return $currentForecastWindowSum - $previousForecastWindowSum;
    }

    public function retrieveSfgFutureMonthsSum(SalesForecastGroup $sfg): float
    {
        $nextFourMonthsSum = 0.0;

        for ($i = 1; $i <= 4; $i++) {
            $fsr = $sfg->getFsrForFutureMonthIndex($i);
            if (!is_null($fsr)) {
                $nextFourMonthsSum += $fsr;
            }
        }

        return $nextFourMonthsSum;
    }

    /**
     * @param SalesForecastGroup $sfg
     * @return void
     */
    public function updateCaFourMonthsDelta(SalesForecastGroup $sfg): void
    {
        $delta = $this->calculateCaFourMonthsDelta($sfg);
        $sfg->setCaFourMonthsDelta($delta);
    }
}