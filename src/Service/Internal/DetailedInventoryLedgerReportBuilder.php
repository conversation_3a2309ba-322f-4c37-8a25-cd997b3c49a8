<?php
declare(strict_types=1);

namespace App\Service\Internal;

/**
 * @phpstan-type ItemShape = array{startDate: \DateTimeImmutable, endDate: \DateTimeImmutable}
 * @extends BaseInternalReportBuilder<ItemShape>
 */
class DetailedInventoryLedgerReportBuilder extends BaseInternalReportBuilder
{
    protected function getLogMessageLoopStart(mixed $item): ?string
    {
        return "Processing Detailed Inventory Ledger from {$item['startDate']->format(self::DATE_FORMAT_YMD)} to {$item['endDate']->format(self::DATE_FORMAT_YMD)}";
    }

    protected function getOutputFilename(mixed $item): string
    {
        $filenameTemplate = "Detailed Inventory Ledger/Gravitiq_Detailed Inventory Ledger_%eventDate%.xlsx";
        return str_replace(
            ['%eventDate%'],
            [$item['startDate']->format('Y-m')],
            $filenameTemplate
        );
    }

    protected function getTemplateOutputStartCell(string $sheetName = null): string
    {
        return 'A1';
    }

    protected function buildDataArray(mixed $item, bool $addHeaderRow=false): ?array
    {
        $sql = <<<SQL
         SELECT LEFT (a.alias,2) AS brand, a.countryCode market
              , LAST_DAY( LEFT( d.eventDateTime,10)) eventDate, d.reportPeriod
              , d.fnsku, d.asin, d.sku, d.disposition
              , d.eventType, d.referenceId, d.fulfillmentCenter, d.country
              , SUM( d.quantity) qty, SUM( d.reconciledQuantity) AS reconciledQuantity, SUM( d.unreconciledQuantity) AS unreconciledQuantity
              , s.shipmentName, s.shipmentFromName, s.shipmentFromCity, s.shipmentFromCountryCode
           FROM data_spa_inv_ledger_detail d
           LEFT JOIN account_profile_spa a on a.id = d.spaProfileId
           LEFT JOIN spa_fba_inbound_shipment s ON d.referenceId = s.azFbaShipmentId
          WHERE d.eventDateTime >= '{$item['startDate']->format(self::DATE_FORMAT_YMD)}' and d.eventDateTime < '{$item['endDate']->format(self::DATE_FORMAT_YMD)}'
            AND d.referenceId <> ''
            AND d.reportPeriod = 'M'
          GROUP BY brand, market
              , eventDate, d.reportPeriod
              , d.fnsku, d.asin, d.sku, d.disposition
              , d.eventType, d.referenceId, d.fulfillmentCenter, d.country
        SQL;

        $dataArray = $this->runSqlAndReturnAllAsAssociativeArray($sql);
        if (empty($dataArray)) {
            return null;
        }

        if ($addHeaderRow) {
            $this->addHeaderRow($dataArray);
        }

        return $dataArray;
    }
}