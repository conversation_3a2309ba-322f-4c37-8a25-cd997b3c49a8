<?php

namespace App\Service\Keepa;

use App\Entity\Keepa\ApiRequestKeepa;
use App\Entity\Keepa\Product;
use App\Entity\Keepa\SalesRank;
use App\Repository\Keepa\ApiRequestKeepaRepository;
use App\Repository\Keepa\Product\Csv\AmazonPriceRepository;
use App\Repository\Keepa\Product\Csv\LightningDealRepository;
use App\Repository\Keepa\Product\Csv\NewCountRepository;
use App\Repository\Keepa\Product\Csv\NewPricesRepository;
use App\Repository\Keepa\Product\Csv\RatingRepository;
use App\Repository\Keepa\Product\Csv\ReviewsCountRepository;
use App\Repository\Keepa\Product\Csv\SalesRepository;
use App\Repository\Keepa\ProductRepository;
use App\Repository\Keepa\SalesRankRepository;
use App\Service\FileSystemSwitchboard;
use App\Service\Internal\CwrDataManipulator;
use App\Service\Trait\FileSystemSwitchboardTrait;
use App\Tools\GravitiqTools;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;
use Keepa\API\Request;
use Keepa\API\ResponseStatus;
use Keepa\KeepaAPI;
use Keepa\objects\AmazonLocale;
use Psr\Log\LoggerInterface;

/**
 * @phpstan-type KeepaConfigArrayShape = array{
 *     country: string,
 *     offers: int,
 *     statsStartDate: string,
 *     statsEndDate: string,
 *     updateIfOlderThanHours: int,
 *     includeHistory: bool,
 *     asin: string
 * }
 */
class KeepaManager
{
    use FileSystemSwitchboardTrait;

    protected LoggerInterface $logger;
    protected EntityManager $em;

    const string REQUEST_TYPE = 'product';

    public function __construct(
        FileSystemSwitchboard $switchboard,
        protected string      $keepaApiKey,
        string                $fileSystemKeepa,
        LoggerInterface       $logger,
        ManagerRegistry       $doctrine)
    {
        $this->setFileSystemSwitchboard($switchboard, $fileSystemKeepa);
        $this->logger = $logger;
        $this->em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function convertCountryCodeToDomainId(string $countryCode): int
    {
        if (!defined(AmazonLocale::class . "::$countryCode")) {
            throw new \InvalidArgumentException('Invalid Country Code: ' . $countryCode);
        }
        return constant(AmazonLocale::class . "::$countryCode");
    }

    public function convertDomainIdToCountryCode(int $domainId): string
    {
        $reflectionClass = new \ReflectionClass(AmazonLocale::class);
        $constants = $reflectionClass->getConstants();
        foreach ($constants as $name => $value) {
            if ($value == $domainId) {
                return $name;
            }
        }
        throw new \InvalidArgumentException('Invalid domain ID: ' . $domainId);
    }

    public function convertKeepaTimeToTimestamp(int $keepaTime): ?int
    {
        if ($keepaTime <= 0) {
            // Keepa uses -1 to indicate "unknown"
            return null;
        }
        return ($keepaTime + 21564000) * 60; // from Keepa docs
    }
    public function convertKeepaTimeToDateTime(int $keepaTime): ?\DateTime
    {
        if (is_null($timestamp = $this->convertKeepaTimeToTimestamp($keepaTime))) {
            return null;
        }
        return new \DateTime("@{$timestamp}");
    }
    public function convertKeepaTimeToDateTimeImmutable(int $keepaTime): ?\DateTimeImmutable
    {
        if (is_null($timestamp = $this->convertKeepaTimeToTimestamp($keepaTime))) {
            return null;
        }
        return new \DateTimeImmutable("@{$timestamp}");
    }

    public function flushIfNumCreatedIsOverThreshold(int &$numCreated): void
    {
        if ($numCreated > 1000) {
            try {
                $this->em->flush();
            } catch (ORMException|OptimisticLockException $e) {
                throw new \RuntimeException('Failed to flush: ' . $e->getMessage(), 0, $e);
            }
            $numCreated = 0;
        }
    }

    /**
     * @template T of object
     * @template-typeof T $className
     * @param class-string<T> $className
     * @return EntityRepository<T>
     */
    protected function getRepository(string $className): EntityRepository
    {
        try {
            return $this->em->getRepository($className);
        } catch (NotSupported $e) {
            throw new \RuntimeException("Failed to get $className repository", 0, $e);
        }
    }

    /**
     * @param KeepaConfigArrayShape $config
     *
     * @throws \Exception
     */
    public function retrieveAndParseKeepaProduct(array $config): void
    {
        $requiredFields = ['country', 'offers', 'statsStartDate', 'statsEndDate', 'updateIfOlderThanHours','includeHistory', 'asin'];

        foreach ($requiredFields as $field) {
            if (!isset($config[$field])) {
                $this->logger->error('Missing required data: ' . $field);
                throw new \InvalidArgumentException('Missing required data: ' . $field);
            }
        }

        $this->logger->info('Country: ' . $config['country']);
        $this->logger->info('ASIN: ' . $config['asin']);
        $this->logger->info('Access Token: ' . $this->keepaApiKey);

        $this->logger->info('Adding entry to ApiRequestKeepa...');
        $apiRequest = $this->createApiRequestKeepa($config);

        $this->logger->info('Calling Keepa API...');
        $response = $this->callKeepaApi($apiRequest);

        $this->logger->info('Saving API response to file...');
        $this->saveDataToFileLinkedToParsableEntity($response, $apiRequest);
        $this->markRequestAsCompletedSuccessfully($apiRequest);

        $this->logger->info('Parsing products from Keepa API response...');
        $this->parseApiRequest($apiRequest);
    }

    /**
     * @param ApiRequestKeepa $apiRequest
     * @return \Keepa\objects\Product[]
     * @throws \Exception
     */
    public function callKeepaApi(ApiRequestKeepa $apiRequest): array
    {
        $domainId = $this->convertCountryCodeToDomainId($apiRequest->getCountryCode());

        $keepa = new KeepaAPI($this->keepaApiKey);
        $productRequest = Request::getProductRequest(
            $domainId,
            $apiRequest->getOffers(),
            $apiRequest->getStartDate()->format('Y-m-d'),
            $apiRequest->getEndDate()->format('Y-m-d'),
            $apiRequest->getUpdateIfOlderThanHours(),
            $apiRequest->isIncludeHistory(),
            [$apiRequest->getAsin()]
        );

        $response = $keepa->sendRequestWithRetry($productRequest);
        $apiRequest->setTokensUsed($response->headers['X-RateLimit-Used'] ?? -1);
        $apiRequest->setTokensRemaining($response->headers['X-RateLimit-Remaining'] ?? -1);

        $productDetails = [];
        /** @phpstan-ignore-next-line There is a mistake in the Keepa API where response-status is marked as int, but it's actually a string */
        if (ResponseStatus::OK === $response->status) {
            foreach ($response->products as $product) {
                $productDetails[] = $product;
            }
        }

        return $productDetails;
    }

    /**
     * @param KeepaConfigArrayShape $config
     * @return ApiRequestKeepa
     */
    public function createApiRequestKeepa(array $config): ApiRequestKeepa
    {
        try {
            $newApiRequestKeepa = new ApiRequestKeepa();
            $newApiRequestKeepa->setRequestType(self::REQUEST_TYPE);

            $newApiRequestKeepa->setCountryCode($config['country']);
            $newApiRequestKeepa->setAsin($config['asin']);
            $newApiRequestKeepa->setOffers($config['offers']);
            $newApiRequestKeepa->setStartDate(GravitiqTools::castToDateTimeImmutable($config['statsStartDate']));
            $newApiRequestKeepa->setEndDate(GravitiqTools::castToDateTimeImmutable($config['statsEndDate']));
            $newApiRequestKeepa->setUpdateIfOlderThanHours($config['updateIfOlderThanHours']);
            $newApiRequestKeepa->setIncludeHistory($config['includeHistory']);

            $newApiRequestKeepa->setTokensUsed(0);
            $newApiRequestKeepa->setTokensRemaining(0);
            $newApiRequestKeepa->setRequestDate(new \DateTime());
            $newApiRequestKeepa->setStatusToInProgress();

            $this->em->persist($newApiRequestKeepa);
            $this->em->flush();

            return $newApiRequestKeepa;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create ApiRequestKeepa', 0, $e);
        }
    }

    protected function markRequestAsCompletedSuccessfully(ApiRequestKeepa $apiRequest): void
    {
        try {
            $apiRequest->setStatusToDone();
            $this->em->flush();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param int|list<int>|list<ApiRequestKeepa>|ApiRequestKeepa $requests
     * @return ApiRequestKeepa[]
     */
    private function convertRequestIdsToArrayOfRequests(int|array|ApiRequestKeepa $requests): array
    {
        if ($requests instanceof ApiRequestKeepa) {
            return [$requests];
        }

        if (is_array($requests)) {
            $firstElement = reset($requests);
            if ($firstElement instanceof ApiRequestKeepa) {
                return $requests;
            }
        }

        if (is_int($requests)) {
            $requests = [$requests];
        }

        try {
            /** @var ApiRequestKeepaRepository $apiRequestRepo */
            $apiRequestRepo = $this->em->getRepository(ApiRequestKeepa::class);
        } catch (NotSupported $e) {
            throw new \RuntimeException('Failed to get ApiRequestKeepaRepository', 0, $e);
        }

        return $apiRequestRepo->retrieveByIds($requests);
    }

    /**
     * @param int|list<int>|list<ApiRequestKeepa>|ApiRequestKeepa $requests
     * @return void
     */
    public function parseRequest(int|array|ApiRequestKeepa $requests): void
    {
        $requests = $this->convertRequestIdsToArrayOfRequests($requests);
        foreach ($requests as $request) {
            if ($request->isReadyToParseButNotYetParsed()) {
                $this->parseApiRequest($request);
            } else {
                $this->logger->info("Request #{$request->getId()} is not ready to parse. Skipping...");
            }
        }
    }

    /**
     * @param ApiRequestKeepa $apiRequestKeepa
     * @return void
     */
    public function parseApiRequest(ApiRequestKeepa $apiRequestKeepa): void
    {
        try {
            /** @var ProductRepository $keepaProductRepo */
            $keepaProductRepo = $this->em->getRepository(Product::class);
        } catch (NotSupported $e) {
            throw new \RuntimeException('Failed to get Keepa ProductRepository', 0, $e);
        }

        $responseData = $this->loadDataFromFileLinkedToParsableEntity($apiRequestKeepa);
        if (!is_null($responseData)) {
            foreach ($responseData as $productData) {
                $this->logger->info('Processing product with ASIN: ' . $productData['asin']);

                $existingProduct = $keepaProductRepo->retrieveOneByAsinAndCountryCode(
                    $productData['asin'],
                    $this->convertDomainIdToCountryCode($productData['domainId'])
                );

                if (!is_null($existingProduct)) {
                    if ($apiRequestKeepa->getRequestDate() < $existingProduct->getUpdatedFromApiRequest()->getRequestDate()) {
                        $this->logger->info('Product already exists in kp_product, but is newer than this request. Skipping...');
                        continue;
                    }
                    $this->logger->info('Product already exists in kp_product. Updating...');
                    $this->updateProductFromKeepaDataArray($existingProduct, $apiRequestKeepa, $productData);
                } else {
                    $this->logger->info('Product does not exist in kp_product. Creating...');
                    $this->createProductFromKeepaDataArray($apiRequestKeepa, $productData);
                }
            }

            try {
                $this->logger->info('Updating ApiRequestKeepa...');
                $apiRequestKeepa->setRecordCount(count($responseData));
                $apiRequestKeepa->setParseDateToNow();
                $this->em->flush();
                $this->logger->info('Done parsing product details');
            } catch (\Exception $e) {
                throw new \RuntimeException('Failed to update ApiRequestKeepa', 0, $e);
            }
        } else {
            throw new \InvalidArgumentException('Response data is null.', 500);
        }
    }

    /**
     * @param ApiRequestKeepa $sourceRequest
     * @param array<string, mixed> $productData
     * @return Product
     */
    public function createProductFromKeepaDataArray(ApiRequestKeepa $sourceRequest, array $productData): Product
    {
        try {
            $newProduct = new Product();
            $newProduct->setCreatedFromApiRequest($sourceRequest);
            $this->populateProductFromDataArray($newProduct, $sourceRequest, $productData);
            $this->em->persist($newProduct);
            $this->em->flush();

            $this->handleSalesRankAndCsvData($newProduct, $productData);
            return $newProduct;
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to create Product: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @param Product $existingProduct
     * @param ApiRequestKeepa $sourceRequest
     * @param array<string, mixed> $productData
     */
    public function updateProductFromKeepaDataArray(Product $existingProduct, ApiRequestKeepa $sourceRequest, array $productData): void
    {
        try {
            $this->populateProductFromDataArray($existingProduct, $sourceRequest, $productData);
            $this->handleSalesRankAndCsvData($existingProduct, $productData);
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to update Product: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @param Product $product
     * @param ApiRequestKeepa $sourceRequest
     * @param array<string, mixed> $productData
     */
    private function populateProductFromDataArray(Product $product, ApiRequestKeepa $sourceRequest, array $productData): void
    {
        $product->setUpdatedFromApiRequest($sourceRequest);
        $countryCode = $this->convertDomainIdToCountryCode($productData['domainId']);

        $product->setTrackingSince($this->convertKeepaTimeToDateTimeImmutable($productData['trackingSince']));
        $product->setListedSince($this->convertKeepaTimeToDateTimeImmutable($productData['listedSince']));

        $product->setPublicationDate($this->convertKeepaTimeToDateTime($productData['publicationDate']));
        $product->setReleaseDate($this->convertKeepaTimeToDateTime($productData['releaseDate']));
        $product->setLastUpdate($this->convertKeepaTimeToDateTime($productData['lastUpdate']));
        $product->setLastPriceChange($this->convertKeepaTimeToDateTime($productData['lastPriceChange']));
        $product->setLastRatingUpdate($this->convertKeepaTimeToDateTime($productData['lastRatingUpdate']));
        $product->setLastEbayUpdate($this->convertKeepaTimeToDateTime($productData['lastEbayUpdate']));

        $product->setAsin($productData['asin']);
        $product->setCountryCode($countryCode);
        $product->setParentAsin($productData['parentAsin']);
        $product->setVariationCsv($productData['variationCSV']);
        $product->setImagesCsv($productData['imagesCSV']);
        $product->setCategories($productData['categories']);
        $product->setRootCategory($productData['rootCategory']);
        $product->setManufacturer($productData['manufacturer']);
        $product->setTitle($productData['title']);
        $product->setBrand($productData['brand']);
        $product->setProductGroup($productData['productGroup']);
        $product->setPartNumber($productData['partNumber']);
        $product->setModel($productData['model']);
        $product->setColor($productData['color']);
        $product->setSize($productData['size']);
        $product->setEdition($productData['edition']);
        $product->setFormat($productData['format']);
        $product->setAuthor($productData['author']);
        $product->setBinding($productData['binding']);
        $product->setCategoryTree($productData['categoryTree']);
        $product->setNumberOfItems($productData['numberOfItems']);
        $product->setNumberOfPages($productData['numberOfPages']);
        $product->setLanguages($productData['languages']);
        $product->setContributors($productData['contributors']);
        $product->setFeatures($productData['features']);
        $product->setDescription($productData['description']);
        $product->setPackageHeight($productData['packageHeight']);
        $product->setPackageLength($productData['packageLength']);
        $product->setPackageWidth($productData['packageWidth']);
        $product->setPackageWeight($productData['packageWeight']);
        $product->setPackageQuantity($productData['packageQuantity']);
        $product->setItemHeight($productData['itemHeight']);
        $product->setItemLength($productData['itemLength']);
        $product->setItemWidth($productData['itemWidth']);
        $product->setItemWeight($productData['itemWeight']);
        $product->setEbayListingIds($productData['ebayListingIds']);
        $product->setIsAdultProduct($productData['isAdultProduct']);
        $product->setIsEligibleForTradeIn($productData['isEligibleForTradeIn']);
        $product->setIsEligibleForSuperSaverShipping($productData['isEligibleForSuperSaverShipping']);
        $product->setAvailabilityAmazon($productData['availabilityAmazon']);
        $product->setSalesRankReference($productData['salesRankReference']);
        $product->setSalesRankReferenceHistory($productData['salesRankReferenceHistory']);
        $product->setLaunchpad($productData['launchpad']);
        $product->setRentalDetails($productData['rentalDetails']);
        $product->setRentalPrices($productData['rentalPrices']);
        $product->setRentalSellerId($productData['rentalSellerId']);
        $product->setAvailabilityAmazonDelay($productData['availabilityAmazonDelay']);
        $product->setAudienceRating($productData['audienceRating']);
        $product->setProductType($productData['productType']);
        $product->setHasReviews($productData['hasReviews']);
        $product->setLiveOffersOrder($productData['liveOffersOrder']);
        $product->setBuyBoxSellerIdHistory($productData['buyBoxSellerIdHistory']);
        $product->setBuyBoxUsedHistory($productData['buyBoxUsedHistory']);
        $product->setIsSns($productData['isSNS']);
        $product->setOffersSuccessful($productData['offersSuccessful']);
        $product->setType($productData['type']);
        $product->setFrequentlyBoughtTogether($productData['frequentlyBoughtTogether']);
        $product->setPromotions($productData['promotions']);
        $product->setVariations($productData['variations']);
        $product->setCoupon($productData['coupon']);
        $product->setNewPriceIsMap($productData['newPriceIsMAP']);
        $product->setFbaFees($productData['fbaFees']);
        $product->setUpcList($productData['upcList']);
        $product->setEanList($productData['eanList']);
    }

    /**
     * @param Product $product
     * @param array<string, mixed> $productData
     * @return void
     */
    private function handleSalesRankAndCsvData(Product $product, array $productData): void
    {
        $numCreated = $this->parseKeepaProductSalesRank($product, $productData['salesRanks']);
        $this->flushIfNumCreatedIsOverThreshold($numCreated);

        $this->handleCsvData($product, $productData['csv']);
    }

    /**
     * @param Product $product
     * @param null|array<int, mixed> $csvData
     * @return void
     */
    private function handleCsvData(Product $product, ?array $csvData): void
    {
        if (is_null($csvData)) {
            $this->logger->info('CSV data is null. Skipping...');
            return;
        }

        $numCreated = 0;
        foreach ($csvData as $index => $data) {
            if (!is_null($data)) {
                switch ($index) {
                    case 0:
                        $numCreated += $this->parseKeepaProductCsvAmazon($product, $data);
                        break;
                    case 1:
                        $numCreated += $this->parseKeepaProductCsvNewPrices($product, $data);
                        break;
                    case 3:
                        $numCreated += $this->parseKeepaProductCsvSales($product, $data);
                        break;
                    case 8:
                        $numCreated += $this->parseKeepaProductCsvLightningDeal($product, $data);
                        break;
                    case 11:
                        $numCreated += $this->parseKeepaProductCsvNewCount($product, $data);
                        break;
                    case 16:
                        $numCreated += $this->parseKeepaProductCsvRating($product, $data);
                        break;
                    case 17:
                        $numCreated += $this->parseKeepaProductCsvReviewsCount($product, $data);
                        break;
                    default:
                        break;
                }
                $this->flushIfNumCreatedIsOverThreshold($numCreated);
            }
        }
    }

    /**
     * @param Product $product
     * @param null|array<int, mixed> $salesRankData
     * @return int
     */
    public function parseKeepaProductSalesRank(Product $product, ?array $salesRankData): int
    {
        /*
         *   data structure looks like
         *   "salesRanks": {
         *       "3760901": [6286036, 18580, 6286308, 17692, ...],
         *       "<categoryId>": [ keepaTime, salesRank, keepaTime, salesRank, ... ]
         *   }
         *   so alternating rows are keepaTime and salesRank
         */
        $this->logger->info('Parsing sales rank data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, array<string, SalesRank>> $newObjectsByCategoryAndDate
         */
        $newObjectsByCategoryAndDate = [];
        $createdSalesRankKeys = [];
        foreach ($salesRankData as $categoryId => $data) {
            $categoryId = (string)$categoryId;
            $newObjectsByCategoryAndDate[$categoryId] = [];
            for ($i = 0; $i < count($data); $i += 2) {
                $rankValue = $data[$i + 1];
                $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
                list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
                $dateKey = $startDate->format('Y-m-d');
                if (empty($newObjectsByCategoryAndDate[$categoryId][$dateKey])) {
                    $salesRank = new SalesRank();
                    $salesRank->setStartDate($startDate);
                    $salesRank->setEndDate($endDate);
                    $salesRank->setProduct($product);
                    $salesRank->setAzCategoryId($categoryId);
                    $newObjectsByCategoryAndDate[$categoryId][$dateKey] = $salesRank;
                    $createdSalesRankKeys[] = [
                        'product' => $product,
                        'categoryId' => $categoryId,
                        'startDate' => $startDate,
                        'endDate' => $endDate,
                    ];
                }
                $newObjectsByCategoryAndDate[$categoryId][$dateKey]->addDataPointToRunningTotal($rankValue);
            }
        }

        /** @var SalesRankRepository $salesRankRepo */
        $salesRankRepo = $this->getRepository(SalesRank::class);

        $existingSalesRanks = $salesRankRepo->retrieveByMatchingKeys($createdSalesRankKeys);
        foreach ($existingSalesRanks as $existingSalesRank) {
            $catKey = $existingSalesRank->getAzCategoryId();
            $dateKey = $existingSalesRank->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByCategoryAndDate[$catKey][$dateKey])) {
                $this->logger->warning('Existing SalesRank retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingSalesRank->updateFromSimilarObject($newObjectsByCategoryAndDate[$catKey][$dateKey]);
            unset($newObjectsByCategoryAndDate[$catKey][$dateKey]);
            if (count($newObjectsByCategoryAndDate[$catKey]) === 0) {
                /** @noinspection PhpConditionAlreadyCheckedInspection */
                unset($newObjectsByCategoryAndDate[$catKey]);
            }
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByCategoryAndDate as $catKey => $salesRanksByDate) {
                foreach ($salesRanksByDate as $dateKey => $newSalesRank) {
                    $this->logger->info('Creating new SalesRank record for ' . $catKey . ' on ' . $dateKey);
                    $this->em->persist($newSalesRank);
                    ++$numCreated;
                }
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating SalesRank: ' . $e->getMessage(), 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvAmazon(Product $product, ?array $data): int
    {
        /*
         *   data structure looks like
         *    [
         *        4007380, // keepaTime
         *        -1, // -1 if missing, otherwise integer price (in cents)
         *        ...
         *    ],
         *   so alternating rows are keepaTime and salePrice
         */
        $this->logger->info('Parsing CSV Amazon Price History data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\AmazonPrice> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\AmazonPrice();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var AmazonPriceRepository $amazonPriceRepo */
        $amazonPriceRepo = $this->getRepository(Product\Csv\AmazonPrice::class);

        $existingRecords = $amazonPriceRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing AmazonPrice retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new AmazonPrice record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating AmazonPrice', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvNewPrices(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV New Prices data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\NewPrices> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\NewPrices();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var NewPricesRepository $newPricesRepo */
        $newPricesRepo = $this->getRepository(Product\Csv\NewPrices::class);
        $existingRecords = $newPricesRepo->retrieveByMatchingKeys($createdKeys);

        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing NewPrice retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new NewPrice record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating NewPrice', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvSales(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV Sales data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\Sales> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\Sales();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var SalesRepository $salesRepo */
        $salesRepo = $this->getRepository(Product\Csv\Sales::class);

        $existingRecords = $salesRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing Sales retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new Sales record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating Sales', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvLightningDeal(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV Lightning Deal data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\LightningDeal> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\LightningDeal();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var LightningDealRepository $lightningDealRepo */
        $lightningDealRepo = $this->getRepository(Product\Csv\LightningDeal::class);

        $existingRecords = $lightningDealRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing LightningDeal retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new LightningDeal record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating LightningDeal', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvNewCount(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV New Count data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\NewCount> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\NewCount();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var NewCountRepository $newCountRepo */
        $newCountRepo = $this->getRepository(Product\Csv\NewCount::class);

        $existingRecords = $newCountRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing NewCount retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new NewCount record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating NewCount', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvRating(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV Rating data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\Rating> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\Rating();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var RatingRepository $ratingRepo */
        $ratingRepo = $this->getRepository(Product\Csv\Rating::class);

        $existingRecords = $ratingRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing Rating retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new Rating record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating Rating', 0, $e);
        }

        return $numCreated;
    }

    /**
     * @param Product $product
     * @param null|list<mixed> $data
     * @return int The number of new items created
     */
    public function parseKeepaProductCsvReviewsCount(Product $product, ?array $data): int
    {
        $this->logger->info('Parsing CSV Reviews Count data...');
        $numCreated = 0;

        // convert array into weekly date periods
        /**
         * @var array<string, Product\Csv\ReviewsCount> $newObjectsByDate
         */
        $newObjectsByDate = [];
        $createdKeys = [];

        for ($i = 0; $i < count($data); $i += 2) {
            $recordValue = $data[$i + 1];
            $recordDate = $this->convertKeepaTimeToDateTimeImmutable($data[$i]);
            list($startDate, $endDate) = CwrDataManipulator::getCwrDatesContainingDate($recordDate);
            $dateKey = $startDate->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $newObject = new Product\Csv\ReviewsCount();
                $newObject->setStartDate($startDate);
                $newObject->setEndDate($endDate);
                $newObject->setProduct($product);
                $newObjectsByDate[$dateKey] = $newObject;
                $createdKeys[] = [
                    'product' => $product,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ];
            }
            $newObjectsByDate[$dateKey]->addDataPointToRunningTotal($recordValue);
        }

        /** @var ReviewsCountRepository $reviewsCountRepo */
        $reviewsCountRepo = $this->getRepository(Product\Csv\ReviewsCount::class);

        $existingRecords = $reviewsCountRepo->retrieveByMatchingKeys($createdKeys);
        foreach ($existingRecords as $existingRecord) {
            $dateKey = $existingRecord->getStartDate()->format('Y-m-d');
            if (empty($newObjectsByDate[$dateKey])) {
                $this->logger->warning('Existing ReviewsCount retrieved, but now cannot find matching new record in hand...');
                continue;
            }
            $existingRecord->updateFromSimilarObject($newObjectsByDate[$dateKey]);
            unset($newObjectsByDate[$dateKey]);
        }

        try {
            // create new records for remaining items
            foreach ($newObjectsByDate as $dateKey => $newObject) {
                $this->logger->info('Creating new ReviewsCount record for ' . $dateKey);
                $this->em->persist($newObject);
                ++$numCreated;
            }
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException('Database operations failed when creating ReviewsCount', 0, $e);
        }

        return $numCreated;
    }
}