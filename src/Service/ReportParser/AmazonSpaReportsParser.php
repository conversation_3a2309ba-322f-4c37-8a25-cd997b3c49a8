<?php
/**
 * @noinspection PhpStatementHasEmptyBodyInspection - empty else blocks are present to simplify debugging
 */

namespace App\Service\ReportParser;

use App\Domain\Enum\SpaReportType;
use App\Entity\AccountProfileSpa;
use App\Entity\AmazonReportSpa;
use App\Entity\DataAzSalesTrafficAccount;
use App\Entity\DataAzSalesTrafficAsin;
use App\Entity\DataSpaBaMarketBasketAsin;
use App\Entity\DataSpaBaRepeatPurchaseAsin;
use App\Entity\DataSpaCouponPerformance;
use App\Entity\DataSpaInvPanEuEligibility;
use App\Entity\DataSpaInvReferralFee;
use App\Entity\DataSpaInvSuppressedListings;
use App\Entity\DataSpaOrderItem;
use App\Entity\DataSpaPromotionPerformance;
use App\Entity\DataSpaPromotionProduct;
use App\Entity\ParsableEntity;
use App\Entity\Spa\DataSpaBrowseTreeNode;
use App\Entity\Spa\DataSpaReservedInventory;
use App\Entity\Spa\Fba\DataSpaFbaRemovalOrder;
use App\Entity\Spa\Fba\DataSpaFbaRemovalShipment;
use App\Entity\Spa\Fba\DataSpaFbaReturn;
use App\Entity\Spa\Fba\DataSpaFbaSnsForecast;
use App\Entity\Spa\Fba\DataSpaFbaSnsPerformance;
use App\Entity\Spa\Fba\DataSpaFbaStranded;
use App\Entity\Spa\Inventory\DataSpaInvAge;
use App\Entity\Spa\Inventory\DataSpaInvFbaFeePreview;
use App\Entity\Spa\Inventory\DataSpaInvHealth;
use App\Entity\Spa\Inventory\DataSpaInvLedgerDetail;
use App\Entity\Spa\Inventory\DataSpaInvLedgerDetailSku;
use App\Entity\Spa\Inventory\DataSpaInvLedgerSummary;
use App\Entity\Spa\Inventory\DataSpaInvLedgerSummaryFc;
use App\Entity\Spa\Inventory\DataSpaInvListingsActive;
use App\Entity\Spa\Inventory\DataSpaInvListingsAll;
use App\Entity\Spa\Inventory\DataSpaInvListingsInactive;
use App\Entity\Spa\Inventory\DataSpaInvManagedAll;
use App\Entity\Spa\Inventory\DataSpaInvPlanning;
use App\Entity\Spa\Payment\DataSpaPayment;
use App\Entity\Spa\Performance\DataSpaPerfSellerFeedback;
use App\Entity\ValidForDateRangeRepositoryInterface;
use App\Entity\ValidForDateRangeTraitInterface;
use App\Repository\AccountProfileSpaRepository;
use App\Repository\AmazonReportSpaRepository;
use App\Repository\DataAzSalesTrafficAccountRepository;
use App\Repository\DataAzSalesTrafficAsinRepository;
use App\Repository\DataSpaBaMarketBasketAsinRepository;
use App\Repository\DataSpaBaRepeatPurchaseAsinRepository;
use App\Repository\DataSpaCouponPerformanceRepository;
use App\Repository\DataSpaFbaRemovalOrderRepository;
use App\Repository\DataSpaInvPanEuEligibilityRepository;
use App\Repository\DataSpaInvReferralFeeRepository;
use App\Repository\DataSpaOrderItemRepository;
use App\Repository\DataSpaPromotionPerformanceRepository;
use App\Repository\RetrieverOptions\AccountProfileSpaRO;
use App\Repository\Spa\DataSpaBrowseTreeNodeRepository;
use App\Repository\Spa\Fba\DataSpaFbaRemovalShipmentRepository;
use App\Repository\Spa\Fba\DataSpaFbaReturnRepository;
use App\Repository\Spa\Fba\DataSpaFbaSnsForecastRepository;
use App\Repository\Spa\Fba\DataSpaFbaSnsPerformanceRepository;
use App\Repository\Spa\Inventory\DataSpaInvAgeRepository;
use App\Repository\Spa\Inventory\DataSpaInvFbaFeePreviewRepository;
use App\Repository\Spa\Inventory\DataSpaInvHealthRepository;
use App\Repository\Spa\Inventory\DataSpaInvLedgerDetailRepository;
use App\Repository\Spa\Inventory\DataSpaInvLedgerSummaryFcRepository;
use App\Repository\Spa\Inventory\DataSpaInvLedgerSummaryRepository;
use App\Repository\Spa\Inventory\DataSpaInvListingsActiveRepository;
use App\Repository\Spa\Inventory\DataSpaInvListingsAllRepository;
use App\Repository\Spa\Inventory\DataSpaInvListingsInactiveRepository;
use App\Repository\Spa\Inventory\DataSpaInvManagedAllRepository;
use App\Repository\Spa\Inventory\DataSpaInvPlanningRepository;
use App\Repository\TrackableStatusBaseRepository;
use App\Service\AmazonSpaBridge;
use App\Service\BaseApiBridge;
use App\Service\FileSystemSwitchboard;
use App\Tools\GravitiqTools;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\Mapping\MappingException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class to handle parsing of AmazonSpa reports
 */
class AmazonSpaReportsParser extends AmazonBaseReportsParser
{
    const string JSON_KEY_reportSpecification = 'reportSpecification';
    const string JSON_KEY_salesAndTrafficByDate = 'salesAndTrafficByDate';
    const string JSON_KEY_salesAndTrafficByAsin = 'salesAndTrafficByAsin';
    const string JSON_KEY_dataByAsin = 'dataByAsin';

    /** @var array<string, AccountProfileSpa> */
    private array $profileAliasMap = [];

    public function __construct(
        ValidatorInterface $validator,
        ManagerRegistry $doctrine,
        LoggerInterface $logger,
        FileSystemSwitchboard $fileSystemSwitchboard,
        string $fileSystemAmazonReportsSpa)
    {
        parent::__construct($validator, $doctrine, $logger, $fileSystemSwitchboard, $fileSystemAmazonReportsSpa);
    }

    protected function getEntityRepository(): AmazonReportSpaRepository
    {
        /** @var AmazonReportSpaRepository $repo */
        $repo = $this->getRepository(AmazonReportSpa::class);
        return $repo;
    }

    public function extractRecordKeysByGroup(ParsableEntity $entity): void {
        // Checking for existing records is not implemented this way for Reports
    }

    protected function retrieveExistingRecords(): void {
        // Checking for existing records is not implemented this way for Reports
    }

    /**
     * @param AmazonReportSpa $entity
     * @return array{countEntitiesParsed:int, countRecordsCreated:int, countRecordsUpdated:int, countRecordsDiscarded:int}
     * @throws \Exception
     */
    public function parseEntity(ParsableEntity $entity): array
    {
        $countEntitiesParsed = $countRecordsCreated = 0;
        try {
            //@todo:: cross check missing report type on SpaReportType::class - don't forget to refer to jlevers resources/reports.json - use existing data for checking contentType
            if ($entity->getReportType() === SpaReportType::GET_SALES_AND_TRAFFIC_REPORT) {
                $countRecordsCreated += $this->parseSalesTrafficReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_MERCHANT_LISTINGS_ALL_DATA) {
                $countRecordsCreated += $this->parseInventoryListingsAllReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_BRAND_ANALYTICS_MARKET_BASKET_REPORT) {
                $countRecordsCreated += $this->parseBaMarketBasketReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_MERCHANT_LISTINGS_DATA) {
                $countRecordsCreated += $this->parseInventoryListingsActiveReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_MERCHANT_LISTINGS_INACTIVE_DATA) {
                $countRecordsCreated += $this->parseInventoryListingsInactiveReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_BRAND_ANALYTICS_REPEAT_PURCHASE_REPORT) {
                $countRecordsCreated += $this->parseBaRepeatPurchaseReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_MERCHANTS_LISTINGS_FYP_REPORT) {
                $countRecordsCreated += $this->parseInvSuppressedListingsReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_PAN_EU_OFFER_STATUS) {
                $countRecordsCreated += $this->parsePanEuEligibilityReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_REFERRAL_FEE_PREVIEW_REPORT) {
                $countRecordsCreated += $this->parseReferralFeePreviewReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA) {
                $countRecordsCreated += $this->parseCustomerReturnsReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_INVENTORY_AGED_DATA) { // deprecated by Amazon
                $countRecordsCreated += $this->parseInventoryAgeReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_ESTIMATED_FBA_FEES_TXT_DATA) {
                $countRecordsCreated += $this->parseFbaFeePreviewReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_FULFILLMENT_INVENTORY_HEALTH_DATA) {
                $countRecordsCreated += $this->parseInventoryHealthReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_FULFILLMENT_REMOVAL_SHIPMENT_DETAIL_DATA) {
                $countRecordsCreated += $this->parseRemovalShipmentReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_INVENTORY_PLANNING_DATA) {
                $countRecordsCreated += $this->parseInventoryPlanningReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_FULFILLMENT_REMOVAL_ORDER_DETAIL_DATA) {
                $countRecordsCreated += $this->parseRemovalOrderReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_STRANDED_INVENTORY_UI_DATA) {
                $countRecordsCreated += $this->parseFbaStrandedInventoryReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_SELLER_FEEDBACK_DATA) {
                $countRecordsCreated += $this->parsePerformanceSellerFeedbackReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_COUPON_PERFORMANCE_REPORT) {
                $countRecordsCreated += $this->parsePerformanceCouponReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FLAT_FILE_ALL_ORDERS_DATA_BY_ORDER_DATE_GENERAL
                ||    $entity->getReportType() === SpaReportType::GET_FLAT_FILE_ALL_ORDERS_DATA_BY_LAST_UPDATE_GENERAL
            ) {
                $countRecordsCreated += $this->parseOrderItemReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_LEDGER_SUMMARY_VIEW_DATA){
                $countRecordsCreated += $this->parseInventoryLedgerSummaryReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_LEDGER_DETAIL_VIEW_DATA){
                $countRecordsCreated += $this->parseInventoryLedgerDetailReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_MYI_ALL_INVENTORY_DATA) {
                $countRecordsCreated += $this->parseInventoryManagedAllReport($entity);
            } elseif ($entity->getReportType() === AmazonReportSpa::REPORT_MANUAL_PAYMENT_TRANSACTIONS_DATE_RANGE) {
                $countRecordsCreated += $this->parseManualPaymentTransactionsReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_XML_BROWSE_TREE_DATA) {
                $countRecordsCreated += $this->parseXmlBrowseTreeData($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_SNS_PERFORMANCE_DATA) {
                $countRecordsCreated += $this->parseFbaSnsPerformanceReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_FBA_SNS_FORECAST_DATA) {
                $countRecordsCreated += $this->parseFbaSnsForecastReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_RESERVED_INVENTORY_DATA) {
                $countRecordsCreated += $this->parseReservedInventoryReport($entity);
            } elseif ($entity->getReportType() === SpaReportType::GET_PROMOTION_PERFORMANCE_REPORT) {
                $countRecordsCreated += $this->parsePromotionPerformanceReport($entity);
            } else {
                $this->logger->warning("Cannot parse report #{$entity->getId()}, it has an unknown type of {$entity->getReportType()}");
                $entity->setParseDate(AmazonSpaBridge::getDoNotParseDateMarker());
                --$countEntitiesParsed; // effectively zero for this case, since it's incremented outside the condition block
            }
            ++$countEntitiesParsed;
        } catch (\InvalidArgumentException $e) {
            $this->logger->error($e->getMessage());
        }

        if (method_exists($entity, 'setRecordCount') && method_exists($entity, 'getRecordCount')) {
            if (is_null($entity->getRecordCount())) {
                $entity->setRecordCount($countRecordsCreated);
            }
        }

        $this->flushChanges();

        return [
            'countEntitiesParsed' => $countEntitiesParsed,
            'countRecordsCreated' => $countRecordsCreated,
            'countRecordsUpdated' => 0, // not counted in this method
            'countRecordsDiscarded' => 0,
        ];
    }

    /**
     * @param array<string, mixed> $dataArray
     * @param string $reportType
     * @param AmazonReportSpa $report
     * @return void
     */
    protected function validateReportSpecification(array $dataArray, string $reportType, AmazonReportSpa $report): void
    {
        if (empty($dataArray['reportType']) || $reportType !== $dataArray['reportType']) {
            throw new \InvalidArgumentException("Report #{$report->getId()} identifies itself as {$dataArray['reportType']} instead of $reportType");
        }
        if (empty($dataArray['marketplaceIds']) || 1 !== count($dataArray['marketplaceIds'])) {
            throw new \InvalidArgumentException("Report #{$report->getId()} has zero or multiple MarketplaceIds - should be just one");
        }
        if ($dataArray['marketplaceIds'][0] !== $report->getAccountProfile()->getMarketplaceId()) {
            throw new \InvalidArgumentException("Report #{$report->getId()} is for marketplace {$dataArray['marketplaceIds'][0]} but should be {$report->getAccountProfile()->getMarketplaceId()} ({$report->getAccountProfile()->getCountryCode()})");
        }
    }

    /**
     * @param array<string, mixed> $dataArray
     * @param string $reportType
     * @param AmazonReportSpa $report
     * @param string|null $reportPeriodField
     * @return \DateTime
     * @throws \Exception
     */
    protected function validateDailyReportSpecificationAndReturnReportDate(array $dataArray, string $reportType, AmazonReportSpa $report, ?string $reportPeriodField='reportPeriod'): \DateTime
    {
        $this->validateReportSpecification($dataArray, $reportType, $report);
        if (empty($dataArray['reportOptions'][$reportPeriodField]) || 'DAY' !== $dataArray['reportOptions'][$reportPeriodField]) {
            throw new \InvalidArgumentException("Report #{$report->getId()} is not set to DAY granularity (E146)");
        }
        if (empty($dataArray['dataStartTime']) || empty($dataArray['dataEndTime']) || $dataArray['dataStartTime'] !== $dataArray['dataEndTime']) {
            throw new \InvalidArgumentException("Report #{$report->getId()} needs the same start and end dates ('{$dataArray['dataStartTime']}' !== '{$dataArray['dataEndTime']}')");
        }

        return new \DateTime($dataArray['dataStartTime']);
    }

    /**
     * @param array<string, mixed> $dataArray
     * @param string $reportType
     * @param AmazonReportSpa $report
     * @param string|null $reportPeriodField
     * @param bool $allowMulti
     * @return \DateTime[]
     */
    protected function validateWeeklyReportSpecificationAndReturnReportDates(array $dataArray, string $reportType, AmazonReportSpa $report, ?string $reportPeriodField='reportPeriod', bool $allowMulti=false): array
    {
        $this->validateReportSpecification($dataArray, $reportType, $report);
        if (empty($dataArray['reportOptions'][$reportPeriodField]) || 'WEEK' !== $dataArray['reportOptions'][$reportPeriodField]) {
            throw new \InvalidArgumentException("Report #{$report->getId()} is not set to DAY granularity (E158)");
        }
        if (empty($dataArray['dataStartTime']) || empty($dataArray['dataEndTime'])) {
            throw new \InvalidArgumentException("Report #{$report->getId()} needs start and end dates to be set ('{$dataArray['dataStartTime']}' AND '{$dataArray['dataEndTime']}')");
        }

        try {
            $startDate = new \DateTime($dataArray['dataStartTime']);
            $endDate = new \DateTime($dataArray['dataEndTime']);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException("Cannot parse dates for report #{$report->getId()} ('{$dataArray['dataStartTime']}' and '{$dataArray['dataEndTime']}') Error was {$e->getMessage()}");
        }
        $reportSpanDays = GravitiqTools::daysBetweenDates($startDate, $endDate);
        if ($allowMulti) {
            if (($reportSpanDays - 6) % 7 !== 0) {
                throw new \InvalidArgumentException("Report #{$report->getId()} spans $reportSpanDays days but it should span a whole number of weeks");
            }
        } else {
            if (6 != $reportSpanDays) { // 6 not 7, because it's usually Sun 00:00 - Sat 23:59:59
                throw new \InvalidArgumentException("Report #{$report->getId()} spans $reportSpanDays days but it should span one week");
            }
        }

        return [$startDate, $endDate];
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseSalesTrafficReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);

        $reportDate = null;
        $flushQueue = 0;

        $accountKeys = [];
        $asinKeys = [];
        // Loop through all records and validate they are complete and match expected values (related to the profile)
        foreach ($dataArray as $key => $value) {
            if (self::JSON_KEY_reportSpecification === $key) {
                if (empty($value['reportOptions']['asinGranularity']) || 'CHILD' !== $value['reportOptions']['asinGranularity']) {
                    $report->setStatusToNotParsable();
                    $report->setParseDate(AmazonSpaBridge::getDoNotParseDateMarker());
                    throw new \InvalidArgumentException("Report #{$report->getId()} is not set to DAY granularity (E205)");
                }

                $reportDate = $this->validateDailyReportSpecificationAndReturnReportDate($value, SpaReportType::GET_SALES_AND_TRAFFIC_REPORT, $report, 'dateGranularity');
            } elseif (self::JSON_KEY_salesAndTrafficByDate === $key) {
                foreach ($value as $record) {
                    if (empty($record['salesByDate']['orderedProductSales']['currencyCode']) ||
                        empty($record['salesByDate']['claimsAmount']['currencyCode']) ||
                        empty($record['salesByDate']['shippedProductSales']['currencyCode'])
                    ) {
                        throw new \InvalidArgumentException("Account record found without a currency code in Report #{$report->getId()}: " . print_r($record, true));
                    }
                    if ($report->getAccountProfile()->getCurrencyCode() !== $record['salesByDate']['orderedProductSales']['currencyCode'] ||
                        $report->getAccountProfile()->getCurrencyCode() !== $record['salesByDate']['shippedProductSales']['currencyCode'] ||
                        $report->getAccountProfile()->getCurrencyCode() !== $record['salesByDate']['claimsAmount']['currencyCode']
                    ) {
                        throw new \InvalidArgumentException("Record found with currency code {$record['salesByDate']['orderedProductSales']['currencyCode']} but report is linked to profile that uses {$report->getAccountProfile()->getCurrencyCode()}");
                    }

                    $accountKeys[] = $record['date'];
                }
            } elseif (self::JSON_KEY_salesAndTrafficByAsin === $key) {
                $countAsinRecords = 0;
                foreach ($value as $record) {
                    if (empty($record['salesByAsin']['orderedProductSales']['currencyCode'])) {
                        throw new \InvalidArgumentException("ASIN record found without a currency code in Report #{$report->getId()}: " . print_r($record, true));
                    }
                    if ($report->getAccountProfile()->getCurrencyCode() !== $record['salesByAsin']['orderedProductSales']['currencyCode']) {
                        throw new \InvalidArgumentException("Record found with currency code {$record['salesByDate']['orderedProductSales']['currencyCode']} but report is linked to profile that uses {$report->getAccountProfile()->getCurrencyCode()}");
                    }

                    ++$countAsinRecords;
                    $asinKeys[] = $record['childAsin'];
                }
                $report->setRecordCount($countAsinRecords);
            }
        }
        $accountKeys = array_unique($accountKeys);
        if (count($accountKeys) > 1) {
            throw new \InvalidArgumentException("Report #{$report->getId()} has multiple dates in the account records: " . implode(', ', $accountKeys));
        }
        $accountKeyDate = new \DateTime($accountKeys[0]);

        /** @var DataAzSalesTrafficAccountRepository $dataAcctRepo */
        $dataAcctRepo = $this->getRepository(DataAzSalesTrafficAccount::class);
        $existingDataAccountRecords = $dataAcctRepo->retrieveByProfileDateJoinReport($report->getAccountProfile(), $accountKeyDate);
        /** @var DataAzSalesTrafficAsinRepository $dataAsinRepo */
        $dataAsinRepo = $this->getRepository(DataAzSalesTrafficAsin::class);
        $existingDataAsinRecords = $dataAsinRepo->retrieveByProfileDateAsinsJoinReport($report->getAccountProfile(), $reportDate, $asinKeys);

        foreach ($dataArray as $key => $value) {
            if (self::JSON_KEY_reportSpecification === $key) {
                continue;
            } elseif (self::JSON_KEY_salesAndTrafficByDate === $key) {
                if (empty($reportDate)) {
                    throw new \InvalidArgumentException("Report #{$report->getId()} does not seem to have a date before the sales data");
                }

                foreach ($value as $record) {
                    ++$countRecords;

                    if (empty($record['date']) || $reportDate->format('Y-m-d') !== $record['date']) {
                        throw new \InvalidArgumentException("Report #{$report->getId()} has a record with a different date to the main report {$reportDate->format('Y-m-d')} !== {$value['date']}");
                    }

                    $dataRowAcct = new DataAzSalesTrafficAccount();
                    $dataRowAcct->setStartDate($reportDate);
                    $dataRowAcct->setEndDate($reportDate);
                    $dataRowAcct->setSpaProfile($report->getAccountProfile());

                    if ($existingIndex = $dataRowAcct->sameKeyAlreadyExistsInCollection($existingDataAccountRecords)) {
                        if ($this->allowUpdate) {
                            if ($existingDataAccountRecords[$existingIndex]->getCreatedFromReportSpa()->getRequestDate() > $report->getRequestDate()) {
                                $this->logger->info("Duplicate record found, but it's from a more recent report, see DataAzSalesTrafficAccount #$existingIndex. Not saving the stale data in hand.");
                                continue;
                            }
                            $dataRowAcct = $existingDataAccountRecords[$existingIndex];
                            $this->logger->info("Updating account record #{$dataRowAcct->getId()}");
                        } else {
                            throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataAzSalesTrafficAccount #$existingIndex");
                        }
                    }

                    $dataRowAcct->setCreatedFromReportSpa($report);
                    $dataRowAcct->setSalesValue(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByDate']['orderedProductSales']['amount']));
                    $dataRowAcct->setSalesUnits($record['salesByDate']['unitsOrdered']);
                    $dataRowAcct->setSalesOrders($record['salesByDate']['totalOrderItems']);
                    $dataRowAcct->setRefundUnits($record['salesByDate']['unitsRefunded']);
                    $dataRowAcct->setClaimsCount($record['salesByDate']['claimsGranted']);
                    $dataRowAcct->setClaimsValue(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByDate']['claimsAmount']['amount']));
                    $dataRowAcct->setShippedValue(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByDate']['shippedProductSales']['amount']));
                    $dataRowAcct->setShippedUnits($record['salesByDate']['unitsShipped']);
                    $dataRowAcct->setShippedOrders($record['salesByDate']['ordersShipped']);
                    $dataRowAcct->setPageViewsBrowser($record['trafficByDate']['browserPageViews']);
                    $dataRowAcct->setSessionsBrowser($record['trafficByDate']['browserSessions']);

                    if (isset($record['salesByDate']['orderedProductSalesB2B']['amount'])) {
                        $dataRowAcct->setSalesValueB2B(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByDate']['orderedProductSalesB2B']['amount']));
                    }
                    if (isset($record['salesByDate']['unitsOrderedB2B'])) {
                        $dataRowAcct->setSalesUnitsB2B($record['salesByDate']['unitsOrderedB2B']);
                    }
                    if (isset($record['salesByDate']['unitsOrderedB2B'])) {
                        $dataRowAcct->setSalesOrdersB2B($record['salesByDate']['unitsOrderedB2B']);
                    }

                    if (isset($record['trafficByDate']['mobileAppPageViews'])) {
                        $dataRowAcct->setPageViewsApp($record['trafficByDate']['mobileAppPageViews']);
                    }
                    if (isset($record['trafficByDate']['mobileAppSessions'])) {
                        $dataRowAcct->setSessionsApp($record['trafficByDate']['mobileAppSessions']);
                    }

                    $dataRowAcct->setBuyBoxPct($record['trafficByDate']['buyBoxPercentage']);
                    $dataRowAcct->setOfferedAsins($record['trafficByDate']['averageOfferCount']);
                    $dataRowAcct->setOfferedParents($record['trafficByDate']['averageParentItems']);
                    $dataRowAcct->setFeedbackRcvd($record['trafficByDate']['feedbackReceived']);
                    $dataRowAcct->setFeedbackRcvdNegative($record['trafficByDate']['negativeFeedbackReceived']);

                    $this->persist($dataRowAcct);
                    $this->flushChanges();
                    $this->em->detach($dataRowAcct);
                }
            } elseif (self::JSON_KEY_salesAndTrafficByAsin === $key) {
                if (empty($reportDate)) {
                    throw new \InvalidArgumentException("Report #{$report->getId()} does not seem to have a date before the sales data");
                }

                foreach ($value as $record) {
                    ++$countRecords;
                    $dataRowAsin = new DataAzSalesTrafficAsin();

                    $dataRowAsin->setStartDate($reportDate);
                    $dataRowAsin->setEndDate($reportDate);
                    $dataRowAsin->setSpaProfile($report->getAccountProfile());
                    $dataRowAsin->setParentAsin($record['parentAsin']);
                    $dataRowAsin->setChildAsin($record['childAsin']);

                    if ($existingIndex = $dataRowAsin->sameKeyAlreadyExistsInCollection($existingDataAsinRecords)) {
                        $recordId = $existingDataAsinRecords[$existingIndex]->getId();
                        if ($this->allowUpdate) {
                            if ($existingDataAsinRecords[$existingIndex]->getCreatedFromReportSpa()->getRequestDate() > $report->getRequestDate()) {
                                $this->logger->info("Duplicate record found, but it's from a more recent report, see DataAzSalesTrafficAsin #$recordId. Not saving the stale data in hand.");
                                continue;
                            }

                            $dataRowAsin = $existingDataAsinRecords[$existingIndex];
                            $this->logger->info("Updating asin record #{$dataRowAsin->getId()}");
                        } else {
                            throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataAzSalesTrafficAsin #$recordId");
                        }
                    }

                    $dataRowAsin->setCreatedFromReportSpa($report);
                    $dataRowAsin->setSalesValue(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByAsin']['orderedProductSales']['amount']));
                    $dataRowAsin->setSalesUnits($record['salesByAsin']['unitsOrdered']);
                    $dataRowAsin->setSalesOrders($record['salesByAsin']['totalOrderItems']);
                    $dataRowAsin->setPageViewsBrowser($record['trafficByAsin']['browserPageViews']);
                    $dataRowAsin->setSessionsBrowser($record['trafficByAsin']['browserSessions']);

                    if (isset($record['salesByAsin']['orderedProductSalesB2B']['amount'])) {
                        $dataRowAsin->setSalesValueB2B(AmazonSpaBridge::convertDecimalPriceToInt($record['salesByAsin']['orderedProductSalesB2B']['amount']));
                    }
                    if (isset($record['salesByAsin']['unitsOrderedB2B'])) {
                        $dataRowAsin->setSalesUnitsB2B($record['salesByAsin']['unitsOrderedB2B']);
                    }
                    if (isset($record['salesByAsin']['totalOrderItemsB2B'])) {
                        $dataRowAsin->setSalesOrdersB2B($record['salesByAsin']['totalOrderItemsB2B']);
                    }

                    if (isset($record['trafficByAsin']['mobileAppPageViews'])) {
                        $dataRowAsin->setPageViewsApp($record['trafficByAsin']['mobileAppPageViews']);
                    }
                    if (isset($record['trafficByAsin']['mobileAppSessions'])) {
                        $dataRowAsin->setSessionsApp($record['trafficByAsin']['mobileAppSessions']);
                    }

                    $dataRowAsin->setBuyBoxPct($record['trafficByAsin']['buyBoxPercentage']);

                    $this->persist($dataRowAsin);

                    if (++$flushQueue > self::FLUSH_QUEUE_LENGTH) {
                        $this->flushChanges();
                        $flushQueue = 0;

                        $this->em->clear(get_class($dataRowAsin));
                    }
                }
            } else {
                throw new \InvalidArgumentException("Cannot parse top level JSON key $key for Report #{$report->getId()}");
            }
        }

        $report->setRecordCount($countRecords);
        $report->setParseDateToNow();
        $this->flushChanges();
        $this->em->clear('App\\Entity\\DataAzSalesTrafficAccount');
        $this->em->clear('App\\Entity\\DataAzSalesTrafficAsin');

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseBaMarketBasketReport(AmazonReportSpa $report): int
    {
        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);

        $reportDate = null;

        $asinKeys = [];
        // Loop through all records and validate they are complete and match expected values (related to the profile)
        foreach ($dataArray as $key => $value) {
            if (self::JSON_KEY_reportSpecification === $key) {
                $reportDate = $this->validateDailyReportSpecificationAndReturnReportDate($value, SpaReportType::GET_BRAND_ANALYTICS_MARKET_BASKET_REPORT, $report);
            } elseif (self::JSON_KEY_dataByAsin === $key) {
                foreach ($value as $record) {
                    $asinKeys[] = $record['asin'];
                }
            }
        }

        $dataClass = DataSpaBaMarketBasketAsin::class;
        /** @var DataSpaBaMarketBasketAsinRepository $dataRepo */
        $dataRepo = $this->getRepository($dataClass);
        $existingDataAsinRecords = $dataRepo->retrieveByProfileDateAsins($report->getAccountProfile(), $reportDate, $asinKeys);

        return $this->extractDataByAsinFromDataArray($dataArray, $dataClass, $report, $existingDataAsinRecords);
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseBaRepeatPurchaseReport(AmazonReportSpa $report): int
    {
        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $reportStartDate = $reportEndDate = null;

        $asinKeys = [];
        // Loop through all records and validate they are complete and match expected values (related to the profile)
        foreach ($dataArray as $key => $value) {
            if (self::JSON_KEY_reportSpecification === $key) {
                list($reportStartDate, $reportEndDate) = $this->validateWeeklyReportSpecificationAndReturnReportDates($value, SpaReportType::GET_BRAND_ANALYTICS_REPEAT_PURCHASE_REPORT, $report, 'reportPeriod', true);
            } elseif (self::JSON_KEY_dataByAsin === $key) {
                foreach ($value as $record) {
                    $asinKeys[] = $record['asin'];
                }
            }
        }

        if (empty($reportStartDate) || empty($reportEndDate)) {
            throw new \InvalidArgumentException("Could not find dates in reportSpecification of BaRepeatPurchaseReport #{$report->getId()}");
        }

        $dataClass = DataSpaBaRepeatPurchaseAsin::class;
        /** @var DataSpaBaRepeatPurchaseAsinRepository $dataRepo */
        $dataRepo = $this->getRepository($dataClass);
        $existingDataAsinRecords = $dataRepo->retrieveByProfileDatesAsins($report->getAccountProfile(), $reportStartDate, $reportEndDate, $asinKeys);

        return $this->extractDataByAsinFromDataArray($dataArray, $dataClass, $report, $existingDataAsinRecords);
    }

    /**
     * @param array<string, list<array<string, mixed>>> $dataArray
     * @param class-string $dataClass
     * @param AmazonReportSpa $report
     * @param DataSpaBaMarketBasketAsin[]|DataSpaBaRepeatPurchaseAsin[] $existingDataAsinRecords
     * @return int
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws MappingException
     */
    function extractDataByAsinFromDataArray(array $dataArray, string $dataClass, AmazonReportSpa $report, array $existingDataAsinRecords): int
    {
        $countRecords = 0;
        $flushQueue = 0;

        foreach ($dataArray as $sectionKey => $section) {
            if (self::JSON_KEY_reportSpecification === $sectionKey) {
                continue;
            } elseif (self::JSON_KEY_dataByAsin === $sectionKey) {
                foreach ($section as $record) {
                    $dataRowAsin = new $dataClass();
                    $dataRowAsin->setSpaProfile($report->getAccountProfile());
                    $dataRowAsin->setCreatedFromReportSpa($report);

                    $this->setEntityValuesFromReportRecord($dataRowAsin, $record);

                    if ($existingIndex = $dataRowAsin->sameKeyAlreadyExistsInCollection($existingDataAsinRecords)) {
                        if ($this->allowUpdate) {
                            if ($existingDataAsinRecords[$existingIndex]->getCreatedFromReportSpa() !== $report && !method_exists($existingDataAsinRecords[$existingIndex], 'setUpdatedFromReportSpa')) {
                                throw new \InvalidArgumentException("Duplicate record found, but it's from a different report, see $dataClass #$existingIndex");
                            }
                            if ($existingDataAsinRecords[$existingIndex]->isSignificantlyDifferentToRecord($dataRowAsin)) {
                                $dataRowAsin = $existingDataAsinRecords[$existingIndex];
                                $this->logger->info("Updating asin record #{$dataRowAsin->getId()}");
                                $dataRowAsin->setUpdatedFromReportSpa($report);
                                $this->setEntityValuesFromReportRecord($dataRowAsin, $record);
                            } else {
                                // $this->logger->info("Record already exists, not creating a new one");
                                unset($dataRowAsin);
                                continue;
                            }
                        } else {
                            throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataAzSalesTrafficAsin #$existingIndex");
                        }
                    }

                    $this->persist($dataRowAsin);
                    ++$countRecords;

                    if (++$flushQueue > self::FLUSH_QUEUE_LENGTH) {
                        $this->flushChanges();
                        $flushQueue = 0;

                        $this->em->clear(get_class($dataRowAsin));
                    }
                }
            } else {
                throw new \InvalidArgumentException("Cannot parse top level JSON key $sectionKey for Report #{$report->getId()}");
            }
        }

        $report->setParseDateToNow();
        $this->flushChanges();
        $this->em->clear($dataClass);

        return $countRecords;
    }

    /**
     * @param object $entity
     * @param array<string, mixed> $record
     * @return void
     */
    private function setEntityValuesFromReportRecord(object $entity, array $record): void
    {
        foreach ($record as $fieldName => $fieldValue) {
            $setter = 'set' . ucfirst($fieldName);
            if (is_array($fieldValue)) {
                $setter .= 'FromArray';
            }

            if (method_exists($entity, $setter)) {
                $entity->$setter($fieldValue);
            } else {
                throw new \InvalidArgumentException(get_class($entity) . "::{$setter}() does not exist. Source was " . print_r($record, true));
            }
        }
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInvSuppressedListingsReport(AmazonReportSpa $report): int
    {
        return $this->parseBasicReport(DataSpaInvSuppressedListings::class, $report);
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parsePanEuEligibilityReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataClass = DataSpaInvPanEuEligibility::class;
        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = call_user_func_array($dataClass.'::getColumnSettersForData', [$dataArray, $report]);

        $firstDataRow = 1;
        $flushQueue = 0;

        /** @var DataSpaInvPanEuEligibilityRepository $dataRepo */
        $dataRepo = $this->getRepository($dataClass);
        $dataRepo->deleteExistingRecordsForSameProfile($report->getAccountProfile());

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData) || count($rowData) != count($setters)) {
                continue;
            }

            $newRecord = new $dataClass();
            $newRecord->setSpaProfile($report->getAccountProfile());
            $newRecord->setCreatedFromReportSpa($report);
            $newRecord->loadDataFromSourceArray($rowData, $setters);

            $this->persist($newRecord);
            ++$countRecords;

            if (++$flushQueue > self::FLUSH_QUEUE_LENGTH) {
                $this->flushChanges();
                $flushQueue = 0;

                $this->em->clear($dataClass);
            }
        }

        $report->setParseDateToNow();
        $this->flushChanges();
        $this->em->clear($dataClass);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseReferralFeePreviewReport(AmazonReportSpa $report): int
    {
        return $this->parseBasicReport(DataSpaInvReferralFee::class, $report);
    }

    /**
     * @template TDataClass of object
     * @param class-string<TDataClass> $dataClass
     * @param AmazonReportSpa $report
     * @param bool $hasUpdatedFromReportSpaField
     * @return int
     * @throws \Exception
     */
    public function parseBasicReport(string $dataClass, AmazonReportSpa $report, bool $hasUpdatedFromReportSpaField = false): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = call_user_func_array($dataClass.'::getColumnSettersForData', [$dataArray, $report]);

        $firstDataRow = 1;
        $flushQueue = 0;

        /** @var TrackableStatusBaseRepository<TDataClass>|DataSpaInvReferralFeeRepository $dataRepo */
        $dataRepo = $this->getRepository($dataClass);
        /** @var TDataClass[] $existingDataRecords */
        $existingDataRecords = $dataRepo->retrieveExistingRecordsThatCouldBeLinkedToReport($report);

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData) || count($rowData) != count($setters)) {
                continue;
            }

            $newRecord = new $dataClass();
            $newRecord->setSpaProfile($report->getAccountProfile());
            $newRecord->setCreatedFromReportSpa($report);
            $newRecord->loadDataFromSourceArray($rowData, $setters);

            if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                    $existingDataRecords[$existingIndex]->loadDataFromSourceArray($rowData, $setters);
                    if ($hasUpdatedFromReportSpaField) {
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    }
                    ++$countRecords;
                } else {
                    $this->logger->info("Record already exists, not creating a new one");
                }
                unset($newRecord);
                continue;
            }

            $this->persist($newRecord);
            ++$countRecords;

            if (++$flushQueue > self::FLUSH_QUEUE_LENGTH) {
                $this->flushChanges();
                $flushQueue = 0;

                $this->em->clear($dataClass);
            }
        }

        $report->setParseDateToNow();
        $this->flushChanges();
        $this->em->clear($dataClass);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseFbaStrandedInventoryReport(AmazonReportSpa $report): int
    {
        return $this->parseBasicReport(DataSpaFbaStranded::class, $report);
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parsePerformanceSellerFeedbackReport(AmazonReportSpa $report): int
    {
        return $this->parseBasicReport(DataSpaPerfSellerFeedback::class, $report);
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parsePerformanceCouponReport(AmazonReportSpa $report): int
    {
        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);

        $countRecords = 0;

        /** @var DataSpaCouponPerformanceRepository $dataSpaCouponPerformanceRepo */
        $dataSpaCouponPerformanceRepo = $this->getRepository(DataSpaCouponPerformance::class);

        $apiRequestDate = GravitiqTools::castToDateTime($report->getRequestDate());
        $weekEnding = $apiRequestDate->modify('next sunday');

        foreach ($dataArray['coupons'] as $couponData) {
            $existingRecord = $dataSpaCouponPerformanceRepo->retrieveByCouponIdAndWeekEnding($couponData['couponId'], $weekEnding);

            $asins = array_map(function($item) {
                return $item['asin'];
            }, $couponData['asins']);
            $couponData['asins'] = $asins;

            $dataRecord = $existingRecord ?: new DataSpaCouponPerformance();

            if (!$existingRecord) {
                $dataRecord->setSpaProfile($report->getAccountProfile());
                $dataRecord->setCreatedFromReportSpa($report);
            } else {
                $dataRecord->setUpdatedFromReportSpa($report);
            }

            foreach (DataSpaCouponPerformance::REPORT_OBJECT_SETTER_MAP as $dataKey => $setterMethod) {
                if (isset($couponData[$dataKey])) {
                    $dataRecord->$setterMethod($couponData[$dataKey]);
                }
            }

            $dataRecord->setWeekEnding($weekEnding);

            $this->persist($dataRecord);
            $countRecords++;
        }

        $report->setParseDateToNow();
        $this->flushChanges();
        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseOrderItemReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        try {
            $setters = DataSpaOrderItem::getColumnSettersForData($dataArray, $report);
        } catch (\InvalidArgumentException $e) {
            $report->setStatusToNotParsable();
            $report->setParseDate(AmazonSpaBridge::getDoNotParseDateMarker());
            throw $e;
        }

        $firstDataRow = 1;
        $flushQueue = 0;

        $countPerKey = [];
        $newRecords = [];
        $existingPartialKeys = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaOrderItem();
            $newRecords[$flushQueue]
                ->setAccountAmazon($report->getAccountAmazon())
                ->setCreatedFromReportSpa($report)
                ->loadDataFromSourceArray($rowData, $setters)
            ;

            $partialKey = $newRecords[$flushQueue]->getPartialKey();
            if (empty($countPerKey[$partialKey])) {
                $countPerKey[$partialKey] = 1;
            } else {
                ++$countPerKey[$partialKey];
            }

            $newRecords[$flushQueue]->setExtraKey($countPerKey[$partialKey]);
            $existingPartialKeys[$partialKey] = true;
            ++$flushQueue;

            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaOrderItem($report, $newRecords, $existingPartialKeys);

                $flushQueue = 0;
                $newRecords = [];
                $existingPartialKeys = [];
            }
        }


        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaOrderItem($report, $newRecords, $existingPartialKeys);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaOrderItem[] $newRecords
     * @param array<string, true> $existingPartialKeys
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaOrderItem(AmazonReportSpa $report, array $newRecords, array $existingPartialKeys): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        /** @var DataSpaOrderItemRepository $dataRepo */
        $dataRepo = $this->getRepository(DataSpaOrderItem::class);

        $existingDataRecords = $dataRepo->retrieveByProfileAndPartialKeys($report->getAccountAmazon(), array_keys($existingPartialKeys));

        foreach ($newRecords as $newRecord) {
            if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                $recordId = $existingDataRecords[$existingIndex]->getId();
                if (!$this->allowUpdate) {
                    throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaOrderItem #$recordId");
                }

                if (!GravitiqTools::firstDateIsSameOrAfterSecondDate($newRecord->getLastUpdatedDate(), $existingDataRecords[$existingIndex]->getLastUpdatedDate())) {
                    $this->logger->info("Record already exists for this order+sku+extra ($recordId), but it was updated more recently than the one in hand. Discarding new one.");
                    continue;
                }
                if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                    $this->logger->info("Older record exists, updating it with newer info");
                    $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                    $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    ++$countRecords;
                } else {
                    // $this->logger->info("Discarding record which already exists");
                }
                continue;
            }

            $this->persist($newRecord);
            ++$countRecords;
        }

        $this->flushChanges();
        $this->em->clear(DataSpaOrderItem::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryListingsAllReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvListingsAll::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvListingsAll();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            if (!$newRecords[$flushQueue]->isInteresting()) {
                unset($newRecords[$flushQueue]);
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvListingsAll($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvListingsAll($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryListingsActiveReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $firstDataRow = 1;
        $flushQueue = 0;
        $newRecords = [];

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $recordClass = DataSpaInvListingsActive::class;
        $notYetImplementedFields = DataSpaInvListingsActive::getArrayParseNotYetImplementedFields();
        foreach ($notYetImplementedFields as $fieldKey => $expectedValue) {
            $index = array_search($fieldKey, $dataArray[0]);
            if ($index !== false) {
                foreach ($dataArray as $rowNum => $actualValue) {
                    if ($rowNum < $firstDataRow) {
                        continue;
                    }
                    if (!array_key_exists($index, $actualValue)) {
                        throw new \InvalidArgumentException("WARNING: $recordClass from request {$report->getId()} is missing $index column in " . print_r($actualValue, true));
                    }
                    if ($actualValue[$index] !== $expectedValue) {
                        throw new \InvalidArgumentException("WARNING: $recordClass from request {$report->getId()} contains data for $fieldKey but this is not yet parsed " . print_r($dataArray, true));
                    }
                    unset($dataArray[$rowNum][$index]);
                }
                unset($dataArray[0][$index]);
            }
        }

        $setters = DataSpaInvListingsActive::getColumnSettersForData($dataArray, $report);

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvListingsActive();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            if (!$newRecords[$flushQueue]->isInteresting()) {
                unset($newRecords[$flushQueue]);
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvListingsActive($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvListingsActive($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvListingsActive[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvListingsActive(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;

        if (count($newRecords)) {
            /** @var DataSpaInvListingsActiveRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvListingsActive::class);

            $newRecordSkus = [];
            foreach ($newRecords as $newRecord) {
                $newRecordSkus[] = $newRecord->getSku();
            }

            /** @var DataSpaInvListingsActive[] $existingDataRecords */
            $existingDataRecords = $dataRepo->retrieveByProfileAndSkusValidOnDate($report->getAccountProfile(), $newRecordSkus, $report->getStartDateOrRequestDate());

            $countRecords = $this->manageValidityDates($newRecords, $existingDataRecords, $dataRepo, $report, $countRecords);
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvListingsActive::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryListingsInactiveReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvListingsInactive::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvListingsInactive();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            if (!$newRecords[$flushQueue]->isInteresting()) {
                unset($newRecords[$flushQueue]);
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvListingsInactive($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvListingsInactive($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvListingsInactive[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvListingsInactive(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;

        if (count($newRecords)) {
            /** @var DataSpaInvListingsInactiveRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvListingsInactive::class);

            $newRecordSkus = [];
            foreach ($newRecords as $newRecord) {
                $newRecordSkus[] = $newRecord->getSku();
            }

            /** @var DataSpaInvListingsInactive[] $existingDataRecords */
            $existingDataRecords = $dataRepo->retrieveByProfileAndSkusValidOnDate($report->getAccountProfile(), $newRecordSkus, $report->getStartDateOrRequestDate());

            $countRecords = $this->manageValidityDates($newRecords, $existingDataRecords, $dataRepo, $report, $countRecords);
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvListingsInactive::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryHealthReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $snapshotDate = null;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvHealth::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvHealth();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            if (empty($snapshotDate)) {
                $snapshotDate = $newRecords[$flushQueue]->getSnapshotDate();
            } else {
                if (GravitiqTools::daysBetweenDates($snapshotDate, $newRecords[$flushQueue]->getSnapshotDate()) !== 0) {
                    throw new \InvalidArgumentException('ERROR: Records in the Inventory Health Report have different snapshot dates');
                }
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvHealth($report, $newRecords, $snapshotDate);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvHealth($report, $newRecords, $snapshotDate);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseRemovalShipmentReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaFbaRemovalShipment::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $this->logger->debug("Skipping row $rowNum");
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaFbaRemovalShipment();
            $newRecords[$flushQueue]->setAccountAmazon($report->getAccountAmazon());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaFbaRemovalShipment($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaFbaRemovalShipment($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param list<DataSpaFbaRemovalShipment> $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaFbaRemovalShipment(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;

        // Summarize shipped-quantities for identical rows
        $summarizedRecords = [];
        foreach ($newRecords as $record) {
            $key = $record->buildKey();
            if (!isset($summarizedRecords[$key])) {
                $summarizedRecords[$key] = clone $record; // Clone to avoid altering original objects
            } else {
                $existingShippedQuantity = $summarizedRecords[$key]->getShippedQuantity();
                $additionalShippedQuantity = $record->getShippedQuantity();
                $summarizedRecords[$key]->setShippedQuantity($existingShippedQuantity + $additionalShippedQuantity);
            }
        }

        /** @var DataSpaFbaRemovalShipmentRepository $dataRepo */
        $dataRepo = $this->getRepository(DataSpaFbaRemovalShipment::class);
        $existingDataRecords = count($summarizedRecords) ? $dataRepo->retrievePossibleMatchesForReport($report) : [];

        foreach ($summarizedRecords as $summarizedRecord) {
            $existingIndex = $summarizedRecord->sameKeyAlreadyExistsInCollection($existingDataRecords);
            if ($existingIndex) {
                if ($this->allowUpdate && $existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($summarizedRecord)) {
                    $existingDataRecords[$existingIndex]->copyDataFromObject($summarizedRecord);
                    $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    $this->persist($existingDataRecords[$existingIndex]);
                    $countRecords++;
                }
            } else {
                $this->persist($summarizedRecord);
                $countRecords++;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaFbaRemovalShipment::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryPlanningReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvPlanning::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];
        $snapshotDates = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvPlanning();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            $newSnapshotDate = $newRecords[$flushQueue]->getSnapshotDate();
            $dateKey = $newSnapshotDate->format('Y-m-d');
            if (empty($snapshotDates[$dateKey])) {
                $snapshotDates[$dateKey] = $newSnapshotDate;
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvPlanning($report, $newRecords, $snapshotDates);

                $flushQueue = 0;
                $newRecords = [];
                $snapshotDates = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvPlanning($report, $newRecords, $snapshotDates);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseRemovalOrderReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaFbaRemovalOrder::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $this->logger->debug("Skipping row $rowNum");
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaFbaRemovalOrder();
            $newRecords[$flushQueue]->setAccountAmazon($report->getAccountAmazon());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaFbaRemovalOrder($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaFbaRemovalOrder($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseManualPaymentTransactionsReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $flushQueue = 0;
        $firstDataRow = 1;

        $dataIterator = $this->getDataIteratorFromEntityFile($report);

        $newRecords = [];

        foreach ($dataIterator as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $setters = DataSpaPayment::getColumnSettersForData([$rowData], $report);
                continue;
            }
            if (empty($rowData)) {
                continue;
            }
            if (empty($setters)) {
                throw new \InvalidArgumentException('Setters not initialised');
            }

            $newRecords[$flushQueue] = new DataSpaPayment();
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);
            if ($newRecords[$flushQueue]->isEmpty()) {
                unset($newRecords[$flushQueue]);
                continue;
            }
            $newRecords[$flushQueue]->setSpaProfile($this->retrieveSpaProfileFromAlias($newRecords[$flushQueue]->getProfileAlias()));

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaPayment($newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaPayment($newRecords);
        $report->setRecordCount($countRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvListingsAll[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvListingsAll(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvListingsAllRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvListingsAll::class);

            $newRecordSkus = [];
            foreach ($newRecords as $newRecord) {
                $newRecordSkus[] = $newRecord->getSku();
            }

            /** @var DataSpaInvListingsAll[] $existingDataRecords */
            $existingDataRecords = $dataRepo->retrieveByProfileAndSkusValidOnDate($report->getAccountProfile(), $newRecordSkus, $report->getStartDateOrRequestDate());

            $countRecords = $this->manageValidityDates($newRecords, $existingDataRecords, $dataRepo, $report, $countRecords);
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvListingsAll::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvLedgerSummary[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvLedgerSummary(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;
        $existingDataRecords = [];

        $dataClass = DataSpaInvLedgerSummary::class;
        if ($report->isLedgerInvSummaryReportLocAggregatedByFc()) {
            $dataClass = DataSpaInvLedgerSummaryFc::class;
        }

        if (count($newRecords)) {
            /** @var DataSpaInvLedgerSummaryRepository|DataSpaInvLedgerSummaryFcRepository $dataRepo */
            $dataRepo = $this->getRepository($dataClass);

            $startDatesInReport = [];
            foreach ($newRecords as $newRecord) {
                $startDatesInReport[$newRecord->getStartDate()->format('Y-m-d')] = $newRecord->getStartDate();
            }
            $startDatesInReport = array_keys($startDatesInReport);

            $firstNewRecord = reset($newRecords);
            $reportPeriod = $firstNewRecord->getReportPeriod(); // should be the same for all records in a single Report

            $existingDataRecords = $dataRepo->retrieveByStartDateAndReportPeriod($report->getAccountAmazon(), $startDatesInReport, $reportPeriod);
        }
        foreach ($newRecords as $newRecord) {
            if ($existingIndex  = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                if ($existingDataRecords[$existingIndex]->getLatestLinkedReport()->getRequestDate() >= $newRecord->getLatestLinkedReport()->getRequestDate()) {
                    $this->logger->info('Existing record is newer, skipping update');
                    continue;
                }

                if (!$this->allowUpdate) {
                    $recordId = $existingDataRecords[$existingIndex]->getId();
                    throw new \InvalidArgumentException("ERROR: Record already exists with same key: $recordId");
                }

                if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                    $this->logger->info('Differing invLedgerSummary record exists, updating it with newer info.');
                    $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                    $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    ++$countRecords;
                }
                continue;
            }
            $this->persist($newRecord);
            ++$countRecords;
        }

        $this->flushChanges();
        $this->em->clear($dataClass);

        return $countRecords;
    }

    /**
     * @param DataSpaInvLedgerDetail[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvLedgerDetail(array $newRecords): int
    {
        $countRecords = 0;
        $datesProcessed = [];

        $uniqueDates = array_unique(array_map(function ($record) {
            return $record->getEventDateTime()->format('Y-m-d');
        }, $newRecords));

        while (!empty(array_diff($uniqueDates, $datesProcessed))) {
            foreach ($uniqueDates as $currentDate) {
                if (in_array($currentDate, $datesProcessed)) {
                    continue;
                }

                $datesProcessed[] = $currentDate;

                $recordsForCurrentDate = array_filter($newRecords, function ($record) use ($currentDate) {
                    return $record->getEventDateTime()->format('Y-m-d') === $currentDate;
                });

                $countRecords += $this->_processDataSpaInvLedgerDetailAndSave($currentDate, $recordsForCurrentDate);
            }
        }

        return $countRecords;
    }

    /**
     * @param string $currentDate
     * @param DataSpaInvLedgerDetail[] $recordsForCurrentDate
     * @return int
     */
    private function _processDataSpaInvLedgerDetailAndSave(string $currentDate, array $recordsForCurrentDate): int
    {
        $this->logger->info("Processing date: $currentDate (" . count($recordsForCurrentDate) . ' rows)');

        $countRecords = 0;
        /** @var DataSpaInvLedgerDetail[][] $recordMap */
        $recordMap = [];

        foreach ($recordsForCurrentDate as $newRecord) {
            $buildKey = $newRecord->buildKey();
            $recordMap[$buildKey][] = $newRecord;
        }

        foreach ($recordMap as $records) {
            $totalQuantity = array_sum(array_map(function($record) {
                return $record->getQuantity();
            }, $records));

            $representativeRecord = clone $records[0];

            try {
                /** @var DataSpaInvLedgerDetailRepository $ledgerDetailRepo */
                $ledgerDetailRepo = $this->getRepository(DataSpaInvLedgerDetail::class);
                $existingRecord = $ledgerDetailRepo->retrieveExistingDataSpaInvLedgerDetail($representativeRecord);
            } catch (NonUniqueResultException $e) {
                throw new \InvalidArgumentException('ERROR: Multiple records found for the same key: ' . $representativeRecord->buildKey(), 0, $e);
            }

            if ($existingRecord) {
                if ($representativeRecord->getCreatedFromReportSpa()->getRequestDate() >= $existingRecord->getCreatedFromReportSpa()->getRequestDate()) {
                    $existingRecord->setQuantity($totalQuantity);
                    $existingRecord->setSourceRowCount(count($records));
                    $existingRecord->setTitle($representativeRecord->getTitle());
                    $existingRecord->setSku($representativeRecord->getSku());
                    $existingRecord->setUpdatedFromReportSpa($representativeRecord->getCreatedFromReportSpa());
                    $this->persist($existingRecord);
                }
            } else {
                $representativeRecord->setQuantity($totalQuantity);
                $representativeRecord->setSourceRowCount(count($records));
                $this->persist($representativeRecord);
            }
            ++$countRecords;
        }

        try {
            $this->flushChanges();
            $this->em->clear(DataSpaInvLedgerDetailSku::class);
            $this->em->clear(DataSpaInvLedgerDetail::class);
        } catch (MappingException $e) {
            throw new \InvalidArgumentException('ERROR: Could not flush and clear EntityManager in _processDataSpaInvLedgerDetailAndSave', 0, $e);
        }

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvHealth[] $newRecords
     * @param ?\DateTimeInterface $snapshotDate
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvHealth(AmazonReportSpa $report, array $newRecords, ?\DateTimeInterface $snapshotDate): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvHealthRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvHealth::class);

            /** @var DataSpaInvHealth[] $existingDataRecords */
            $existingDataRecords = $dataRepo->retrieveByProfileAndSnapshotDate($report->getAccountProfile(), $snapshotDate);

            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaInvHealth #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaInvHealth record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    } else {
                        // $this->logger->info("Discarding record which already exists");
                    }
                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvHealth::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvPlanning[] $newRecords
     * @param \DateTimeInterface[] $snapshotDate
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvPlanning(AmazonReportSpa $report, array $newRecords, array $snapshotDate): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvPlanningRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvPlanning::class);

            $existingDataRecords = $dataRepo->retrieveByProfileAndSnapshotDate($report->getAccountProfile(), $snapshotDate);

            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaInvPlanning #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaInvPlanning record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    } else {
                        // $this->logger->info("Discarding record which already exists");
                    }
                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvPlanning::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaFbaRemovalOrder[] $newRecords
     * @return int
     * @throws \Exception
     */
    public function _flushQueueDataSpaFbaRemovalOrder(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        /** @var DataSpaFbaRemovalOrderRepository $dataRepo */
        $dataRepo = $this->getRepository(DataSpaFbaRemovalOrder::class);
        if (count($newRecords)) {
            $existingDataRecords = $dataRepo->retrievePossibleMatchesForReport($report);
        } else {
            $existingDataRecords = [];
        }

        foreach ($newRecords as $newRecord) {
            if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                if (!$this->allowUpdate) {
                    $recordId = $existingDataRecords[$existingIndex]->getId();
                    throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaFbaReturn #$recordId");
                }

                if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                    $this->logger->info("Differing spaFbaRemoval record exists, updating it with newer info");
                    $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                    $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    ++$countRecords;
                } else {
                    // $this->logger->info("Discarding record which already exists");
                }
                continue;
            }

            $this->persist($newRecord);
            ++$countRecords;
        }

        $this->flushChanges();
        $this->em->clear(DataSpaFbaRemovalOrder::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseCustomerReturnsReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaFbaReturn::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $this->logger->debug("Skipping row $rowNum");
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaFbaReturn();
            $newRecords[$flushQueue]->setAccountAmazon($report->getAccountAmazon());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaFbaReturn($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaFbaReturn($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaFbaReturn[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaFbaReturn(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        /** @var DataSpaFbaReturnRepository $dataRepo */
        $dataRepo = $this->getRepository(DataSpaFbaReturn::class);
        if (count($newRecords)) {
            $existingDataRecords = $dataRepo->retrievePossibleMatchesForReport($report);
        } else {
            $existingDataRecords = [];
        }

        foreach ($newRecords as $newRecord) {
            if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                if (!$this->allowUpdate) {
                    $recordId = $existingDataRecords[$existingIndex]->getId();
                    throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaFbaReturn #$recordId");
                }

                if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                    $this->logger->info("Differing spaFbaReturn record exists, updating it with newer info");
                    $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                    $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                    ++$countRecords;
                } else {
                    // $this->logger->info("Discarding record which already exists");
                }
                continue;
            }

            $this->persist($newRecord);
            ++$countRecords;
        }

        $this->flushChanges();
        $this->em->clear(DataSpaFbaReturn::class);
        $dataRepo->updateLinkedDateFields($this->em, $this->logger);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryManagedAllReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report, false, self::TSV_ENCLOSURE_CHARACTER_NONE_EXPECTED);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvManagedAll::getColumnSettersForData($dataArray, $report);

        $firstDataRow = 1;
        $flushQueue = 0;
        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvManagedAll();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvManagedAll($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvManagedAll($report, $newRecords);

        return $countRecords;
    }

    /**
     * Note that this report has been deprecated by Amazon and is no longer available.
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryAgeReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $snapshotDate = null;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvAge::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvAge();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            if (empty($snapshotDate)) {
                $snapshotDate = $newRecords[$flushQueue]->getSnapshotDate();
            } else {
                if (GravitiqTools::daysBetweenDates($snapshotDate, $newRecords[$flushQueue]->getSnapshotDate()) !== 0) {
                    throw new \InvalidArgumentException('ERROR: Records in the Inventory Age Report have different snapshot dates');
                }
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvAge($report, $newRecords, $snapshotDate);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvAge($report, $newRecords, $snapshotDate);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvAge[] $newRecords
     * @param ?\DateTimeInterface $snapshotDate
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvAge(AmazonReportSpa $report, array $newRecords, ?\DateTimeInterface $snapshotDate): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvAgeRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvAge::class);

            $existingDataRecords = $dataRepo->retrieveByProfileAndSnapshotDate($report->getAccountProfile(), $snapshotDate);

            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaInvAge #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaInvAge record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    } else {
                        // $this->logger->info("Discarding record which already exists");
                    }
                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvAge::class);

        return $countRecords;
    }
    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvManagedAll[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvManagedAll(AmazonReportSpa $report, array $newRecords): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvManagedAllRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvManagedAll::class);

            $existingDataRecords = $dataRepo->retrieveByProfileAndDate($report->getAccountProfile(), $report->getStartDateOrRequestDate());
            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see DataSpaInvManagedAll #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaInvManagedAll record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    } else {
                        // $this->logger->info("Discarding record which already exists");
                    }
                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvManagedAll::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseFbaFeePreviewReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvFbaFeePreview::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];
        $newRecordSkus = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new DataSpaInvFbaFeePreview();
            $newRecords[$flushQueue]
                ->setAccountAmazon($report->getAccountAmazon())
                ->setCreatedFromReportSpa($report)
                ->loadDataFromSourceArray($rowData, $setters)
                ->calculateMarketplace()
                ->calculateExpectedFulfillmentFeePerUnit()
            ;
            $newRecordSkus[$flushQueue] = $newRecords[$flushQueue]->getSku();

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvFbaFeePreview($report, $newRecords, $newRecordSkus);

                $flushQueue = 0;
                $newRecords = [];
                $newRecordSkus = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvFbaFeePreview($report, $newRecords, $newRecordSkus);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryLedgerSummaryReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);

        $entityToUse = DataSpaInvLedgerSummary::class;
        if ($report->isLedgerInvSummaryReportLocAggregatedByFc()) {
            $entityToUse = DataSpaInvLedgerSummaryFc::class;
        }

        $setters = $entityToUse::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecords[$flushQueue] = new $entityToUse();
            $newRecords[$flushQueue]->setSpaProfile($report->getAccountProfile());
            $newRecords[$flushQueue]->setAccountAmazon($report->getAccountAmazon());
            $newRecords[$flushQueue]->setCreatedFromReportSpa($report);
            $newRecords[$flushQueue]->loadDataFromSourceArray($rowData, $setters);

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaInvLedgerSummary($report, $newRecords);

                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvLedgerSummary($report, $newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseInventoryLedgerDetailReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = DataSpaInvLedgerDetail::getColumnSettersForData($dataArray, $report);
        $firstDataRow = 1;

        $flushQueue = 0;

        $newRecords = [];

        $lastDateProcessed = null;
        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                continue;
            }
            if (empty($rowData)) {
                continue;
            }

            $newRecord = new DataSpaInvLedgerDetail();
            $newRecord->setSpaProfile($report->getAccountProfile());
            $newRecord->setAccountAmazon($report->getAccountAmazon());
            $newRecord->setCreatedFromReportSpa($report);
            $newRecord->loadDataFromSourceArray($rowData, $setters);
            $recordDate = $newRecord->getEventDateTime()->format('Y-m-d');
            ++$flushQueue;

            if (is_null($lastDateProcessed)) {
                $lastDateProcessed = $recordDate;
            } elseif ($lastDateProcessed !== $recordDate) {
                $countRecords += $this->_flushQueueDataSpaInvLedgerDetail($newRecords);
                $flushQueue = 0;
                $newRecords = [];
                $lastDateProcessed = $recordDate;
            }
            $newRecords[$flushQueue] = $newRecord;
        }

        $report->setParseDateToNow();
        $countRecords += $this->_flushQueueDataSpaInvLedgerDetail($newRecords);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaInvFbaFeePreview[] $newRecords
     * @param string[] $newRecordSkus
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaInvFbaFeePreview(AmazonReportSpa $report, array $newRecords, array $newRecordSkus): int
    {
        $countRecords = 0;
        // $this->logger->info('Flushing queue...');

        if (count($newRecords)) {
            /** @var DataSpaInvFbaFeePreviewRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaInvFbaFeePreview::class);

            $existingDataRecords = $dataRepo->retrieveByAccountSkusAndReportDate($report->getAccountAmazon(), $newRecordSkus, $report->getStartDateOrRequestDate());

            $countRecords = $this->manageValidityDates($newRecords, $existingDataRecords, $dataRepo, $report, $countRecords);
        }

        $this->flushChanges();
        $this->em->clear(DataSpaInvFbaFeePreview::class);

        return $countRecords;
    }

    /**
     * @param DataSpaPayment[] $newRecords
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaPayment(array $newRecords): int
    {
        $countRecords = 0;
        $sqlHeader = /** @lang text - prevents warning about partial statement */ <<<SQL
            INSERT INTO `data_spa_payment` (`id`, `spaProfileId`, `reportCreateId`, `brand`, `market`, `eventDate`, `settlementId`, `eventType`, `orderId`, `sku`, `quantity`, `marketplace`, `accountType`, `fulfillment`, `orderCity`, `orderState`, `orderPostal`, `taxCollectionModel`, `productSales`, `productSalesTax`, `shippingCredits`, `shippingCreditsTax`, `giftWrapCredits`, `giftWrapCreditsTax`, `regulatoryFee`, `taxOnRegulatoryFee`, `promotionalRebates`, `promotionalRebatesTax`, `marketplaceWithheldTax`, `sellingFees`, `fbaFees`, `otherTransactionFees`, `other`, `total`, `lowValueGoods`, `description`) 
            VALUES 
        SQL;
        $sqlRows = [];

        foreach ($newRecords as $newRecord) {
            ++$countRecords;
            $sqlRows[] = $newRecord->getInsertionSql();
        }

        if (!empty($sqlRows)) {
            $sql = $sqlHeader . implode(',', $sqlRows);
            $this->em->getConnection()->executeQuery($sql);
        }

        $this->em->clear(DataSpaPayment::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseXmlBrowseTreeData(AmazonReportSpa $report): int
    {
        try {
            $fileContentString = $this->readFileFromFileSystem($report);
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'NoSuchKey')) {
                $report->resetToReDownload();
                $this->flushChanges();
                $this->logger->warning("Entity #{$report->getId()} had no data file (B). It has been reset to enable re-download.");
                $this->logger->warning($e->getMessage());
                return 0;
            } else {
                throw $e;
            }
        }

        if (str_ends_with($report->getFilename(), AmazonSpaBridge::FILE_EXTENSION_GZIP)) {
            $fileContentString = gzdecode($fileContentString);
        }

        $xmlData = simplexml_load_string($fileContentString);
        $dataArray = [];

        foreach ($xmlData->xpath('//Node') as $node) {
            $browseNodeId = (string)$node->browseNodeId;
            if (!isset($dataArray[$browseNodeId])) {
                $dataArray[$browseNodeId] = [
                    'browseNodeId' => $browseNodeId,
                    'browseNodeName' => (string)$node->browseNodeName,
                    'browsePathById' => (string)$node->browsePathById,
                    'browsePathByName' => (string)$node->browsePathByName,
                    'hasChildren' => (string)$node->hasChildren,
                ];
            }
        }

        $countRecords = 0;
        $batchSize = 1000;

        /** @var DataSpaBrowseTreeNodeRepository $repo */
        $repo = $this->getRepository(DataSpaBrowseTreeNode::class);

        $batches = array_chunk($dataArray, $batchSize, true);
        foreach ($batches as $batch) {
            $batchNodeIds = array_keys($batch);
            $existingBrowseNodes = $repo->retrieveByBrowseNodeIds($batchNodeIds);
            foreach ($batch as $nodeId => $rowData) {
                if (!isset($existingBrowseNodes[$nodeId])) {
                    $existingBrowseNodes[$nodeId] = new DataSpaBrowseTreeNode();
                }
                $existingBrowseNodes[$nodeId]->setCountryCode($report->getAccountProfile()->getCountryCode());
                $existingBrowseNodes[$nodeId]->setBrowseNodeId($rowData['browseNodeId']);
                $existingBrowseNodes[$nodeId]->setBrowseNodeName($rowData['browseNodeName']);
                $existingBrowseNodes[$nodeId]->setBrowsePathById($rowData['browsePathById']);
                $existingBrowseNodes[$nodeId]->setRootNodeIdFromBrowsePathById();
                $existingBrowseNodes[$nodeId]->setBrowsePathByName($rowData['browsePathByName']);
                $existingBrowseNodes[$nodeId]->setHasChildrenFromString($rowData['hasChildren']);

                $this->persist($existingBrowseNodes[$nodeId]);
                ++$countRecords;
            }

            $this->flushChanges();
            $this->em->clear(DataSpaBrowseTreeNode::class);
        }

        $report->setParseDateToNow();
        $report->setRecordCount($countRecords);
        $report->setStatusToDone();
        $this->flushChanges();

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     * @throws \Exception
     */
    public function parseFbaSnsForecastReport(AmazonReportSpa $report): int
    {
        $recordClass = DataSpaFbaSnsForecast::class;
        $countRecords = 0;
        $snapshotDate = [];

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = $recordClass::getColumnSettersForData($dataArray, $report);

        $newRecords = [];
        $firstDataRow = 1;
        $flushQueue = 0;
        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $this->logger->debug("Skipping row $rowNum");
                continue;
            }
            if (empty($rowData)) {
                continue;
            }
            if (count(array_unique($rowData)) === 1 && $rowData[0] === "") {
                $this->logger->debug("All elements are empty. Skipping...");
                continue;
            }

            $recordForFirstWeek = new $recordClass();
            $recordForFirstWeek->setAccountAmazon($report->getAccountAmazon());
            $recordForFirstWeek->setCreatedFromReportSpa($report);
            $recordForFirstWeek->loadDataFromSourceArray($rowData, $setters);

            if (empty($snapshotDate)) {
                $snapshotDate = $recordForFirstWeek->getSnapshotDate();
            } elseif (is_array($snapshotDate)) {
                $foundMatch = false;
                foreach ($snapshotDate as $singleDate) {
                    if (GravitiqTools::daysBetweenDates($singleDate, $recordForFirstWeek->getSnapshotDate()) == 0) {
                        $foundMatch = true;
                        break;
                    }
                }
                if (!$foundMatch) {
                    throw new \InvalidArgumentException('ERROR: Subscribe and save records have more than two different snapshot dates');
                }
            } elseif (GravitiqTools::daysBetweenDates($snapshotDate, $recordForFirstWeek->getSnapshotDate()) !== 0) {
                $snapshotDate = [
                    $snapshotDate,
                    $recordForFirstWeek->getSnapshotDate()
                ];
            }
            $newRecords[$flushQueue] = $recordForFirstWeek;

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaFbaSnsForecastReport($report, $newRecords, $snapshotDate);
                $flushQueue = 0;
                $newRecords = [];
            }
        }

        $report->setParseDate(new \DateTime());
        $countRecords += $this->_flushQueueDataSpaFbaSnsForecastReport($report, $newRecords, $snapshotDate);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaFbaSnsForecast[]|DataSpaFbaSnsPerformance[] $newRecords
     * @param \DateTimeInterface|\DateTimeInterface[] $snapshotDate
     * @return int
     * @throws \Exception
     */
    private function _flushQueueDataSpaFbaSnsForecastReport(AmazonReportSpa $report, array $newRecords, \DateTimeInterface|array $snapshotDate): int
    {
        $recordClass = DataSpaFbaSnsForecast::class;
        $countRecords = 0;

        if (count($newRecords)) {
            if (!($newRecords[0] instanceof DataSpaFbaSnsForecast)) {
                throw new \InvalidArgumentException('ERROR: Invalid class type passed to _flushQueueDataSpaFbaSnsForecastReport');
            }

            /** @var DataSpaFbaSnsForecastRepository $dataRepo */
            $dataRepo = $this->getRepository($recordClass);
            $existingDataRecords = $dataRepo->retrieveByAccountAndSnapshotDate($report->getAccountAmazon(), $snapshotDate);

            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see $recordClass #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaFbaSnsReport record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    }

                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
            }
        }

        $this->flushChanges();
        $this->em->clear(DataSpaFbaSnsForecast::class);

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @return int
     */
    public function parseFbaSnsPerformanceReport(AmazonReportSpa $report): int
    {
        $recordClass = DataSpaFbaSnsPerformance::class;
        $countRecords = 0;
        $earliestRecordDate = $latestRecordDate = null;

        try {
            $dataArray = $this->getDataArrayFromEntityFile($report);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException("Error reading file for report #{$report->getId()}: " . $e->getMessage());
        }
        $this->checkDataForErrorMessage($report, $dataArray);
        $setters = $recordClass::getColumnSettersForData($dataArray, $report);

        $newRecords = [];
        $firstDataRow = 1;
        $flushQueue = 0;
        foreach ($dataArray as $rowNum => $rowData) {
            if ($rowNum < $firstDataRow) {
                $this->logger->debug("Skipping row $rowNum");
                continue;
            }
            if (empty($rowData)) {
                continue;
            }
            if (count(array_unique($rowData)) === 1 && $rowData[0] === "") {
                $this->logger->debug("All elements are empty. Skipping...");
                continue;
            }

            $recordForFirstWeek = new $recordClass();
            $recordForFirstWeek->setAccountAmazon($report->getAccountAmazon());
            $recordForFirstWeek->setCreatedFromReportSpa($report);
            $recordForFirstWeek->loadDataFromSourceArray($rowData, $setters);

            if (is_null($earliestRecordDate) || $recordForFirstWeek->getWeekStartDate() < $earliestRecordDate) {
                $earliestRecordDate = $recordForFirstWeek->getWeekStartDate();
            }
            if (is_null($latestRecordDate) || $recordForFirstWeek->getWeekStartDate() > $latestRecordDate) {
                $latestRecordDate = $recordForFirstWeek->getWeekStartDate();
            }

            $newRecords[$flushQueue] = $recordForFirstWeek;

            $newWeekStartDate = $recordForFirstWeek->getWeekStartDate();
            $colNum = 11; // Remaining data starts at the 12th column
            for ($i = 2; $i <= 4; $i++) {
                try {
                    $newWeekStartDate = $newWeekStartDate->modify('-1 week');
                } catch (\Exception $e) {
                    throw new \InvalidArgumentException("Error modifying date: " . $e->getMessage());
                }
                $recordForLoopWeek = clone $recordForFirstWeek;
                $recordForLoopWeek->setWeekStartDate($newWeekStartDate);
                // Dynamic key construction with the loop variable
                $recordForLoopWeek->setSnsUnitsShipped($rowData[++$colNum] ?? null);
                $recordForLoopWeek->setOosRate($rowData[++$colNum] ?? null);
                $recordForLoopWeek->setSnsSalePriceFromDecimal($rowData[++$colNum] ?? null);
                $recordForLoopWeek->setSnsDiscount($rowData[++$colNum] ?? null);
                $newRecords[++$flushQueue] = $recordForLoopWeek;
            }
            if (is_null($earliestRecordDate) || $newRecords[$flushQueue]->getWeekStartDate() < $earliestRecordDate) {
                $earliestRecordDate = $newRecords[$flushQueue]->getWeekStartDate();
            }

            ++$flushQueue;
            if ($flushQueue > 1000) {
                $countRecords += $this->_flushQueueDataSpaFbaSnsPerformanceReport($report, $newRecords, $earliestRecordDate, $latestRecordDate);
                $flushQueue = 0;
                $newRecords = [];
                $earliestRecordDate = $latestRecordDate = null;
            }
        }

        if (!is_null($earliestRecordDate)) {
            $countRecords += $this->_flushQueueDataSpaFbaSnsPerformanceReport($report, $newRecords, $earliestRecordDate, $latestRecordDate);
        }
        $report->setParseDate(new \DateTime());

        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param DataSpaFbaSnsPerformance[] $newRecords
     * @param \DateTimeInterface $minWeekStartDate
     * @param ?\DateTimeInterface $maxWeekStartDate
     * @return int
     */
    private function _flushQueueDataSpaFbaSnsPerformanceReport(AmazonReportSpa $report, array $newRecords, \DateTimeInterface $minWeekStartDate, ?\DateTimeInterface $maxWeekStartDate): int
    {
        $recordClass = DataSpaFbaSnsPerformance::class;
        $countRecords = 0;

        if (count($newRecords)) {
            /** @var DataSpaFbaSnsPerformanceRepository $dataRepo */
            $dataRepo = $this->getRepository(DataSpaFbaSnsPerformance::class);

            $existingDataRecords = $dataRepo->retrieveByAccountInDateRange($report->getAccountAmazon(), $minWeekStartDate, $maxWeekStartDate);

            foreach ($newRecords as $newRecord) {
                if ($existingIndex = $newRecord->sameKeyAlreadyExistsInCollection($existingDataRecords)) {
                    if ($existingDataRecords[$existingIndex]->getSnapshotDate() > $newRecord->getSnapshotDate()) {
                        $this->logger->info("Existing record is newer than the one being processed. Skipping...");
                        continue;
                    }

                    if (!$this->allowUpdate) {
                        $recordId = $existingDataRecords[$existingIndex]->getId();
                        throw new \InvalidArgumentException("Report #{$report->getId()} contains at least one record that has already been saved, see $recordClass #$recordId");
                    }

                    if ($existingDataRecords[$existingIndex]->isSignificantlyDifferentToRecord($newRecord)) {
                        $this->logger->info("Differing spaFbaSnsReport record exists, updating it with newer info");
                        $existingDataRecords[$existingIndex]->copyDataFromObject($newRecord);
                        $existingDataRecords[$existingIndex]->setUpdatedFromReportSpa($report);
                        ++$countRecords;
                    }

                    continue;
                }

                $this->persist($newRecord);
                ++$countRecords;
                $newKey = 'new' . $countRecords;
                $existingDataRecords[$newKey] = $newRecord;
            }
        }

        $this->flushChanges();
        try {
            $this->em->clear(DataSpaFbaSnsPerformance::class);
        } catch (MappingException $e) {
            throw new \RuntimeException("MappingException when clearing DataSpaFbaSnsPerformance: " . $e->getMessage(), 0, $e);
        }

        return $countRecords;
    }

    /**
     * @throws \Exception
     */
    public function parseReservedInventoryReport(AmazonReportSpa $report): int
    {
        return $this->parseBasicReport(DataSpaReservedInventory::class, $report, true);
    }

    /**
     * @throws \Exception
     */
    public function parsePromotionPerformanceReport(AmazonReportSpa $report): int
    {
        $countRecords = 0;
        $classesToClearOnFlush = [
            DataSpaPromotionPerformance::class,
            DataSpaPromotionProduct::class,
        ];

        $dataArray = $this->getDataArrayFromEntityFile($report);
        $this->checkDataForErrorMessage($report, $dataArray);

        if (empty($dataArray) || empty($dataArray['promotions'])) {
            $promotionDataArray = [];
        } else {
            $promotionDataArray = $dataArray['promotions'];
        }

        /** @var DataSpaPromotionPerformanceRepository $promotionRepo */
        $promotionRepo = $this->getRepository(DataSpaPromotionPerformance::class);
        foreach ($promotionDataArray as $promotionData) {
            $existingPromotion = $promotionRepo->retrieveByPromotionIdAndReportDate($promotionData['promotionId'], $report->getRequestDate());
            if ($existingPromotion && !$this->allowUpdate) {
                throw new \InvalidArgumentException("AmazonReportSpa #{$report->getId()}: DataSpaPromotionPerformance with promotionId {$existingPromotion->getPromotionId()} and report date {$existingPromotion->getReportDate()->format('Y-d-m')} already exists");
            }

            $promotionRecord = $existingPromotion ?: new DataSpaPromotionPerformance();
            if (!$existingPromotion) {
                $promotionRecord->setSpaProfile($report->getAccountProfile());
                $promotionRecord->setCreatedFromReportSpa($report);

                $this->checkIfMissingKey($report, DataSpaPromotionPerformance::INCLUDED_PRODUCTS_KEY, $promotionData, 'promotions');
                foreach ($promotionData['includedProducts'] as $productData) {
                    $productRecord = new DataSpaPromotionProduct();
                    foreach (DataSpaPromotionProduct::REPORT_OBJECT_SETTER_MAP as $fieldKey => $setter) {
                        if ($fieldKey === 'productName' && !array_key_exists($fieldKey, $productData)) {
                            $productRecord->setProductName('');
                            continue;
                        }
                        $this->checkIfMissingKey($report, $fieldKey, $productData, 'includedProducts');
                        $productRecord->$setter($productData[$fieldKey]);
                    }

                    $promotionRecord->addPromotionProduct($productRecord);
                }
            } else {
                if ($report->getRequestDate() < $promotionRecord->getReportDate()) {
                    $this->logger->info("Discarding DataSpaPromotionPerformance with promotion #{$promotionData['promotionId']} and report date {$report->getRequestDate()->format('Y-m-d H:i:s')} because it is not newer than record #{$promotionRecord->getId()}");
                    continue;
                }
                $newRecordUpdateDate = GravitiqTools::castToDateTime($promotionData['lastUpdatedDateTime'] ?? null);
                if (is_null($newRecordUpdateDate) || $existingPromotion->getLastUpdatedDate() > $newRecordUpdateDate) {
                    $this->logger->info("Discarding DataSpaPromotionPerformance with promotion #{$promotionData['promotionId']} and report date {$report->getRequestDate()->format('Y-m-d H:i:s')} because ({$newRecordUpdateDate?->format('c')}) is not newer than record #{$existingPromotion->getId()} ({$existingPromotion->getLastUpdatedDate()->format('c')})");
                    continue;
                }

                $promotionRecord->setUpdatedFromReportSpa($report);

                $this->checkIfMissingKey($report, DataSpaPromotionPerformance::INCLUDED_PRODUCTS_KEY, $promotionData, 'promotions');
                foreach ($promotionData['includedProducts'] as $productData) {
                    $productAlreadyExist = false;
                    $productRecord = new DataSpaPromotionProduct();
                    foreach ($existingPromotion->getPromotionProducts() as $existingProduct) {
                        if ($existingProduct->getAsin() === $productData['asin']) {
                            $productRecord = $existingProduct;
                            $productAlreadyExist = true;
                            break;
                        }
                    }

                    foreach (DataSpaPromotionProduct::REPORT_OBJECT_SETTER_MAP as $fieldKey => $setter) {
                        if ($fieldKey === 'productName' && !array_key_exists($fieldKey, $productData)) {
                            $productRecord->setProductName('');
                            continue;
                        }
                        $this->checkIfMissingKey($report, $fieldKey, $productData, 'includedProducts');
                        $productRecord->$setter($productData[$fieldKey]);
                    }

                    $this->validateEntityAndStashErrors($productRecord);
                    if (!$productAlreadyExist) {
                        $promotionRecord->addPromotionProduct($productRecord);
                    }
                }
            }

            foreach (DataSpaPromotionPerformance::REPORT_OBJECT_SETTER_MAP as $fieldKey => $setter) {
                $this->checkIfMissingKey($report, $fieldKey, $promotionData, 'promotions');
                $promotionRecord->$setter($promotionData[$fieldKey]);
            }

            $this->validateEntityAndStashErrors($promotionRecord);
            $this->failIfThereAreValidationErrors($report);

            ++$countRecords;
            $this->persist($promotionRecord);
            $this->flushQueue($classesToClearOnFlush);
        }

        $report->setParseDateToNow();
        return $countRecords;
    }

    /**
     * @param AmazonReportSpa $report
     * @param string $fieldKey
     * @param array<string, mixed> $dataArray
     * @param string $sourceDataIdentifier
     * @throws \Exception
     */
    public function checkIfMissingKey(
        AmazonReportSpa $report,
        string          $fieldKey,
        array           $dataArray,
        string          $sourceDataIdentifier
    ): void
    {
        if (!array_key_exists($fieldKey, $dataArray)) {
            throw new \InvalidArgumentException("AmazonReportSpa #{$report->getId()}: Missing field '{$fieldKey}' on {$sourceDataIdentifier} data array");
        }
    }

    /**
     * @param AmazonReportSpa $report
     * @param array<string, mixed> $dataArray
     * @return void
     */
    private function checkDataForErrorMessage(AmazonReportSpa $report, array $dataArray): void
    {
        if (!empty($dataArray['reportRequestError'])) {
            $report->setError($dataArray['reportRequestError']);
            $report->setParseDateToNow();
            $this->persist($report);
            $this->flushChanges();
            throw new \InvalidArgumentException("Report had a request error: {$report->getError()}");
        }
    }

    /**
     * @throws \Exception
     */
    public function setRecordCountIfNull(AmazonReportSpa $report): void
    {
        if (is_null($report->getRecordCount())) {
            if ($report->getReportType() === SpaReportType::GET_SALES_AND_TRAFFIC_REPORT) {
                $dataArray = $this->getDataArrayFromEntityFile($report);
                if (empty($dataArray) && !str_ends_with($report->getFilename(), BaseApiBridge::FILE_EXTENSION_GZIP)) {
                    // check if it's gz encoded, but has the wrong file extension
                    $dataArray = $this->getDataArrayFromEntityFile($report, true);
                    if (!empty($dataArray)) {
                        $this->logger->info("File for {$report->getId()} seems to be gz encoded without the right extension");
                        $this->renameFileAsGz($report);
                    }
                }
                $this->checkDataForErrorMessage($report, $dataArray);

                foreach ($dataArray as $key => $value) {
                    if (self::JSON_KEY_salesAndTrafficByAsin === $key) {
                        $countAsinRecords = 0;
                        foreach ($value as $record) {
                            if (empty($record['salesByAsin']['orderedProductSales']['currencyCode'])) {
                                continue;
                            }
                            if ($report->getAccountProfile()->getCurrencyCode() !== $record['salesByAsin']['orderedProductSales']['currencyCode']) {
                                continue;
                            }
                            ++$countAsinRecords;
                        }
                        $report->setRecordCount($countAsinRecords);
                    }
                }

            }
        }
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    protected function retrieveSpaProfileFromAlias(string $alias): AccountProfileSpa
    {
        if (empty($alias)) {
            throw new \InvalidArgumentException("Cannot find a Profile for a blank alias");
        }
        if (empty($this->profileAliasMap[$alias])) {
            /** @var AccountProfileSpaRepository $profileRepo */
            $profileRepo = $this->getRepository(AccountProfileSpa::class);

            $ro = new AccountProfileSpaRO();
            $ro->setOptionIncludeInactive(true);
            $profile = $profileRepo->retrieveOneByAlias($alias, $ro);
            $this->profileAliasMap[$alias] = $profile;
        }
        return $this->profileAliasMap[$alias];
    }

    /**
     * @param ValidForDateRangeTraitInterface[] $newRecords
     * @param ValidForDateRangeTraitInterface[] $existingDataRecords note: only contains records which are relevant ON the report date
     * @param ValidForDateRangeRepositoryInterface $dataRepo
     * @param AmazonReportSpa $report
     * @param int $countRecords
     * @return int
     */
    private function manageValidityDates(array $newRecords, array $existingDataRecords, ValidForDateRangeRepositoryInterface $dataRepo, AmazonReportSpa $report, int $countRecords): mixed
    {
        foreach ($newRecords as $newRecord) {
            $actionResult = $newRecord->updateValidityBasedOnExistingMatches($existingDataRecords, $dataRepo, $report, $this->allowUpdate);
            if ('persist_and_continue' === $actionResult) {
                $this->persist($newRecord);
                ++$countRecords;
                continue;
            } elseif ('continue' === $actionResult) {
                continue;
            }

            $newRecord->initDatesBasedOnReport($report);
            $this->persist($newRecord);
            ++$countRecords;
        }
        return $countRecords;
    }
}