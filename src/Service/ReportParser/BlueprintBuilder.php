<?php

namespace App\Service\ReportParser;

use App\Entity\AccountProfileSpa;
use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\ChannelSku;
use App\Entity\Company;
use App\Entity\Merchant;
use App\Entity\MerchantSku;
use App\Entity\Spa\Inventory\DataSpaInvFbaFeePreview;
use App\Entity\Tracker\Blueprint;
use App\Repository\ChannelRepository;
use App\Repository\ChannelSkuRepository;
use App\Repository\MerchantRepository;
use App\Repository\MerchantSkuRepository;
use App\Repository\Spa\Inventory\DataSpaInvFbaFeePreviewRepository;
use App\Repository\Tracker\BlueprintRepository;
use App\Service\Messaging\SlackMessenger;
use App\Tools\GravitiqTools;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

/**
 * Class to build new Blueprints from Amazon-sourced data
 */
class BlueprintBuilder
{
    protected const string SLACK_CHANNEL = 'gravitiq_ops-brand-directors';

    protected EntityManager $em;
    /** @var Blueprint[] */
    protected array $mapAsinToBlueprint = [];
    /** @var Channel[] */
    protected array $mapMarketplaceToAmazonChannel = [];
    /** @var ChannelSku[] */
    protected array $mapMarketplaceAsinToChannelSku = [];
    /** @var Company[] */
    protected array $mapAccountToCompany = [];
    /** @var Merchant[] */
    protected array $mapAccountMarketplaceToMerchant = [];
    /** @var Brand[] */
    protected array $mapBrandNameToBrand = [];

    public function __construct(ManagerRegistry $doctrine, protected LoggerInterface $logger, protected SlackMessenger $slackMessenger)
    {
        $this->em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
        $this->slackMessenger->setLogger($this->logger);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param AccountProfileSpa $profile
     * @param list<string>|null $asins
     * @param int|null $limit
     * @param bool $notifySlack
     * @return int
     * @throws NonUniqueResultException
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function notifyPotentialBlueprintsForProfile(AccountProfileSpa $profile, ?array $asins=null, ?int $limit=0, bool $notifySlack=false): int
    {
        return $this->buildBlueprints($profile, $asins, $limit, false, $notifySlack);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param array<string, string> $asinIskuMap
     * @return void
     * @throws NonUniqueResultException
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function buildBlueprintsForAsinsAndIskus(AccountProfileSpa $profile, array $asinIskuMap): void
    {
        $this->buildBlueprints($profile, $asinIskuMap);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param list<string>|array<string, string>|null $asins Either simple array of ASINs (for $doPersist=false) or map of ASINs to ISKUs (for $doPersist=true)
     * @param int|null $limit
     * @param bool $doPersist
     * @param bool $notifySlack
     * @return int  The number of Blueprints created or notified
     * @throws NonUniqueResultException
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function buildBlueprints(AccountProfileSpa $profile, ?array $asins=null, ?int $limit=0, bool $doPersist=true, bool $notifySlack=false): int
    {
        $numBlueprintsCreated = 0;
        $asinsToNotify = [];
        if ($doPersist) {
            $this->throwExceptionIfAsinIskuMapIsInvalid($asins);
            $asinIskuMap = $asins;
            $asins = array_keys($asinIskuMap);
        }

        /** @var DataSpaInvFbaFeePreviewRepository $amazonInventoryRepo */
        $amazonInventoryRepo = $this->em->getRepository(DataSpaInvFbaFeePreview::class);

        $amazonInventoryItems = $amazonInventoryRepo->retrieveByProfileAsinWithAccount($profile, $asins, $limit);
        foreach ($amazonInventoryItems as $amazonItem) {
            $this->logger->debug("Working with AmazonItem #{$amazonItem->getAsin()}");

            if ($amazonItem->isBundle()) {
                $this->logger->debug("Skipping Bundle AmazonItem #{$amazonItem->getId()}");
                continue;
            }

            try {
                $channel = $this->retrieveOrCreateChannelForAmazonItem($amazonItem);
                $this->logger->debug("Linked to Channel {$channel->getId()}");
            } catch (\Exception) {
                $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} to a Channel");
                continue;
            }

            try {
                $channelSku = $this->retrieveOrCreateChannelSkuForAmazonItem($amazonItem);
                $this->logger->debug("Linked to ChannelSku {$channelSku->getId()} ({$channelSku->getUid()})");
            } catch (\Exception) {
                $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} to a ChannelSku");
                continue;
            }

            try {
                $merchant = $this->retrieveOrCreateMerchantForAmazonItem($amazonItem);
                $this->logger->debug("Linked to Merchant {$merchant->getAlias()}");
            } catch (\InvalidArgumentException|ORMException) {
                if ('TR'===$amazonItem->getMarketplace() || 'PL'===$amazonItem->getMarketplace()) {
                    $this->logger->warning("Could not link AmazonItem #{$amazonItem->getId()} ({$amazonItem->getMarketplace()}) to a Merchant (nor create a new one)");
                } else {
                    $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} ({$amazonItem->getMarketplace()}) to a Merchant (nor create a new one)");
                }
                continue;
            }

            $isku = $doPersist ? $asinIskuMap[$amazonItem->getAsin()] : null;
            $blueprint = $this->retrieveOrCreateBlueprintForAmazonItem($amazonItem, $doPersist, $isku);
            if (!$blueprint) {
                $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} to a Blueprint (nor create a new one)");
                continue;
            }

            if (!$doPersist) {
                if ($blueprint->getId()) {
                    $this->logger->debug("Blueprint #{$blueprint->getId()} already exists for AmazonItem #{$amazonItem->getAsin()}");
                } else {
                    $asinsToNotify[$amazonItem->getAsin()] = $amazonItem->getSku();
                    $this->logger->info("Options prevent saving new Blueprint for AmazonItem #{$amazonItem->getId()}");
                }
                continue;
            } else {
                if (!$blueprint->getId()) {
                    $numBlueprintsCreated++;
                }
            }

            /** @var MerchantSkuRepository $mSkuRepo */
            $mSkuRepo = $this->em->getRepository(MerchantSku::class);
            try {
                $mSku = $mSkuRepo->retrieveForMerchantAndSku($merchant, $amazonItem->getSku());
                $this->updateMerchantSkuFromAmazonItem($mSku, $amazonItem, $blueprint);
                continue;
            } catch (NonUniqueResultException) {
                $this->logger->error("Multiple MerchantSkus returned for AmazonItem #{$amazonItem->getId()}");
                continue;
            } catch (NoResultException) {
                try {
                    $debugType = 'MerchantSku';
                    $mSku = new MerchantSku();
                    $mSku
                        ->setMerchant($merchant)
                        ->setChannelSku($channelSku)
                        ->setSku($amazonItem->getSku())
                        ->setFnsku($amazonItem->getFnsku())
                        ->setBlueprint($blueprint)
                    ;
                    if ($amazonItem->isFba()) {
                        $mSku->setFulfilledByAmazon();
                    } elseif ($amazonItem->isFbm()) {
                        $mSku->setFulfilledByMerchant();
                    }
                    $this->em->persist($mSku);
                    $this->logger->info('Created new MerchantSku');
                } catch (\Exception) {
                    $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} to a $debugType (nor create a new one)");
                    continue;
                }
            }
        }

        if ($doPersist) {
            $this->em->flush();
            return $numBlueprintsCreated;
        } else {
            if (0 < count($asinsToNotify)) {
                $asinSkuList = '';
                foreach ($asinsToNotify as $asin => $sku) {
                    $asinSkuList .= "\n$asin: $sku";
                }
                $message = "Found ".count($asinsToNotify)." {$profile->getAlias()} ASINs without Blueprints: $asinSkuList";
                $this->logger->info($message);
                if ($notifySlack) {
                    $this->slackMessenger->sendMessageToChannel("BlueprintBuilder {$profile->getAlias()}: $message", self::SLACK_CHANNEL);
                }
            }
            return count($asinsToNotify);
        }
    }
    public function sendSlackMessageNotificationFooter(): void
    {
        $this->slackMessenger->sendMessageToChannelWithoutUnfurl(<<<EOS
Please could each Brand Owner check the <https://newton.gravitiq.com/controlroom/blueprint/list|Blueprint list>, create new Blueprints as required, then link any ASINs/SKUs that are mentioned above.

For reference, here's how to <https://share.getcloudapp.com/6quJ9k7G|create a Blueprint in Newton>.
EOS
, self::SLACK_CHANNEL);
    }

    /**
     * @param array<string, string>|null $asinIskuMap
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function throwExceptionIfAsinIskuMapIsInvalid(?array $asinIskuMap): void
    {
        if (empty($asinIskuMap)) {
            throw new \InvalidArgumentException("No asin-isku map provided");
        }
        foreach ($asinIskuMap as $asin => $isku) {
            if (empty($asin) || empty($isku)) {
                throw new \InvalidArgumentException("Invalid asin-isku map provided: contains blank values");
            }
            if (strlen($asin)<10) {
                throw new \InvalidArgumentException("Invalid asin-isku map provided: ASIN $asin is not valid for ISKU $isku");
            }
            if (strlen($isku)<5 || !is_string($isku)) {
                throw new \InvalidArgumentException("Invalid asin-isku map provided: ISKU $isku is not valid for ASIN $asin");
            }
        }
    }
    private function calculateCompanyForAmazonItem(DataSpaInvFbaFeePreview $amazonItem): ?Company
    {
        $key = $amazonItem->getAccountAmazon()->getId();
        if (empty($this->mapAccountToCompany[$key])) {
            $this->mapAccountToCompany[$key] = $amazonItem->getAccountAmazon()->getCompany();
        }
        return $this->mapAccountToCompany[$key];
    }

    /**
     * @param DataSpaInvFbaFeePreview $amazonItem
     * @return Channel
     * @throws ORMException
     * @throws \InvalidArgumentException
     */
    private function retrieveOrCreateChannelForAmazonItem(DataSpaInvFbaFeePreview $amazonItem): Channel
    {
        $country = $amazonItem->getMarketplace();
        if (empty($this->mapMarketplaceToAmazonChannel[$country])) {
            /** @var ChannelRepository $channelRepo */
            $channelRepo = $this->em->getRepository(Channel::class);
            $channel = $channelRepo->retrieveOrCreateAmazonChannelForCountry($country, $this->logger);
            $this->mapMarketplaceToAmazonChannel[$country] = $channel;
        }
        return $this->mapMarketplaceToAmazonChannel[$country];
    }

    /**
     * @param DataSpaInvFbaFeePreview $amazonItem
     * @return ChannelSku
     * @throws NonUniqueResultException
     * @throws NotSupported
     * @throws ORMException
     */
    private function retrieveOrCreateChannelSkuForAmazonItem(DataSpaInvFbaFeePreview $amazonItem): ChannelSku
    {
        $marketplace = $amazonItem->getMarketplace();
        $asin = $amazonItem->getAsin();
        $key = "{$marketplace}~{$asin}";
        if (empty($this->mapMarketplaceAsinToChannelSku[$key])) {
            /** @var ChannelSkuRepository $csRepo */
            $csRepo = $this->em->getRepository(ChannelSku::class);

            $channel = $this->retrieveOrCreateChannelForAmazonItem($amazonItem);
            try {
                $channelSku = $csRepo->retrieveOneByChannelAndUid($channel, $amazonItem->getAsin());
            } catch (NoResultException) {
                $channelSku = new ChannelSku();
                $channelSku
                    ->setChannel($channel)
                    ->setUid($amazonItem->getAsin())
                ;
                $this->em->persist($channelSku);
            }
            $this->mapMarketplaceAsinToChannelSku[$key] = $channelSku;
        }

        return $this->mapMarketplaceAsinToChannelSku[$key];
    }

    /**
     * @param DataSpaInvFbaFeePreview $amazonItem
     * @return Merchant
     * @throws \InvalidArgumentException
     * @throws ORMException
     */
    private function retrieveOrCreateMerchantForAmazonItem(DataSpaInvFbaFeePreview $amazonItem): Merchant
    {
        $accountId = $amazonItem->getAccountAmazon()->getId();
        $marketplace = $amazonItem->getMarketplace();
        $key = "{$accountId}~{$marketplace}";
        if (empty($this->mapAccountMarketplaceToMerchant[$key])) {
            $spaProfile = $amazonItem->getAccountAmazon()->getSpaProfileForCountry($marketplace); // throws InvalidArgumentException if not found

            /** @var MerchantRepository $merchantRepo */
            $merchantRepo = $this->em->getRepository(Merchant::class);
            $this->mapAccountMarketplaceToMerchant[$key] = $merchantRepo->createAmazonMerchantForSpaProfile($spaProfile, $this->logger, $this->mapMarketplaceToAmazonChannel[$marketplace] ?? null);
        }
        return $this->mapAccountMarketplaceToMerchant[$key];
    }

    private function updateMerchantSkuFromAmazonItem(MerchantSku $mSku, DataSpaInvFbaFeePreview $amazonItem, Blueprint $blueprint): void
    {
        if ($mSku->getChannelSku()->getUid() !== $amazonItem->getAsin()) {
            $mSku->setChannelSku($this->retrieveOrCreateChannelSkuForAmazonItem($amazonItem));
        }
        if ($mSku->getFnsku() !== $amazonItem->getFnsku()) {
            $mSku->setFnsku($amazonItem->getFnsku());
        }
        if ($mSku->getBlueprint() !== $blueprint) {
            $mSku->setBlueprint($blueprint);
        }
    }

    private function retrieveOrCreateBrandForAmazonItem(DataSpaInvFbaFeePreview $amazonItem): Brand
    {
        $key = $amazonItem->getBrand();
        if (empty($this->mapBrandNameToBrand[$key])) {
            /** @var ChannelSkuRepository $brandRepo */
            $brandRepo = $this->em->getRepository(Brand::class);

            $brand = $brandRepo->findOneBy(['brandCode' => $amazonItem->getAccountAmazon()->getBrandCode()]);
            if (empty($brand)) {
                $brand = new Brand();
                $brand
                    ->addCompanyInCountry($this->calculateCompanyForAmazonItem($amazonItem), $amazonItem->getMarketplace())
                    ->setBrandCode($amazonItem->getAccountAmazon()->getBrandCode())
                    ->setInternalName($key)
                    ->setExternalName($key)
                ;
                $this->em->persist($brand);
            }
            $this->mapBrandNameToBrand[$key] = $brand;
        }
        return $this->mapBrandNameToBrand[$key];
    }

    /**
     * @param DataSpaInvFbaFeePreview $amazonItem
     * @param bool $doPersist
     * @param string|null $newIsku The new Isku to use if a new blueprint is created. If null, no new blueprint will be saved.
     * @return Blueprint|null
     * @throws NonUniqueResultException
     * @throws NotSupported
     * @throws ORMException
     */
    private function retrieveOrCreateBlueprintForAmazonItem(DataSpaInvFbaFeePreview $amazonItem, bool $doPersist, ?string $newIsku=null): ?Blueprint
    {
        $key = $amazonItem->getAsin();
        if (!empty($this->mapAsinToBlueprint[$key]) && $this->mapAsinToBlueprint[$key]->getId()) {
            return $this->mapAsinToBlueprint[$key];
        }

        /** @var BlueprintRepository $blueprintRepo */
        $blueprintRepo = $this->em->getRepository(Blueprint::class);

        $channel = $this->retrieveOrCreateChannelForAmazonItem($amazonItem);
        $blueprint = $blueprintRepo->retrieveOneByAsinAndChannel($amazonItem->getAsin(), $channel);

        if (empty($blueprint) && $newIsku) {
            $blueprint = $blueprintRepo->retrieveOneByIsku($newIsku);
        }

        if (empty($blueprint) && $doPersist) {
            if ($blueprintRepo->addJoinsBetweenBlueprintsAndChannelSkusBasedOnMerchantSkus(null, [$amazonItem->getAsin()])) {
                $blueprint = $blueprintRepo->retrieveOneByAsinAndChannel($amazonItem->getAsin(), $channel);
            }
        }

        if (empty($blueprint)) {
            $blueprint = new Blueprint();
            $blueprint
                ->setProductName($amazonItem->getProductName())
                ->setLegacySku($amazonItem->getSku())
                ->setDimensions($amazonItem->getDimensionsAsUnifiedString())
                ->setWeight($amazonItem->getWeightGrams())
                ->setRetail(true)
            ;

            if ($newIsku) {
                $blueprint->setIsku($newIsku);

                try {
                    $debugType = 'Brand';
                    $brand = $this->retrieveOrCreateBrandForAmazonItem($amazonItem);
                    $blueprint->setBrand($brand);
                } catch (\Exception) {
                    $this->logger->error("Could not link AmazonItem #{$amazonItem->getId()} to a $debugType (nor create a new one)");
                    return null;
                }

                $channelSku = $this->retrieveOrCreateChannelSkuForAmazonItem($amazonItem);
                $channelSku->addBlueprint($blueprint);
                $this->logger->info('Created new Blueprint for asin~isku ' . $key . '~' . $blueprint->getIsku());
                $this->em->persist($blueprint);
                $this->em->flush(); // Needed here so getId() check works at start of this function
            } else {
                $this->logger->info("Want to create new Blueprint for asin $key in {$amazonItem->getMarketplace()} no isku was provided (SKU: {$amazonItem->getSku()})");
            }
        }

        $this->mapAsinToBlueprint[$key] = $blueprint;
        return $this->mapAsinToBlueprint[$key];
    }
}