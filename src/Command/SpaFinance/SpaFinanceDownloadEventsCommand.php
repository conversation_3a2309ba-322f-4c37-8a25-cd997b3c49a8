<?php

namespace App\Command\SpaFinance;

use App\Command\GravitiqFileSystemSwitchboardCommand;
use App\Command\SpaPoolTrait;
use App\Tools\GravitiqTools;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:spa-fde',
    description: 'Command to download financial events for a given date range',
//    aliases: ['newton:spa-finance-download-events'],
)]
class SpaFinanceDownloadEventsCommand extends GravitiqFileSystemSwitchboardCommand
{
    use SpaPoolTrait;

    protected function doConfigure(): void
    {
        $this
//            ->setHelp('')
            ->addArgumentProfileAlias()
            ->addArgument('startDate', InputArgument::REQUIRED,'Start date to pass through to request')
            ->addArgument('days', InputArgument::REQUIRED,'Number of days that the report should span (or "toNow" to span until today)')
            ->addOption('daily', null, InputOption::VALUE_NONE,'Requests one report per day over the span')
            ->addOption('perPage', null, InputOption::VALUE_REQUIRED,'Pagination setting', 100)
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        /** @var \DateTime $startDate */
        $startDate = GravitiqTools::castToDateTime($input->getArgument('startDate'));
        $numDays = $input->getArgument('days');
        $perPage = $input->getOption('perPage');
        if ('toNow' === $numDays) {
            $numDays = GravitiqTools::daysBetweenDates($startDate, new \DateTime('yesterday'));
            $output->writeln("Using $numDays days (between startDate and yesterday)");
        }
        $forceDaily = $input->getOption('daily');
        if ($forceDaily) {
            $numLoops = $numDays;
            $endDateDaysAfterStartDate = 1;
        } else {
            $numLoops = 1;
            $endDateDaysAfterStartDate = $numDays;
        }
        $endDate = GravitiqTools::getDateXDaysAfter($startDate, $endDateDaysAfterStartDate);

        for ($i=1; $i<=$numLoops; ++$i) {
            foreach ($this->retrieveOneProfileSpaPerRegionFromInput($input) as $profile) {
                $profileAlias = $profile->getAlias();
                $output->writeln("Using accountProfile #$profileAlias to retrieve financial events between {$startDate->format('Y-m-d H:i')} and {$endDate->format('Y-m-d H:i')}");

                $financeApiManager = $this->getFinanceApiManager($profileAlias, $this->fileSystemSwitchboard, $this->getFileSystemNameForFinance());

                $message = $financeApiManager->downloadFinancialEvents($startDate, $endDate, $perPage);
                $output->writeln($message);
            }

            $startDate->add(new \DateInterval('P1D'));
            $endDate->add(new \DateInterval('P1D'));
        }
    }
}