<?php

namespace App\Command\Shopify;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Entity\AccountShopify;
use App\Repository\AccountShopifyRepository;
use App\Service\Shopify\ShopifyManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:shopify:count-orders',
    description: 'Command to count orders from Shopify matching the given parameters',
)]
class ShopifyCountOrdersCommand extends GravitiqDoctrineLoggingCommand
{
    public function __construct(ManagerRegistry $doctrine, LoggerInterface $gravitiqConsoleLogger, private readonly ShopifyManager $shopifyManager)
    {
        $this->shopifyManager->setLogger($gravitiqConsoleLogger);
        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgument('brandCode', InputArgument::REQUIRED, 'Store to process.')
            ->addOption('status', null, InputArgument::OPTIONAL, 'Filter orders by their status {open, closed, cancelled, any}', 'any')
            ->addOption('startOrderDate', null, InputArgument::OPTIONAL, 'Show orders created at or after date')
            ->addOption('endOrderDate', null, InputArgument::OPTIONAL, 'Show orders created at or before date')
            ->addOption('startUpdateDate', null, InputArgument::OPTIONAL, 'Show orders last updated at or after date')
            ->addOption('endUpdateDate', null, InputArgument::OPTIONAL, 'Show orders last updated at or before date')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $brandCode = $input->getArgument('brandCode');
        $status = $input->getOption('status');
        $startOrderDate = GravitiqTools::castToDateTimeImmutable($input->getOption('startOrderDate'));
        $endOrderDate = GravitiqTools::castToDateTimeImmutable($input->getOption('endOrderDate'));
        $startUpdateDate = GravitiqTools::castToDateTimeImmutable($input->getOption('startUpdateDate'));
        $endUpdateDate = GravitiqTools::castToDateTimeImmutable($input->getOption('endUpdateDate'));

        /** @var AccountShopifyRepository $accountShopifyRepo */
        $accountShopifyRepo = $this->em->getRepository(AccountShopify::class);
        $accounts = $accountShopifyRepo->retrieveAccountsToReadOrdersByBrandCode($brandCode);

        if (empty($accounts)) {
            $this->logger->error("No Shopify read-order accounts found for $brandCode");
            return;
        }

        $queryParams = $this->shopifyManager->buildQueryParamsForStatusAndDates($status, $startOrderDate, $endOrderDate, $startUpdateDate, $endUpdateDate);
        $this->logger->info("Query params: " . json_encode($queryParams));
        foreach ($accounts as $account) {
            $this->logger->info("Processing {$account->getBrand()->getBrandCode()} Shopify account {$account->getId()}");
            $this->shopifyManager->countShopifyOrders($account, $queryParams);
        }
    }
}