<?php

namespace App\Command\SpaReport;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Service\Internal\ReportSpaManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:spa-copy-report',
    description: 'Copies report(s) to a new file system',
)]
class CopySpaReportFileCommand extends GravitiqDoctrineLoggingCommand
{
    protected ReportSpaManager $reportManager;

    public function __construct(ReportSpaManager $reportManager, LoggerInterface $gravitiqConsoleLogger, ManagerRegistry $doctrine)
    {
        $this->reportManager = $reportManager;
        $reportManager->setLogger($gravitiqConsoleLogger);

        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgument('reportIds', InputArgument::REQUIRED, 'The report id(s) to copy, can be range list')
            ->addArgument('newFileSystem', InputArgument::REQUIRED, 'The file system to copy to')
            ->addArgument('subfolder', InputArgument::OPTIONAL, 'The subfolder to copy to on the newFileSystem')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $reportIds = GravitiqTools::decodeRangeList($input->getArgument('reportIds'));
        $newFileSystem = $input->getArgument('newFileSystem');
        $subfolder = $input->getArgument('subfolder');

        $reportRepo = $this->reportManager->getReportRepository();
        $reports = $reportRepo->retrieveByIds($reportIds);
        foreach ($reports as $report) {
            $this->reportManager->copyFileSystemFileToNewFileSystem($report, $newFileSystem, $subfolder);
        }
    }
}