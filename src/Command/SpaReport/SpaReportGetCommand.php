<?php

namespace App\Command\SpaReport;

use App\Command\PollingCommandTrait;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:spa-rg',
    description: 'Command to get a report update from Amazon via the SP-API (to find out when it is ready to download)',
//    aliases: ['newton:spa-report-get'],
)]
class SpaReportGetCommand extends SpaReportBaseCommand
{
    use PollingCommandTrait;

    protected function doConfigure(): void
    {
        $this
//            ->setHelp('')
            ->addArgumentProfileAlias()
            ->addArgument('reportId', InputArgument::REQUIRED, 'The AmazonReportSpa.id to get an update for')
        ;
        $this->addPollingOptions();
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $this->parsePollingOptions($input);
        $profileAlias = $this->extractSpaProfileAliasFromInput($input);
        $reportId = $input->getArgument('reportId');

        $output->writeln("Using accountProfile #$profileAlias to retrieve report #$reportId");

        $reportApiManager = $this->getReportApiManager($profileAlias, $this->fileSystemSwitchboard, $this->getFileSystemNameForReportsSpa());

        $azReport = $reportApiManager->updateReportStatus($reportId, $this->pollCount, $this->pollSeconds);

        $status = $azReport->getStatus();
        $document = $azReport->getAzReportDocumentId();
        $output->writeln("Status is: $status" . ($document ? " and document is: $document" : ''));
    }
}