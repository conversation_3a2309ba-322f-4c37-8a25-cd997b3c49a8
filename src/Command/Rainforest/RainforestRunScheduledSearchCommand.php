<?php

namespace App\Command\Rainforest;

use App\Command\GravitiqDoctrineCommand;
use App\Command\LockableProcessTrait;
use App\Entity\Rainforest\ScheduleSearch;
use App\Repository\Rainforest\ScheduleSearchRepository;
use App\Service\Internal\ProcessLockManager;
use App\Service\Rainforest\RainforestManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:rf:ss',
    description: 'Command to run all scheduled Rainforest pending searches',
//    aliases: ['newton:rainforest:scheduled-search'],
)]
class RainforestRunScheduledSearchCommand extends GravitiqDoctrineCommand
{
    use LockableProcessTrait;

    public function __construct(
        ManagerRegistry $doctrine,
        LoggerInterface $gravitiqConsoleLogger,
        private readonly RainforestManager $rainforestManager,
        ProcessLockManager $lockManager
    ){
        $this->rainforestManager->setLogger($gravitiqConsoleLogger);
        $this->configureLockManager($lockManager, $gravitiqConsoleLogger);
        parent::__construct($doctrine);
    }

    public function doConfigure(): void
    {
        $this
            ->addArgument('maxApiRequests', InputArgument::REQUIRED, 'Max number of requests to make in this run (intention is that this command is scheduled, so next run will continue the queue)')
            ->addOption('country', null, InputOption::VALUE_REQUIRED, 'The country code(s) to process, e.g. "US"')
            ->addOption('maxPage', null, InputOption::VALUE_REQUIRED, 'Max page to read from, defaults to 1 but can be set to higher value', 1)
            ->addOption('incFailed', null, InputOption::VALUE_NONE, 'Consider failed requests as "done recently"')
        ;
    }

    public function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $maxApiRequests = $input->getArgument('maxApiRequests'); // will not stop in the middle of a single schedule, so actual number of request may be higher than this
        $country = GravitiqTools::decodeStringList($input->getOption('country'));
        $maxPage = $input->getOption('maxPage');
        $incFailed = $input->getOption('incFailed');

        /** @var ScheduleSearchRepository $ssRepo */
        $ssRepo = $this->em->getRepository(ScheduleSearch::class);
        $schedules = $ssRepo->retrieveStaleSchedules($country, $incFailed);
        if (empty($schedules)) {
            echo "\nNo stale schedules found";
            return;
        }

        $this->rainforestManager->initClient();

        $numPagesRetrieved = 0;
        foreach ($schedules as $schedule) {
            if ($numPagesRetrieved >= $maxApiRequests) {
                $output->writeln("Max number of API requests reached ($maxApiRequests), so stopping even though there are more schedules to process");
                break;
            }

            $pageText = ($maxPage > 1) ? "pp 1-$maxPage" : 'page 1';
            echo "Processing schedule for {$schedule->getQuery()} in {$schedule->getCountryCode()}, {$pageText}\n";
            $apiRequests = $this->rainforestManager->retrieveAndParseSearchResults($schedule->getCountryCode(), $schedule->getQuery(), 0, $schedule->getFirstPage(), $schedule->getMaxPageNum());
            $numPagesRetrieved += count($apiRequests);
        }
    }

    protected function postConfigure(): void
    {
        $this->configureLockOptions();
        parent::postConfigure();
    }

    /**
     * @throws \Exception
     */
    protected function preExecute(InputInterface $input, OutputInterface $output): void
    {
        $this->preExecuteProcessLock($input);
        parent::preExecute($input, $output);
    }
    protected function postExecute(InputInterface $input, OutputInterface $output): void
    {
        $this->postExecuteProcessLock();
        parent::postExecute($input, $output);
    }
}