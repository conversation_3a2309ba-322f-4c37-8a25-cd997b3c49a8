<?php

namespace App\Command\Spa\Crawler;

use App\Command\GravitiqCrawlerCommand;
use App\Service\Crawler\AmazonSellerCentralCouponCrawler;
use App\Service\Internal\ProcessLockManager;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @extends GravitiqCrawlerCommand<AmazonSellerCentralCouponCrawler>
 */
#[AsCommand(
    name: 'newton:crawl-sc:coupon-restart',
    description: 'Restarts a coupon on seller central',
)]
class AmazonScCouponRestartCommand extends GravitiqCrawlerCommand
{
    protected LoggerInterface $logger;

    public function __construct(AmazonSellerCentralCouponCrawler $crawler, ProcessLockManager $lockManager, LoggerInterface $gravitiqConsoleLogger, ManagerRegistry $doctrine)
    {
        parent::__construct($crawler, $lockManager, $gravitiqConsoleLogger, $doctrine);
    }

    public function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
            ->addArgument('couponId', InputOption::VALUE_REQUIRED, 'Coupon ID to re enable')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $couponId = $input->getArgument('couponId');
        if (empty($couponId) || (20 > strlen($couponId))) {
            throw new \InvalidArgumentException('Coupon ID is required, should look like 12345678-9abc-def0-1234-56789abcdef0');
        }

        $profiles = $this->retrieveProfileSpasFromInput($input, true);
        $profile = reset($profiles);

        $this->crawler->manageRestartCoupon($profile, $couponId);
    }
}
