<?php

namespace App\Command\Spa\Crawler;

use App\Command\GravitiqCrawlerCommand;
use App\Service\Crawler\AmazonSellerCentralCreatorConnectionsCrawler;
use App\Service\Internal\ProcessLockManager;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @extends GravitiqCrawlerCommand<AmazonSellerCentralCreatorConnectionsCrawler>
 */
#[AsCommand(
    name: 'newton:crawl-sc:cc-get-messages',
    description: 'Logins to Seller Central and goes to advertising to get Creator Connection messages data',
)]
class AmazonScCreatorConnectionGetMessagesCommand extends GravitiqCrawlerCommand
{
    protected LoggerInterface $logger;

    public function __construct(AmazonSellerCentralCreatorConnectionsCrawler $crawler, ProcessLockManager $lockManager, LoggerInterface $gravitiqConsoleLogger, ManagerRegistry $doctrine)
    {
        parent::__construct($crawler, $lockManager, $gravitiqConsoleLogger, $doctrine);
    }

    public function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $profiles = $this->retrieveProfileSpasFromInput($input);
        $this->crawler->manageScrapeMessagesForProfiles($profiles);
    }
}
