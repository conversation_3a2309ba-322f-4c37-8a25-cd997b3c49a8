<?php

namespace App\Command\Spa\Crawler;

use App\Command\GravitiqCrawlerCommand;
use App\Entity\Crawler\DataCrawlAscCoupon;
use App\Service\Crawler\AmazonSellerCentralCouponCrawler;
use App\Service\Internal\ProcessLockManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @extends GravitiqCrawlerCommand<AmazonSellerCentralCouponCrawler>
 */
#[AsCommand(
    name: 'newton:crawl-sc:coupon-create',
    description: 'Creates a coupon on seller central',
)]
class AmazonScCouponCreateCommand extends GravitiqCrawlerCommand
{
    protected LoggerInterface $logger;

    public function __construct(AmazonSellerCentralCouponCrawler $crawler, ProcessLockManager $lockManager, LoggerInterface $gravitiqConsoleLogger, ManagerRegistry $doctrine)
    {
        parent::__construct($crawler, $lockManager, $gravitiqConsoleLogger, $doctrine);
    }

    public function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
            ->addOption('asins',                    null, InputOption::VALUE_REQUIRED, 'ASINs to create coupon for', null)
            ->addOption('startDate',                null, InputOption::VALUE_REQUIRED, 'Start date for the coupon', null)
            ->addOption('endDate',                  null, InputOption::VALUE_REQUIRED, 'End date for the coupon', null)
            ->addOption('discountType',             null, InputOption::VALUE_REQUIRED, 'Discount Type for the coupon', 'PERCENT')
            ->addOption('discount',                 null, InputOption::VALUE_REQUIRED, 'Discount for the coupon', null)
            ->addOption('budget',                   null, InputOption::VALUE_REQUIRED, 'Total budget for the coupon', 100)
            ->addOption('title',                    null, InputOption::VALUE_REQUIRED, 'Title for the coupon', null)
            ->addOption('allowMultiplePerCustomer', null, InputOption::VALUE_NONE,     'Allow more than one discount per customer (very unusual - be careful!)')
            ->addOption('stackedPromotions',        null, InputOption::VALUE_REQUIRED, 'Stack Promotion for the coupon', 'Exclusive')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $profiles = $this->retrieveProfileSpasFromInput($input, true);
        $profile = reset($profiles);
        $title = $input->getOption('title');
        if (preg_match('/[^a-zA-Z0-9 ]/', $title)) {
            throw new \InvalidArgumentException('Title can only contain letters, numbers and spaces');
        }

        $coupon = new DataCrawlAscCoupon();
        $coupon
            ->setAsins(GravitiqTools::decodeStringList($input->getOption('asins')))
            ->setStartDate($input->getOption('startDate'))
            ->setEndDate($input->getOption('endDate'))
            ->setDiscountType($input->getOption('discountType'))
            ->setDiscountValueFromDecimal($input->getOption('discount'))
            ->setBudgetFromDecimal($input->getOption('budget'))
            ->setTitle($title)
            ->setOncePerCustomer(!$input->getOption('allowMultiplePerCustomer'))
            ->setCombinability($input->getOption('stackedPromotions'))
            ->setSpaProfile($profile)
        ;

        $this->crawler->manageCreateCoupon($coupon);
        $this->logger->info("Coupon ID: {$coupon->getPromotionId()}");
    }
}
