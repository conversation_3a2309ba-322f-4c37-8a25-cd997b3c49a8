<?php

namespace App\Command\DataDive;

use App\Entity\DataDive\ApiRequestDataDive;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:data-dive:fetch-rank-radars',
    description: 'Command to fetch a list of rank radars from the DataDive API',
)]
class DataDiveFetchNicheRankRadarsCommand extends DataDiveBaseCommand
{
    protected function doConfigure(): void
    {
        $this
            ->configureNeedUpdateNicheOptionsAndArgument()
            ->addOption('status', null, InputOption::VALUE_REQUIRED, 'Status for Data Dive Niche rank radars (ACTIVE, ALL, PAUSED)', 'ALL')
            ->addOption('searchText', null, InputOption::VALUE_REQUIRED, 'Search text for Data Dive Niche rank radars', '')
            ->configurePageOptions()
        ;
    }

    protected function getRequestType(): string
    {
        return ApiRequestDataDive::REQUEST_TYPE_NICHE_RANK_RADARS;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $niches = $this->retrieveNichesThatNeedUpdateFromInput($input);
        $this->logger->info("Found " . count($niches) . " niches to process");

        $status = strtoupper($input->getOption('status'));
        $searchText = $input->getOption('searchText');
        list($page, $pageSize, $maxPages) = $this->getAndValidatePageOptions($input);

        $statusOptions = ['ACTIVE', 'PAUSED', 'ALL'];
        if (!in_array($status, $statusOptions)) {
            throw new \InvalidArgumentException('Status must be one of: ' . implode(', ', $statusOptions));
        }

        $apiManager = $this->getApiManager();
        $apiManager->manageFetchNicheRankRadars($niches, $status, $page, $pageSize, $maxPages, $searchText);
    }
}