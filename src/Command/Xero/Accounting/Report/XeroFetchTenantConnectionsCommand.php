<?php

namespace App\Command\Xero\Accounting\Report;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Service\Xero\XeroApiManager;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @noinspection PhpUnused - this is a Command
 */
#[AsCommand(
    name: 'newton:xero:fetch-tenants',
    description: 'Command to fetch tenant connections from Xero via the XERO-API.',
)]
class XeroFetchTenantConnectionsCommand extends GravitiqDoctrineLoggingCommand
{
    public function __construct(
        ManagerRegistry $doctrine,
        LoggerInterface $gravitiqConsoleLogger,
        private readonly XeroApiManager $xeroApiManager
    )
    {
        $this->xeroApiManager->setLogger($gravitiqConsoleLogger);
        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $profiles = $this->xeroApiManager->retrieveXeroProfilesFromInput($input);
        if (count($profiles) === 0) {
            $this->logger->error("No profiles found for {$input->getArgument('profileAlias')}");
            return;
        }

        foreach ($profiles as $profile) {
            $this->xeroApiManager->fetchTenantConnections($profile);
        }
    }
}