<?php

namespace App\Command\Internal;

use App\Command\GravitiqDoctrineParamsCommand;
use App\Entity\AccountProfileSpa;
use App\Service\ContainerParametersHelper;
use App\Service\Internal\CwrDataManipulator;
use App\Service\Messaging\SlackMessenger;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:cwr:paf',
    description: 'Prepares advertising files for Commercial Weekly Reports and copies them to Google Drive',
//    aliases: ['newton:cwr:prepare-advertising-files'],
)]
class CwrPrepareAdvertisingFilesCommand extends GravitiqDoctrineParamsCommand
{
    protected CwrDataManipulator $dataManipulator;
    protected LoggerInterface $logger;
    protected SlackMessenger $slackMessenger;

    public function __construct(CwrDataManipulator $dataManipulator, LoggerInterface $gravitiqConsoleLogger, SlackMessenger $slackMessenger, ManagerRegistry $doctrine, ContainerParametersHelper $paramsHelper)
    {
        $this->logger = $gravitiqConsoleLogger;
        $dataManipulator->setLogger($gravitiqConsoleLogger);
        $this->dataManipulator = $dataManipulator;
        $this->slackMessenger = $slackMessenger;

        parent::__construct($doctrine, $paramsHelper, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
//            ->setHelp('')
            ->addArgumentProfileAlias(true)
            ->addOption('weekEnding', null, InputOption::VALUE_REQUIRED, 'Use to specify week in the past, defaults to last week')
            ->addOption('startDate', null, InputOption::VALUE_REQUIRED, 'Use to specify a date to create CWRs from')
            ->addOption('endDate', null, InputOption::VALUE_REQUIRED, 'Use to specify a date to create CWRs until')
            ->addOption('notifySlack', null, InputOption::VALUE_NONE, 'Set to send a slack notification when the script completes')
            ->addOption('force', null, InputOption::VALUE_NONE, 'Use to force creation of files, even if incomplete')
            ->addOption('freeDateMode', null, InputOption::VALUE_NONE, 'Set to use exactly the dates given, instead of forcing to CWR week start/end')
            ->addOption('incNonCwr', null, InputOption::VALUE_NONE, 'Set to include accounts that are not usually in the CWR')
            ->addOption('notAdSpend', null, InputOption::VALUE_NONE, 'Use to skip creation of AdSpend reports')
            ->addOption('notAdRevenue', null, InputOption::VALUE_NONE, 'Use to skip creation of AdRevenue reports')
            ->addOption('outputPath', null, InputOption::VALUE_REQUIRED, 'Set to use a non-standard output path for generated files')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $doAdSpend = !$input->getOption('notAdSpend');
        $doAdRevenue = !$input->getOption('notAdRevenue');
        $notifySlack = $input->getOption('notifySlack');
        $force = $input->getOption('force');
        $freeDateMode = $input->getOption('freeDateMode');
        $cwrOnly = !($input->getOption('incNonCwr'));
        if ($outputPathPrefix = $input->getOption('outputPath')) {
            $outputPathPrefix = '/' . trim($outputPathPrefix, '/') . '/';
        } elseif ($freeDateMode) {
            $this->dataManipulator->setOutputPathPrefixToGsr();
        }
        $weekEnding = $this->convertDateOptionToDate($input, 'weekEnding');
        if ($freeDateMode) {
            if ($weekEnding) {
                throw new \InvalidArgumentException('ERROR: freeDateMode and weekEnding cannot be used together');
            }
            $startDateBasedOnWeekEnding = null;
            $endDateBasedOnWeekEnding = null;
        } else {
            if (empty($weekEnding)) {
                $weekEnding = new \DateTimeImmutable('last Sunday');
            }
            $startDateBasedOnWeekEnding = new \DateTimeImmutable(date('c', strtotime('last Monday',strtotime($weekEnding->format('c')))));
            $endDateBasedOnWeekEnding = new \DateTimeImmutable(date('c', strtotime('next Monday',strtotime($startDateBasedOnWeekEnding->format('c')))));
        }

        $startDate = GravitiqTools::castToDateTimeImmutable($input->getOption('startDate')) ?: $startDateBasedOnWeekEnding;
        $endDate = GravitiqTools::castToDateTimeImmutable($input->getOption('endDate')) ?: $endDateBasedOnWeekEnding;
        if (is_null($startDate)) {
            throw new \InvalidArgumentException('ERROR: startDate or weekEnding must be specified');
        }
        if (is_null($endDate)) {
            throw new \InvalidArgumentException('ERROR: endDate or weekEnding must be specified');
        }

        $profiles = $this->retrieveProfileAaasFromInput($input);
        if ($cwrOnly) {
            /** @var AccountProfileSpa[] $profiles */
            $profiles = $this->dataManipulator->filterCwrProfilesFromArray($profiles);
        }

        $this->logger->info("Creating CWR AAA reports for date range {$startDate->format('Y-m-d')} - {$endDate->format('Y-m-d')}");

        if ($outputPathPrefix) {
            $this->dataManipulator->setOutputPathPrefix($outputPathPrefix);
        }

        $profilesDone = [];
        $profilesSkipped = [];

        foreach ($profiles as $profile) {
            if (!$profile->isSellerProfile()) {
                $this->logger->info("Skipping {$profile->getAlias()} because not a seller profile");
                continue;
            }
            if (!$this->dataManipulator->checkAaaDataLooksComplete($profile, $startDate, $endDate)) {
                $message = "Data for {$profile->getAlias()} is not complete";
                if ($force) {
                    $this->logger->warning($message);
                } else {
                    $profilesSkipped[$profile->getAlias()] = true;
                    $this->logger->error($message);
                    continue;
                }
            }
            if ($doAdSpend) {
                $this->dataManipulator->buildWeeklyAdSpendReport($profile, $startDate, $endDate);
            }
            if ($doAdRevenue) {
                $this->dataManipulator->buildWeeklyAdRevenueReport($profile, $startDate, $endDate);
            }
//            if ($doKeyword) {
//                $this->dataManipulator->buildWeeklyKeywordQueryReport($profile, $startDate, $endDate);
//                $this->dataManipulator->buildWeeklyKeywordSalesReport($profile, $startDate, $endDate);
//            }
            $profilesDone[$profile->getAlias()] = true;
        }
        ksort($profilesDone);
        ksort($profilesSkipped);

        $message = "Published CWR Advertising report data (week ending {$endDate->format('Y-m-d')}) for profiles: " . implode(', ', array_keys($profilesDone));
        if (!empty($profilesSkipped)) {
            $message .= "\n:warning: Skipped profiles with incomplete data. If any of these are your brands and you expect them to have complete data for this week, please notify Christian ASAP: " . implode(',', array_keys($profilesSkipped));
        }
        $this->logger->info($message);
        if ($notifySlack && !empty($profilesDone)) {
            $this->slackMessenger->sendMessageToChannel($message, 'support_commercial-weekly-reports-cwr');
        }
    }
}