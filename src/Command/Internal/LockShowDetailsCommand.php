<?php

namespace App\Command\Internal;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Command\GravitiqLockableDoctrineLoggingCommand;
use App\Service\Internal\DsrManager;
use App\Service\Internal\ProcessLockManager;
use App\Service\Monday\MondayManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:lock:details',
    description: 'Shows details about a given process lock',
)]
class LockShowDetailsCommand extends GravitiqDoctrineLoggingCommand
{
    public function __construct(
        protected ProcessLockManager $lockManager,
        ManagerRegistry $doctrine,
        LoggerInterface $gravitiqConsoleLogger,
    ) {
        $this->lockManager->setLogger($gravitiqConsoleLogger);
        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgument('lockCode', InputArgument::REQUIRED, 'Lock to display details for')
            ->addOption('forceUnlock')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $lockCode = $input->getArgument('lockCode');
        $this->lockManager->getLockDetails($lockCode);
        if ($input->getOption('forceUnlock')) {
            $this->lockManager->unlockProcess($lockCode);
        }
    }
}