<?php

namespace App\Command\Internal;

use App\Command\GravitiqLockableDoctrineLoggingCommand;
use App\Entity\Crawler\CachedCreatorConnectionsAdMetricWeekly;
use App\Entity\Crawler\CachedCreatorConnectionsCampaignMetricWeekly;
use App\Repository\Crawler\CachedCreatorConnectionsAdMetricWeeklyRepository;
use App\Repository\Crawler\CachedCreatorConnectionsCampaignMetricWeeklyRepository;
use App\Tools\GravitiqTools;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:cache:cc-weekly',
    description: 'Re-calculates weekly metric values for Creator Connections tables',
)]
class ReCacheCreatorConnectionWeeklyMetricsCommand extends GravitiqLockableDoctrineLoggingCommand
{
    protected LoggerInterface $logger;

    protected function doConfigure(): void
    {
        $this
            ->addArgument('startDate', InputArgument::OPTIONAL, 'Start date, defaults to Monday last week', 'last week monday')
            ->addArgument('endDate', InputArgument::OPTIONAL, 'End date, defaults to today', 'midnight today')
        ;
    }

    protected function getDefaultLockCode(): ?string
    {
        return 'reCacheCcWeekly';
    }

    protected function getDefaultLockMins(): ?int
    {
        return 10;
    }

    protected function getDefaultLockExpiresAfter(): ?int
    {
        return 3 * $this->getDefaultLockMins();
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $startDate = GravitiqTools::castToDateTimeMidnight($input->getArgument('startDate'));
        $endDate = GravitiqTools::castToDateTimeMidnight($input->getArgument('endDate'));

        if (is_null($startDate) || is_null($endDate)) {
            throw new \InvalidArgumentException("Invalid date range, please provide a startDate and endDate");
        }
        if ($startDate >= $endDate) {
            throw new \InvalidArgumentException("Invalid date range, startDate must be before endDate");
        }

        $dateBatches = [];
        $daysBetweenDates = GravitiqTools::daysBetweenDates($startDate, $endDate);
        if (7 >= $daysBetweenDates) {
            $dateBatches[] = $startDate;
        } else {
            // create 7-day batches
            $batchStartDate = $startDate;
            while ($batchStartDate < $endDate) {
                $dateBatches[] = $batchStartDate;
                $batchStartDate = GravitiqTools::getDateXDaysAfter($batchStartDate, 7);
            }
            $dateBatches[] = $batchStartDate;
        }

        /** @var CachedCreatorConnectionsAdMetricWeeklyRepository $adMetricsRepo */
        $adMetricsRepo = $this->getRepository(CachedCreatorConnectionsAdMetricWeekly::class);
        /** @var CachedCreatorConnectionsCampaignMetricWeeklyRepository $campaignMetricsRepo */
        $campaignMetricsRepo = $this->getRepository(CachedCreatorConnectionsCampaignMetricWeekly::class);

        foreach ($dateBatches as $loopDate) {
            $startDate = GravitiqTools::getDateOfPreviousDayOfWeek($loopDate, 1);
            $endDate = GravitiqTools::getDateOfNextDayOfWeek($loopDate, 7);
            $this->logger->info("Processing date range {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

            $numUpdates = $adMetricsRepo->upsertBetweenDates($startDate, $endDate);
            $this->logger->info("Upserted $numUpdates AdMetricWeekly rows");
            $numUpdates = $campaignMetricsRepo->upsertBetweenDates($startDate, $endDate);
            $this->logger->info("Upserted $numUpdates CampaignMetricWeekly rows");

            $numUpdates = $adMetricsRepo->processQueuedRecalculationOfFutureDeltas();
            $this->logger->info("Queue updated $numUpdates AdMetricWeekly rows");
            $numUpdates = $campaignMetricsRepo->processQueuedRecalculationOfFutureDeltas();
            $this->logger->info("Queue updated $numUpdates CampaignMetricWeekly rows");
        }
    }
}