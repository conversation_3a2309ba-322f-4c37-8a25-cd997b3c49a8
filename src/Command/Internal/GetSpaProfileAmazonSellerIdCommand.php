<?php

namespace App\Command\Internal;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Domain\AmazonSpaPool;
use App\Entity\AccountProfileSpa;
use App\Entity\Spa\Inventory\DataSpaInvListingsAll;
use App\Entity\SpaOrder\DataSpaOrdItem;
use App\Repository\Spa\Inventory\DataSpaInvListingsAllRepository;
use App\Repository\SpaOrder\DataSpaOrdItemRepository;
use App\Service\Internal\ProfileSpaManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use SellingPartnerApi\Response;
use SellingPartnerApi\Seller\ProductFeesV0\Dto\FeesEstimateRequest;
use SellingPartnerApi\Seller\ProductFeesV0\Dto\GetMyFeesEstimateRequest;
use Selling<PERSON>artnerApi\Seller\ProductFeesV0\Dto\MoneyType;
use SellingPartnerApi\Seller\ProductFeesV0\Dto\PriceToEstimateFees;
use SellingPartnerApi\Seller\ProductFeesV0\Responses\GetMyFeesEstimateResponse;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:spa:asi',
    description: 'Gets sellerId from Amazon API and checks/saves it to the database',
//    aliases: ['newton:spa:amazon-seller-id'],
)]
class GetSpaProfileAmazonSellerIdCommand extends GravitiqDoctrineLoggingCommand
{
    public function __construct(
        protected ProfileSpaManager            $profileManager,
        LoggerInterface                        $gravitiqConsoleLogger,
        ManagerRegistry                        $doctrine,
    ) {
        $this->profileManager->setLogger($gravitiqConsoleLogger);
        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
            ->addOption('includeExisting', null, InputOption::VALUE_NONE, 'Set to re-check profiles that already have a sellerId')
            ->addOption('months', null, InputOption::VALUE_REQUIRED, 'Number of months to check', 2)
            ->addOption('skus', null, InputOption::VALUE_REQUIRED, 'SKUs to use (sellerid found via fee preview)')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $profiles = $this->retrieveProfileSpasFromInput($input);
        $includeExisting = $input->getOption('includeExisting');
        $months = $input->getOption('months');
        if ($inputSkus = $input->getOption('skus')) {
            $inputSkus = GravitiqTools::decodeStringList($inputSkus);
        }

        /** @var DataSpaInvListingsAllRepository $invListingsAllRepo */
        $invListingsAllRepo = $this->em->getRepository(DataSpaInvListingsAll::class);
        /** @var DataSpaOrdItemRepository $orderItemRepo */
        $orderItemRepo = $this->em->getRepository(DataSpaOrdItem::class);
        foreach ($profiles as $profile) {
            if (!$includeExisting && $profile->getAzSellerId()) {
                $this->logger->info("Skipping profile {$profile->getAlias()} because it already has a sellerId");
                continue;
            } else {
                $this->logger->info("Checking profile {$profile->getAlias()}");
            }

            $sellerId = null;
            $skus = $inputSkus ?: null;
            if (empty($skus)) {
                $skus = $orderItemRepo->retrieveLatestSkus($profile, 3, $months);
                if (empty($skus)) {
                    $skus = $invListingsAllRepo->retrieveSkus($profile, 5);
                }
                if (empty($skus)) {
                    $this->logger->info("Skipping profile {$profile->getAlias()} because it has no SKUs (checked orders and inv_listings_all)");
                    continue;
                }
            }

            foreach ($skus as $sku) {
                $sellerId = $this->_getSellerIdForProfileAndSku($profile, $sku);
                if (!empty($sellerId)) {
                    break;
                }
            }

            if ($sellerId) {
                if ($profile->getAzSellerId()) {
                    if ($profile->getAzSellerId() !== $sellerId) {
                        $this->logger->error("SellerId mismatch for profile {$profile->getAlias()}: {$profile->getAzSellerId()} vs. $sellerId");
                    } else {
                        $this->logger->info("SellerId already set correctly for profile {$profile->getAlias()}");
                    }
                    continue;
                }
                $this->logger->info("Setting SellerId for profile {$profile->getAlias()} to $sellerId");
                $profile->setAzSellerId($sellerId);
            } else {
                $this->logger->error("NO sellerId found for profile {$profile->getAlias()}");
            }
        }
        $this->em->flush();
    }

    protected function _getSellerIdForProfileAndSku(AccountProfileSpa $profile, string $sku): ?string
    {
        $spaPool = new AmazonSpaPool($this->em, $profile);

        $api = $spaPool->getSpaProductFeesApi($profile->getCountryCode());
        $identifier = 'NewtonGq' . microtime(true);
        $request = new GetMyFeesEstimateRequest(new FeesEstimateRequest(
            marketplaceId: $profile->getMarketplaceId(),
            priceToEstimateFees: new PriceToEstimateFees(
                listingPrice: new MoneyType($profile->getCurrencyCode(), 10,),
            ),
            identifier: $identifier,

        ));
        /** @var Response $result */
        $result = $api->getMyFeesEstimateForSKU($sku, $request);

        $responseBody = $result->getResponse()->body();
        $responseData = json_decode($responseBody, true);

        $response = new GetMyFeesEstimateResponse($responseData);

        return $response?->payload->feesEstimateResult?->feesEstimateIdentifier?->sellerId;
    }
}