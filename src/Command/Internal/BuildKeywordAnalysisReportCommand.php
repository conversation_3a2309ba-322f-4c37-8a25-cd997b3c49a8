<?php

namespace App\Command\Internal;

use App\Command\GravitiqDoctrineCommand;
use App\Service\Internal\KeywordAnalysisBuilder;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:build:kar',
    description: 'Build Keyword Analysis report',
//    aliases: ['newton:build:keyword-analysis-report']
)]
class BuildKeywordAnalysisReportCommand extends GravitiqDoctrineCommand
{
    public function __construct(
        protected KeywordAnalysisBuilder $builder,
        LoggerInterface                  $gravitiqConsoleLogger,
        ManagerRegistry                  $doctrine,
    ) {
        $this->builder->setLogger($gravitiqConsoleLogger);
        parent::__construct($doctrine);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgumentProfileAlias()
            ->addOption('minDate', null, InputOption::VALUE_REQUIRED, 'Minimum date for report, defaults to yesterday', 'yesterday')
            ->addOption('maxDate', null, InputOption::VALUE_REQUIRED, 'Maximum date for report, defaults to today', 'today')
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $minDate = GravitiqTools::castToDateTime($input->getOption('minDate'));
        $minDateString = $minDate->format('Y-m-d');
        $maxDate = GravitiqTools::castToDateTime($input->getOption('maxDate'));
        $maxDateString = $maxDate->format('Y-m-d');
        $profiles = $this->retrieveProfileSpasFromInput($input);

        $items = [];
        foreach ($profiles as $profile) {
            $items[] = [
                'minDate'   => $minDateString,
                'maxDate'   => $maxDateString,
                'brandCode' => substr($profile->getAlias(), 0, 2),
            ];
        }

        $this->builder->buildReportsForCollection($items, true);
    }
}
