<?php

namespace App\Command;

use App\Domain\AmazonAaaApiManager;
use App\Domain\AmazonSpaAwdApiManager;
use App\Domain\AmazonSpaFinancesApiManager;
use App\Domain\AmazonSpaListingsApiManager;
use App\Domain\AmazonSpaOrdersApiManager;
use App\Domain\AmazonSpaReportsApiManager;
use App\Domain\SpaApiManager\AmazonSpaFbaInboundApiManager;
use App\Domain\SpaApiManager\AmazonSpaInventoryApiManager;
use App\Service\ContainerParametersHelper;
use App\Service\FileSystemSwitchboard;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

abstract class GravitiqFileSystemSwitchboardCommand extends GravitiqDoctrineParamsCommand
{
    protected FileSystemSwitchboard $fileSystemSwitchboard;

    public function __construct(
        FileSystemSwitchboard $fileSystemSwitchboard,
        ManagerRegistry $doctrine,
        ContainerParametersHelper $paramsHelper,
        LoggerInterface $gravitiqConsoleLogger
    ) {
        $this->fileSystemSwitchboard = $fileSystemSwitchboard;

        parent::__construct($doctrine, $paramsHelper, $gravitiqConsoleLogger);
    }

    protected function getFileSystemNameForAwd(): string
    {
        return $this->getContainerParameter(AmazonSpaAwdApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForFinance(): string
    {
        return $this->getContainerParameter(AmazonSpaFinancesApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForInventory(): string
    {
        return $this->getContainerParameter(AmazonSpaInventoryApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForListings(): string
    {
        return $this->getContainerParameter(AmazonSpaListingsApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForOrders(): string
    {
        return $this->getContainerParameter(AmazonSpaOrdersApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForReportsAaa(): string
    {
        return $this->getContainerParameter(AmazonAaaApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForReportsSpa(): string
    {
        return $this->getContainerParameter(AmazonSpaReportsApiManager::DEFAULT_FILE_SYSTEM);
    }
    protected function getFileSystemNameForFbaInbound(): string
    {
        return $this->getContainerParameter(AmazonSpaFbaInboundApiManager::DEFAULT_FILE_SYSTEM);
    }
}