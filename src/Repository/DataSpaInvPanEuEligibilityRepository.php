<?php

namespace App\Repository;

use App\Entity\AccountProfileSpa;
use App\Entity\DataSpaInvPanEuEligibility;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<DataSpaInvPanEuEligibility>
 *
 * @method DataSpaInvPanEuEligibility|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataSpaInvPanEuEligibility|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataSpaInvPanEuEligibility[]    findAll()
 * @method DataSpaInvPanEuEligibility[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataSpaInvPanEuEligibilityRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'dInvPEE';

    public function deleteExistingRecordsForSameProfile(AccountProfileSpa $profile)
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS)
            ->delete()
        ;
        $qb
            ->andWhere($qb->expr()->eq(static::getAlias('spaProfile'), ':spaProfile'))
            ->setParameter('spaProfile', $profile)
        ;
        return $qb
            ->getQuery()
            ->getResult()
        ;

    }

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataSpaInvPanEuEligibility::class);
    }

    public function add(DataSpaInvPanEuEligibility $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DataSpaInvPanEuEligibility $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return DataSpaInvPanEuEligibility[] Returns an array of DataSpaInvPanEuEligibility objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataSpaInvPanEuEligibility
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
