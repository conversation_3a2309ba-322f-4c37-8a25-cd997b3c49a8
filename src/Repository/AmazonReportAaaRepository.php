<?php

namespace App\Repository;

use App\Entity\AmazonReportAaa;
use App\Repository\RetrieverOptions\AmazonReportAaaRO;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends AmazonReportBaseRepository<AmazonReportAaa>
 *
 * @method AmazonReportAaa|null find($id, $lockMode = null, $lockVersion = null)
 * @method AmazonReportAaa|null findOneBy(array $criteria, array $orderBy = null)
 * @method AmazonReportAaa[]    findAll()
 * @method AmazonReportAaa[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AmazonReportAaaRepository extends AmazonReportBaseRepository
{
    const string ROOT_ALIAS = 'aRepAaa';

    /**
     * @param AmazonReportAaaRO $ro
     * @param ?QueryBuilder $qb
     * @return QueryBuilder
     * @throws QueryException
     */
    public function generateQueryBuilder($ro, ?QueryBuilder $qb=null): QueryBuilder
    {
        $qb = $this->initQueryBuilder($ro);

        $qb = parent::generateQueryBuilder($ro, $qb);

        if ($ro->getOptionMetricGroup()) {
            $qb
                ->andWhere($qb->expr()->like(self::getAlias('metricGroup'),':metricGroup'))
                ->setParameter('metricGroup', $ro->getOptionMetricGroup())
            ;
        }
        if ($ro->getOptionDateMax()) {
            $qb
                ->andWhere($qb->expr()->lte(self::getAlias('reportDate'),':reportDateMax'))
                ->setParameter('reportDateMax', $ro->getOptionDateMax())
            ;
        }
        if ($ro->getOptionDateMin()) {
            $qb
                ->andWhere($qb->expr()->gte(self::getAlias('reportDate'),':reportDateMin'))
                ->setParameter('reportDateMin', $ro->getOptionDateMin())
            ;
        }
        if ($ro->getOptionReportDate()) {
            $qb
                ->andWhere($qb->expr()->like(self::getAlias('reportDate'),':reportDateExact'))
                ->setParameter('reportDateExact', $ro->getOptionReportDate()->format('Y-m-d %'))
            ;
        }
        if ($ro->getOptionSizeMax()) {
            $qb
                ->andWhere($qb->expr()->lt(self::getAlias('fileSize'), ':fileSizeMax'))
                ->setParameter('fileSizeMax', $ro->getOptionSizeMax())
            ;
        }
        if ($ro->getOptionSizeMin()) {
            $qb
                ->andWhere($qb->expr()->orX(
                    $qb->andWhere($qb->expr()->gte(self::getAlias('fileSize'), ':fileSizeMin')),
                    $qb->expr()->isNull(self::getAlias('fileSize'))
                ))
                ->setParameter('fileSizeMin', $ro->getOptionSizeMin())
            ;
        }

        return $qb;
    }

    /**
     * @param ?AmazonReportAaaRO $ro
     * @return AmazonReportAaaRO
     */
    public function initRo($ro = null): AmazonReportAaaRO
    {
        if (empty($ro)) {
            return new AmazonReportAaaRO();
        }
        return $ro;
    }

    /**
     * @param AmazonReportAaaRO $ro
     * @return AmazonReportAaa[]
     */
    public function retrieveByOptions($ro): array
    {
        return parent::doRetrieveByOptions($ro);
    }
    /**
     * @param AmazonReportAaaRO $ro
     * @return AmazonReportAaa
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function retrieveOneByOptions($ro): AmazonReportAaa
    {
        return parent::doRetrieveOneByOptions($ro);
    }

    protected function getReportStatusCancelled(): string
    {
        return AmazonReportAaa::STATUS_CANCELLED;
    }
    protected function getReportStatusDone(): string
    {
        return AmazonReportAaa::STATUS_DONE;
    }
    protected function getReportStatusStillProcessing(): string|array
    {
        return [AmazonReportAaa::STATUS_IN_PROGRESS,AmazonReportAaa::STATUS_IN_QUEUE];
    }



    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AmazonReportAaa::class);
    }

    public function add(AmazonReportAaa $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AmazonReportAaa $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
