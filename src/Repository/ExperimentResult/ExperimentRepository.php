<?php

namespace App\Repository\ExperimentResult;

use App\Entity\ExperimentResult\Experiment;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<Experiment>
 *
 * @method Experiment|null find($id, $lockMode = null, $lockVersion = null)
 * @method Experiment|null findOneBy(array $criteria, array $orderBy = null)
 * @method Experiment[]    findAll()
 * @method Experiment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ExperimentRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'exp';

    /**
     * @param string $experimentId
     * @return ?Experiment Returns an array of Experiment objects
     */
    public function retrieveByExperimentId(string $experimentId): ?Experiment
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('azExperimentId'), ':experimentId'))
            ->setParameter('experimentId', $experimentId)
        ;

        try {
            $experiment = $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            throw new \RuntimeException("Multiple experiments found for experimentId $experimentId", 0, $e);
        }
        return $experiment;
    }

    /**
     * @return Experiment[]
     */
    public function retrieveExperimentsToReCache(bool $notCached, int $limit=null, ?\DateTimeImmutable $minStartDate=null, ?\DateTimeImmutable $maxStartDate=null): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
        ;
        if ($notCached) {
            $qb
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->isNull(self::getAlias('caUnitsSoldDelta')),
                    $qb->expr()->isNull(self::getAlias('caProbBBetter')),
                    $qb->expr()->isNull(self::getAlias('caConversionDelta')),
                    $qb->expr()->isNull(self::getAlias('caMostLikelyProjectedAnnualIncrementalSales')),
                    $qb->expr()->isNull(self::getAlias('caMostLikelyProjectedAnnualIncrementalUnits')),
                    $qb->expr()->isNull(self::getAlias('caTotalOfControlSampleAndTreatmentSample')),
                ))
            ;
        } else {
            if (is_null($minStartDate) && is_null($maxStartDate)) {
                throw new \InvalidArgumentException("You must provide either a minStartDate/maxStartDate or set notCached to true");
            }
        }
        if ($limit) {
            $qb->setMaxResults($limit);
        }
        if ($minStartDate) {
            $qb
                ->andWhere($qb->expr()->gte(self::getAlias('startDate'), ':minStartDate'))
                ->setParameter('minStartDate', $minStartDate, 'datetime')
            ;
        }
        if ($maxStartDate) {
            $qb
                ->andWhere($qb->expr()->lte(self::getAlias('startDate'), ':maxStartDate'))
                ->setParameter('maxStartDate', $maxStartDate, 'datetime')
            ;
        }

        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    public function cacheUncachedExperimentIskus(): int
    {
        $sql = <<<SQL
UPDATE data_crawl_asc_er_experiment e
   SET e.caIskus = (
	SELECT GROUP_CONCAT(b.isku SEPARATOR ' / ') AS 'iSKUs'
	  FROM tk_blueprint b
	  LEFT JOIN tk_join_blueprint_channel_sku j ON b.id = j.blueprintId
	  LEFT JOIN top_channel_sku cs ON j.csId = cs.id
	 WHERE cs.uid IN (
		SELECT t.targetString
		  FROM data_crawl_asc_er_target t
		 WHERE t.experimentId = e.id
		   AND t.targetType = 'ASIN'
		)
	   AND cs.channelId = 'AMZ_US'
	)
  WHERE e.caIskus IS NULL
SQL;
        return $this->getEntityManager()->getConnection()->executeStatement($sql);
    }

    /**
     * @return Experiment[]
     */
    public function retrieveExperimentsToNotifyAbout(int $limit=null, ?\DateTimeImmutable $minStartDate=null, ?\DateTimeImmutable $maxStartDate=null): array
    {
        $qb = $this->createQueryBuilder(self::getRootAlias());
        $qb
            ->select(self::getRootAlias())
        ;

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        $qb
            ->andWhere($qb->expr()->orX(
                $qb->expr()->isNull(self::getAlias('isNotified')),
                $qb->expr()->eq(self::getAlias('isNotified'), ':isNotified')
            ))
            ->setParameter('isNotified', false)
            ->andWhere($qb->expr()->in(self::getAlias('status'), ':statuses'))
            ->setParameter('statuses', [Experiment::STATUS_COMPLETE, Experiment::STATUS_CANCELLED, Experiment::STATUS_VALIDATION_FAILED])
        ;

        if ($minStartDate) {
            $qb
                ->andWhere($qb->expr()->gte(self::getAlias('startDate'), ':minStartDate'))
                ->setParameter('minStartDate', $minStartDate, 'datetime')
            ;
        }
        if ($maxStartDate) {
            $qb
                ->andWhere($qb->expr()->lte(self::getAlias('startDate'), ':maxStartDate'))
                ->setParameter('maxStartDate', $maxStartDate, 'datetime')
            ;
        }

        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

     public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Experiment::class);
    }

//    /**
//     * @return Experiment[] Returns an array of Experiment objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('e.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Experiment
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
