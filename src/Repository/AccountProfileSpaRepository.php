<?php

namespace App\Repository;

use App\Entity\AccountProfileSpa;
use App\Repository\RetrieverOptions\AccountProfileSpaRO;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends AccountProfileBaseRepository<AccountProfileSpa>
 *
 * @method AccountProfileSpa|null find($id, $lockMode = null, $lockVersion = null)
 * @method AccountProfileSpa|null findOneBy(array $criteria, array $orderBy = null)
 * @method AccountProfileSpa[]    findAll()
 * @method AccountProfileSpa[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AccountProfileSpaRepository extends AccountProfileBaseRepository
{
    const string ROOT_ALIAS = 'aps';

    public function initRo($ro = null): AccountProfileSpaRO
    {
        if (empty($ro)) {
            return new AccountProfileSpaRO();
        }
        return $ro;
    }
    /**
     * @param AccountProfileSpaRO $ro
     * @return AccountProfileSpa[]
     */
    public function retrieveByOptions($ro): array
    {
        return parent::doRetrieveByOptions($ro);
    }
    /**
     * @param AccountProfileSpaRO $ro
     * @return AccountProfileSpa
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function retrieveOneByOptions($ro): AccountProfileSpa
    {
        return parent::doRetrieveOneByOptions($ro);
    }

    /**
     * @return array{string, string}
     */
    public function retrieveAliasesAsChoiceArray(): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $results = $qb
            ->select(self::getAlias('alias'))
            ->orderBy(self::getAlias('alias'), 'ASC')
            ->getQuery()
            ->getSingleColumnResult()
        ;
        return array_combine($results, $results);
    }

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AccountProfileSpa::class);
    }

    public function add(AccountProfileSpa $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AccountProfileSpa $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
