<?php

namespace App\Repository\Levanta;

use App\Entity\Levanta\LevantaBrand;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<LevantaBrand>
 *
 * @method LevantaBrand|null find($id, $lockMode = null, $lockVersion = null)
 * @method LevantaBrand|null findOneBy(array $criteria, array $orderBy = null)
 * @method LevantaBrand[]    findAll()
 * @method LevantaBrand[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LevantaBrandRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'lvtBrand';

    /**
     * @param string[] $lvtBrandIds
     * @return LevantaBrand[]
     */
    public function retrieveByLvtBrandIds(array $lvtBrandIds): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS, self::getAlias('lvtBrandId'));
        $qb
            ->leftJoin(self::getAlias('creatorBrands'), LevantaCreatorBrandRepository::ROOT_ALIAS)
            ->addSelect(LevantaCreatorBrandRepository::ROOT_ALIAS)
            ->andWhere($qb->expr()->in(self::getAlias('lvtBrandId'), ':lvtBrandId'))
            ->setParameter('lvtBrandId', $lvtBrandIds)
        ;
        return $qb->getQuery()->getResult();
    }

    /**
     * @param string $lvtBrandId
     * @return LevantaBrand|null
     * @throws NonUniqueResultException
     */
    public function retrieveOneByLvtBrandId(string $lvtBrandId): ?LevantaBrand
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->andWhere($qb->expr()->eq(self::getAlias('lvtBrandId'), ':lvtBrandId'))
            ->setParameter('lvtBrandId', $lvtBrandId)
        ;
        return $qb->getQuery()->getOneOrNullResult();
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LevantaBrand::class);
    }

//    /**
//     * @return LevantaBrand[] Returns an array of LevantaBrand objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?LevantaBrand
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
