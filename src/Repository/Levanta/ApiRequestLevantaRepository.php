<?php

namespace App\Repository\Levanta;

use App\Entity\Levanta\ApiRequestLevanta;
use App\Repository\CountableEntityRepositoryTrait;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\Query\Expr\Comparison;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<ApiRequestLevanta>
 *
 * @method ApiRequestLevanta|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiRequestLevanta|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiRequestLevanta[]    findAll()
 * @method ApiRequestLevanta[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiRequestLevantaRepository extends GravitiqRepository
{
    /**
     * @use CountableEntityRepositoryTrait<ApiRequestLevanta>
     */
    use CountableEntityRepositoryTrait;

    const string ROOT_ALIAS = 'apiReqLvt';

    /**
     * @param int|list<int> $ids
     * @param array{allReady:bool, requestType:string|null, limit:int|null} $options
     * @return ApiRequestLevanta[]
     */
    public function retrieveByIdsFilteredByOptions(int|array $ids, array $options): array
    {
        $qb = $this->createQueryBuilder(static::ROOT_ALIAS)
            ->select(static::ROOT_ALIAS)
        ;
        $qb
            ->andWhere($qb->expr()->in(static::getAliasId(), ':ids'))
            ->setParameter('ids', $ids)
        ;
        if ($options['allReady']) {
            $qb
                ->andWhere($qb->expr()->eq(static::getAlias('status'), ':statusReady'))
                ->andWhere($qb->expr()->eq(static::getAlias('genHasParseDate'), ':isParsed'))
                ->setParameter('statusReady', ApiRequestLevanta::STATUS_DONE)
                ->setParameter('isParsed', false)
            ;
        }
        if ($options['requestType']) {
            $qb
                ->andWhere($qb->expr()->eq(static::getAlias('requestType'), ':requestType'))
                ->setParameter('requestType', $options['requestType'])
            ;
        }
        if ($options['limit']) {
            $qb
                ->setMaxResults($options['limit'])
            ;
        }

        return $qb
            ->getQuery()
            ->getResult()
            ;
    }

    /**
     * @param array{accountId?:int|null, limit?:int|null} $filterValues
     * @return list<ApiRequestLevanta>
     */
    public function retrieveUnparsableRequests(array $filterValues): array
    {
        $qb = $this->createQueryBuilderForRequestsToProcess($filterValues);
        $this->replaceWhereAndClause($qb, [
            'leftPart' => self::getAlias('savedFileSize'),
            'operator' => Comparison::GT,
            'rightPart' => null,
        ], [
            'leftPart' => null,
            'operator' => Comparison::LTE,
            'rightPart' => null,
        ]);

        return $qb
            ->getQuery()
            ->getResult()
            ;
    }

    /**
     * @param array{accountId?:int|null, limit?:int|null} $filterValues
     * @return QueryBuilder
     */
    public function createQueryBuilderForRequestsToProcess(array $filterValues): QueryBuilder
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS)->select(self::ROOT_ALIAS);
        $qb = $this->addParsableEntityClausesToQueryBuilder($qb, $filterValues, null, 30);

        return $qb;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiRequestLevanta::class);
    }

//    /**
//     * @return ApiRequestLevanta[] Returns an array of ApiRequestLevanta objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ApiRequestLevanta
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
