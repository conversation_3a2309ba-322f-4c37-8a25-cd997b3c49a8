<?php

namespace App\Repository;

use App\Entity\AccountAmazon;
use App\Repository\RetrieverOptions\AccountProfileBaseRO;
use App\Repository\RetrieverOptions\AccountProfileSpaRO;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;

/**
 * @template T of object
 * @template-extends GravitiqROBasedRepository<T>
 */
abstract class AccountProfileBaseRepository extends GravitiqROBasedRepository
{
    /**
     * @param AccountProfileBaseRO $ro
     * @return QueryBuilder
     */
    public function generateQueryBuilder($ro): QueryBuilder
    {
        $qb = $this->initQueryBuilder($ro);

        $qb
            ->addSelect(MerchantRepository::getRootAlias(), AccountAmazonRepository::getRootAlias())
            ->leftJoin(self::getAlias('merchant'), MerchantRepository::getRootAlias())
            ->leftJoin(self::getAlias('accountAmazon'), AccountAmazonRepository::getRootAlias())
        ;

        if ($ro->getOptionAliases()) {
            $qb
                ->andWhere($qb->expr()->in(static::getAlias('alias'), ':aliases'))
                ->setParameter('aliases', $ro->getOptionAliases())
            ;
        }
        if ($ro->getOptionWildcardAliases()) {
            $wildcardAliasClause = $qb->expr()->orX();
            foreach($ro->getOptionWildcardAliases() as $key => $wildcardAlias) {
                $paramKey = "wildAlias$key";

                if (str_contains($wildcardAlias, '!')) {
                    $wildcardAlias = str_replace('!', '', $wildcardAlias);
                    $qb->andWhere($qb->expr()->notLike(static::getAlias('alias'), ":$paramKey"));
                } else {
                    $wildcardAliasClause->add($qb->expr()->like(static::getAlias('alias'), ":$paramKey"));
                }
                $qb->setParameter($paramKey, $wildcardAlias);
            }
            $qb->andWhere($wildcardAliasClause);
        }

        if ($ro->getOptionAccountAmazon()) {
            if ($ro->getOptionAccountAmazon() instanceof AccountAmazon) {
                $qb
                    ->andWhere($qb->expr()->eq(static::getAlias('accountAmazon'), ':accountAmazon'))
                ;
            } elseif (is_int($ro->getOptionAccountAmazon())) {
                $qb = $qb
                    ->andWhere($qb->expr()->eq(static::getIdentity('accountAmazon'), ':accountAmazon'))
                ;
            }
            $qb->setParameter('accountAmazon', $ro->getOptionAccountAmazon());
        }

        if (!$ro->getOptionIncludeInactive()) { // if option is FALSE then filter out the inactive ones
            $qb
                ->andWhere($qb->expr()->eq(static::getAlias('active'), ':boolActive'))
                ->setParameter('boolActive', true)
            ;
        }

        if ($ro->getOptionOnlyInactive()) {
            $qb
                ->andWhere($qb->expr()->eq(static::getAlias('active'), ':boolInactive'))
                ->setParameter('boolInactive', false)
            ;
        }

        return $qb;
    }

    /**
     * @param bool $onlyInactive
     * @param AccountProfileSpaRO|null $ro
     * @return array<T>
     */
    public function retrieveAll(bool $onlyInactive=false, ?AccountProfileSpaRO $ro = null): array
    {
        return $this->retrieveByOptions($this
            ->initRo($ro)
            ->setOptionOnlyInactive($onlyInactive)
        );
    }

    /**
     * @param AccountProfileSpaRO|null $ro
     * @return array<T>
     */
    public function retrieveAllInactive(?AccountProfileSpaRO $ro = null): array
    {
        return $this->retrieveAll(true, $ro);
    }

    /**
     * @param string $key The alias of the Profile to retrieve
     * @param AccountProfileBaseRO|null $ro
     * @return T
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function retrieveOneByAlias(string $key, ?AccountProfileBaseRO $ro = null)
    {
        return $this->retrieveOneByOptions($this
            ->initRo($ro)
            ->setOptionAliases($key)
        );
    }

    /**
     * @param string|array<string> $keys    The aliases of the Profiles to retrieve. Can be a CSL. Can include '%' as a wildcard.
     * @param AccountProfileBaseRO|null $ro
     * @return array<T>
     */
    public function retrieveByWildcardAlias(string|array $keys, ?AccountProfileBaseRO $ro = null): array
    {
        return $this->retrieveByOptions($this
            ->initRo($ro)
            ->setOptionWildcardAliases($keys)
        );
    }

    /**
     * @param int|AccountAmazon $accountAmazon
     * @param AccountProfileBaseRO|null $ro
     * @return array<T>
     */
    public function retrieveProfilesForAccount(int|AccountAmazon $accountAmazon, ?AccountProfileBaseRO $ro = null): array
    {
        return $this->retrieveByOptions($this
            ->initRo($ro)
            ->setOptionAccountAmazon($accountAmazon)
        );
    }
}
