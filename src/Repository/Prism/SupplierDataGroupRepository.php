<?php

namespace App\Repository\Prism;

use App\Entity\Prism\SupplierDataGroup;
use App\Repository\GravitiqRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<SupplierDataGroup>
 *
 * @method SupplierDataGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupplierDataGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method SupplierDataGroup[]    findAll()
 * @method SupplierDataGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SupplierDataGroupRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'prism_sdg';

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SupplierDataGroup::class);
    }

//    /**
//     * @return SupplierDataGroup[] Returns an array of SupplierDataGroup objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('s.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?SupplierDataGroup
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
