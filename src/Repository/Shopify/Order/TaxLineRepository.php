<?php

namespace App\Repository\Shopify\Order;

use App\Entity\Shopify\Order\LineItem;
use App\Entity\Shopify\Order\TaxLine;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<TaxLine>
 *
 * @method TaxLine|null find($id, $lockMode = null, $lockVersion = null)
 * @method TaxLine|null findOneBy(array $criteria, array $orderBy = null)
 * @method TaxLine[]    findAll()
 * @method TaxLine[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TaxLineRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'spfOLITL';

    /**
     * @param LineItem $lineItemId
     * @return TaxLine|null
     * @throws NonUniqueResultException
     */
    public function retrieveByShopifyLineItem(LineItem $lineItemId): ?TaxLine
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->where($qb->expr()->eq(self::getAlias('lineItem'), ':lineItemId'))
            ->setParameter('lineItemId', $lineItemId)
        ;
        return $qb->getQuery()->getOneOrNullResult();
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TaxLine::class);
    }

//    /**
//     * @return TaxLine[] Returns an array of TaxLine objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('t.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?TaxLine
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
