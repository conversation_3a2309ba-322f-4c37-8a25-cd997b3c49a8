<?php

namespace App\Repository\Shopify;

use App\Entity\Shopify\ProductOption;
use App\Repository\GravitiqRepository;
use App\Repository\SoftDeleteRepositoryTrait;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<ProductOption>
 *
 * @method ProductOption|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductOption|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductOption[]    findAll()
 * @method ProductOption[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductOptionRepository extends GravitiqRepository
{
    use SoftDeleteRepositoryTrait;

    const string ROOT_ALIAS = 'spfProdOpt';

    /**
     * @param int|string $optionId
     * @param int|string $productId
     * @param bool $includeDeleted
     * @return ProductOption|null
     * @throws NonUniqueResultException
     */
    public function retrieveOneByShopifyOptionIdAndProductId(int|string $optionId, int|string $productId, bool $includeDeleted = false): ?ProductOption
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('spfProductId'), ':spfProductId'))
            ->andWhere($qb->expr()->eq(self::getAlias('spfOptionsId'), ':spfOptionsId'))
            ->setParameter('spfProductId', $productId)
            ->setParameter('spfOptionsId', $optionId)
        ;

        if ($includeDeleted) {
            $this->disableSoftDelete(); // This disables the soft delete filter
        }

        $result = $qb
            ->getQuery()
            ->getOneOrNullResult();

        if ($includeDeleted) {
            $this->enableSoftDelete(); // Re-enable the soft delete filter after the query
        }

        return $result;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductOption::class);
    }

    public function save(ProductOption $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ProductOption $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return ShopifyProductsOptions[] Returns an array of ShopifyProductsOptions objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('s.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ShopifyProductsOptions
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
