<?php

namespace App\Repository\AzRank;

use App\Entity\AzRank\ApiRequestAzRank;
use App\Repository\CountableEntityRepositoryInterface;
use App\Repository\CountableEntityRepositoryTrait;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<ApiRequestAzRank>
 *
 * @method ApiRequestAzRank|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiRequestAzRank|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiRequestAzRank[]    findAll()
 * @method ApiRequestAzRank[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiRequestAzRankRepository extends GravitiqRepository implements CountableEntityRepositoryInterface
{
    /**
     * @use CountableEntityRepositoryTrait<ApiRequestAzRank>
     */
    use CountableEntityRepositoryTrait;

    protected const string ROOT_ALIAS = 'azr_apireq';

    /**
     * @param int[]|int $ids
     * @return ApiRequestAzRank[]
     */
    public function retrieveEntitiesWithProfiles(array|int $ids): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->in(self::getAlias('id'), ':ids'))
            ->setParameter('ids', $ids)
        ;
        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param array{requestType?:string|null, limit?:int|null} $filterValues
     * @return QueryBuilder
     */
    public function createQueryBuilderForRequestsToProcess(array $filterValues): QueryBuilder
    {
        $requestType = $filterValues['requestType'] ?? null;

        $qb = $this->createQueryBuilder(self::ROOT_ALIAS)->select(self::ROOT_ALIAS);
        $qb = $this->addParsableEntityClausesToQueryBuilder($qb, $filterValues);

        if ($requestType) {
            $qb
                ->andWhere($qb->expr()->eq(self::getAlias('requestType'), ':requestType'))
                ->setParameter('requestType', $requestType)
            ;
        }

        return $qb;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiRequestAzRank::class);
    }

//    /**
//     * @return ApiRequestAzRank[] Returns an array of ApiRequestAzRank objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ApiRequestAzRank
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
