<?php

namespace App\Repository\Manychat;

use App\Entity\Manychat\SubscriberCampaign;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SubscriberCampaign>
 *
 * @method SubscriberCampaign|null find($id, $lockMode = null, $lockVersion = null)
 * @method SubscriberCampaign|null findOneBy(array $criteria, array $orderBy = null)
 * @method SubscriberCampaign[]    findAll()
 * @method SubscriberCampaign[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriberCampaignRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SubscriberCampaign::class);
    }

//    /**
//     * @return SubscriberCampaign[] Returns an array of SubscriberCampaign objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('s.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?SubscriberCampaign
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
