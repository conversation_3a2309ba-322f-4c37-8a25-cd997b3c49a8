<?php

namespace App\Repository\Spa\Fba;

use App\Entity\SpaApiRequest\ApiRequestSpaFbaInbound;
use App\Repository\FileSystemRepositoryTrait;
use App\Repository\RetrieverOptions\ApiRequestSpaFbaInboundRO;
use App\Repository\SpaApiRequest\ApiRequestSpaBaseRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ApiRequestSpaBaseRepository<ApiRequestSpaFbaInbound>
 *
 * @method ApiRequestSpaFbaInbound|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiRequestSpaFbaInbound|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiRequestSpaFbaInbound[]    findAll()
 * @method ApiRequestSpaFbaInbound[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiRequestSpaFbaInboundRepository extends ApiRequestSpaBaseRepository
{
    use FileSystemRepositoryTrait;

    const string ROOT_ALIAS = 'aReqFbaInb';

    /**
     * @param ApiRequestSpaFbaInboundRO $ro
     * @return ApiRequestSpaFbaInbound
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function retrieveOneByOptions($ro): ApiRequestSpaFbaInbound
    {
        return parent::doRetrieveOneByOptions($ro);
    }

    /**
     * @param ApiRequestSpaFbaInboundRO $ro
     * @return ApiRequestSpaFbaInbound[]
     */
    public function retrieveByOptions($ro): array
    {
        return parent::doRetrieveByOptions($ro);
    }

    /**
     * @param ApiRequestSpaFbaInboundRO $ro
     * @return QueryBuilder
     */
    public function generateQueryBuilder($ro): QueryBuilder
    {
        $qb = $this->initQueryBuilder($ro);
        $qb = $this->addCommonClausesToQueryBuilder($qb, $ro);

        return $qb;
    }

    public function initRo($ro = null): ApiRequestSpaFbaInboundRO
    {
        if (empty($ro)) {
            return new ApiRequestSpaFbaInboundRO();
        }
        return $ro;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiRequestSpaFbaInbound::class);
    }

//    /**
//     * @return ApiRequestSpaFbaInbound[] Returns an array of ApiRequestSpaFbaInbound objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ApiRequestSpaFbaInbound
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
