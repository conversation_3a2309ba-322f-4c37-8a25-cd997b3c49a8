<?php

namespace App\Repository\Spa\Inventory;

use App\Entity\Spa\Inventory\DataSpaInvListingsInactive;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends DataSpaInvListingsBaseRepository<DataSpaInvListingsInactive>
 *
 * @method DataSpaInvListingsInactive|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataSpaInvListingsInactive|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataSpaInvListingsInactive[]    findAll()
 * @method DataSpaInvListingsInactive[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataSpaInvListingsInactiveRepository extends DataSpaInvListingsBaseRepository
{
    const string ROOT_ALIAS = 'dILI';

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataSpaInvListingsInactive::class);
    }

    public function save(DataSpaInvListingsInactive $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DataSpaInvListingsInactive $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return DataSpaInvListingsInactive[] Returns an array of DataSpaInvListingsInactive objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataSpaInvListingsInactive
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
