<?php

namespace App\Repository\DataDive;

use App\Entity\DataDive\NicheAnalysis;
use App\Repository\GravitiqRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<NicheAnalysis>
 *
 * @method NicheAnalysis|null find($id, $lockMode = null, $lockVersion = null)
 * @method NicheAnalysis|null findOneBy(array $criteria, array $orderBy = null)
 * @method NicheAnalysis[]    findAll()
 * @method NicheAnalysis[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NicheAnalysisRepository extends GravitiqRepository
{
    protected const string ROOT_ALIAS = 'dd_na';

    /**
     * @param int $getId
     * @param \DateTimeInterface|string $latestResearchDate
     * @return NicheAnalysis[]
     */
    public function retrieveByNicheIdAndLatestResearchDate(int $getId, \DateTimeInterface|string $latestResearchDate): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('niche'), ':nicheId'))
            ->andWhere($qb->expr()->eq(self::getAlias('latestResearchDate'), ':latestResearchDate'))
            ->setParameter('nicheId', $getId)
            ->setParameter('latestResearchDate', $latestResearchDate)
        ;
        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NicheAnalysis::class);
    }

//    /**
//     * @return NicheAnalysis[] Returns an array of NicheAnalysis objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('n')
//            ->andWhere('n.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('n.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?NicheAnalysis
//    {
//        return $this->createQueryBuilder('n')
//            ->andWhere('n.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
