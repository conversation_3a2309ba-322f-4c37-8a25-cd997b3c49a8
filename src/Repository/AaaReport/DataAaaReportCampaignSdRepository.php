<?php

namespace App\Repository\AaaReport;

use App\Entity\AaaReport\DataAaaReportCampaignSd;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DataAaaReportCampaignSd>
 *
 * @method DataAaaReportCampaignSd|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataAaaReportCampaignSd|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataAaaReportCampaignSd[]    findAll()
 * @method DataAaaReportCampaignSd[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataAaaReportCampaignSdRepository extends DataAaaReportBaseRepository
{
    const string ROOT_ALIAS = 'daarCamSd';


    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataAaaReportCampaignSd::class);
    }

    public function add(DataAaaReportCampaignSd $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DataAaaReportCampaignSd $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return DataAaaReportCampaignSd[] Returns an array of DataAaaReportCampaignSd objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataAaaReportCampaignSd
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
