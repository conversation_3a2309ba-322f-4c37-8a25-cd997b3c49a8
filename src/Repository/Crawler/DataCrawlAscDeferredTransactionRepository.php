<?php

namespace App\Repository\Crawler;

use App\Entity\Crawler\DataCrawlAscDeferredTransaction;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<DataCrawlAscDeferredTransaction>
 *
 * @method DataCrawlAscDeferredTransaction|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataCrawlAscDeferredTransaction|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataCrawlAscDeferredTransaction[]    findAll()
 * @method DataCrawlAscDeferredTransaction[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataCrawlAscDeferredTransactionRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'asc_defrd_trxn';

    /**
     * @param string $orderId
     * @return DataCrawlAscDeferredTransaction[]
     */
    public function retrieveByOrderId(string $orderId): array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('orderId'), ':orderId'))
            ->setParameter('orderId', $orderId)
        ;
        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataCrawlAscDeferredTransaction::class);
    }

//    /**
//     * @return DataCrawlAscPaymentDeferredTransaction[] Returns an array of DataCrawlAscPaymentDeferredTransaction objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataCrawlAscPaymentDeferredTransaction
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
