<?php

namespace App\Repository\Crawler;

use App\Entity\AccountProfileSpa;
use App\Entity\Crawler\ApiRequestCrawlAsc;
use App\Entity\Crawler\DataCrawlAscCoupon;
use App\Entity\Crawler\EvergreenCoupon;
use App\Repository\GravitiqRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<DataCrawlAscCoupon>
 *
 * @method DataCrawlAscCoupon|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataCrawlAscCoupon|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataCrawlAscCoupon[]    findAll()
 * @method DataCrawlAscCoupon[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataCrawlAscCouponRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'casc_coupon';

    /**
     * @param array<int, list<string>> $keys Keys are profileIds, values are reportDates
     * @return array
     */
    public function retrieveByGroupedKeys(array $keys): array
    {
        $qb = $this
            ->createQueryBuilder(self::ROOT_ALIAS)
            ->select(self::ROOT_ALIAS)
        ;
        $orClauses = [];
        foreach ($keys as $spaProfileId => $reportDates) {
            $profileKey = "profileId{$spaProfileId}";
            $dateKey = "reportDates{$spaProfileId}";
            $orClauses[] = $qb->expr()->andX(
                $qb->expr()->eq(self::getAlias('spaProfile'), ":{$profileKey}"),
                $qb->expr()->in(self::getAlias('reportDate'), ":{$dateKey}")
            );
            $qb
                ->setParameter($profileKey, $spaProfileId)
                ->setParameter($dateKey, $reportDates)
            ;
        }
        $qb->andWhere($qb->expr()->orX(...$orClauses));

        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param string $promotionId
     * @param \DateTimeInterface $reportDate
     * @return ?DataCrawlAscCoupon
     * @throws NonUniqueResultException
     */
    public function retrieveByPromotionIdAndReportDate(string $promotionId, \DateTimeInterface $reportDate): ?DataCrawlAscCoupon
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('promotionId'), ':promotionId'))
            ->andWhere($qb->expr()->eq(self::getAlias('reportDate'), ':reportDate'))
            ->setParameters([
                'promotionId' => $promotionId,
                'reportDate'  => $reportDate->format('Y-m-d'),
            ])
        ;
        return $qb
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param EvergreenCoupon $evergreenCoupon
     * @param \DateTime $windowStartDate
     * @param \DateTime $windowEndDate
     * @return ?DataCrawlAscCoupon[]
     */
    public function retrieveCouponsMatchingEvergreenWithinWindowOrderedByDate(EvergreenCoupon $evergreenCoupon, \DateTime $windowStartDate, \DateTime $windowEndDate): ?array
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);

        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('spaProfile'), ':profile'))
            ->andWhere($qb->expr()->eq(self::getAlias('asins'), ':asins'))
            ->andWhere($qb->expr()->eq(self::getAlias('discountType'), ':discountType'))
            ->andWhere($qb->expr()->eq(self::getAlias('discountValue'), ':discountValue'))
            ->andWhere($qb->expr()->gte(self::getAlias('endDate'), ':windowStartDate'))
            ->andWhere($qb->expr()->lt(self::getAlias('startDate'), ':windowEndDate'))
            ->setParameters([
                'profile'         => $evergreenCoupon->getSpaProfile(),
                'asins'           => implode(',', $evergreenCoupon->getAsins()),
                'discountType'    => $evergreenCoupon->getDiscountType(),
                'discountValue'   => $evergreenCoupon->getDiscountValue(),
                'windowStartDate' => $windowStartDate->format('Y-m-d H:i:s'),
                'windowEndDate'   => $windowEndDate->format('Y-m-d H:i:s'),
            ])
            ->orderBy(self::getAlias('startDate'), 'ASC')
            ->addOrderBy(self::getAlias('endDate'), 'ASC')
            ->addOrderBy(self::getAlias('reportDate'), 'DESC')
        ;

        /** @var DataCrawlAscCoupon[] $results */
        $results = $qb
            ->getQuery()
            ->getResult()
        ;

        // Process results to get only the latest reportDate per coupon
        $coupons = [];
        foreach ($results as $coupon) {
            $key = $coupon->getPromotionId();
            if (!isset($coupons[$key])) {
                $coupons[$key] = $coupon;
            }
        }

        return array_values($coupons);
    }

    /**
     * @param AccountProfileSpa $profile
     * @param int $numDays
     * @return array{string: true}
     */
    public function retrievePromotionIdsScrapedAtLeastXDaysAfterEnd(AccountProfileSpa $profile, int $numDays): array
    {
        $sql = <<<SQL
        SELECT DISTINCT d.promotionId
          FROM data_crawl_asc_coupon d
         WHERE d.reportDate > DATE_ADD(d.endDate, INTERVAL {$numDays} DAY)
           AND d.spaProfileId = {$profile->getId()}
        SQL;

        $results = $this->executeSql($sql);
        $promotionIds = array_map(fn($result) => $result['promotionId'], $results);
        $promotionIds = array_fill_keys($promotionIds, true);
        return $promotionIds;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataCrawlAscCoupon::class);
    }

//    /**
//     * @return DataCrawlAscCoupon[] Returns an array of DataCrawlAscCoupon objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataCrawlAscCoupon
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
