<?php

namespace App\Repository\SpaAwd;

use App\Entity\AccountAmazon;
use App\Entity\AccountProfileSpa;
use App\Entity\SpaApiRequest\ApiRequestSpaAwd;
use App\Entity\SpaAwd\AwdInboundShipment;
use App\Repository\GravitiqRepository;
use App\Repository\SpaApiRequest\ApiRequestSpaAwdRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<AwdInboundShipment>
 *
 * @method AwdInboundShipment|null find($id, $lockMode = null, $lockVersion = null)
 * @method AwdInboundShipment|null findOneBy(array $criteria, array $orderBy = null)
 * @method AwdInboundShipment[]    findAll()
 * @method AwdInboundShipment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataSpaAwdInboundShipmentRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'dsais';

    /**
     * @param int|int[]|null $ids
     * @return AwdInboundShipment[]
     */
    public function retrieveInboundShipmentsNeedToRequestForDetails(AccountAmazon $account, int|array|null $ids = null): array
    {
        if (!is_array($ids) && !is_null($ids)) {
            $ids = [$ids];
        }

        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->leftJoin(
                ApiRequestSpaAwd::class,
                ApiRequestSpaAwdRepository::getRootAlias(),
                'WITH',
                $qb->expr()->eq(self::getAlias('inboundShipmentRequest'), ApiRequestSpaAwdRepository::getAlias('id'))
            )
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->isNull(self::getAlias('inboundShipmentRequest')),
                    $qb->expr()->gt(self::getAlias('lastUpdatedDate'), ApiRequestSpaAwdRepository::getAlias('requestDate'))
                )
            )
            ->andWhere($qb->expr()->eq(self::getAlias('accountAmazon'), ':accountAmazon'))
            ->setParameter('accountAmazon', $account)
        ;

        if ($ids) {
            $qb
                ->andWhere($qb->expr()->in(self::getAlias('id'), ':ids'))
                ->setParameter('ids', $ids)
            ;
        }

        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param string $shipmentId
     * @param AccountAmazon $accountAmazon
     * @return ?AwdInboundShipment
     * @throws NonUniqueResultException
     */
    public function retrieveOneByShipmentIdAndAccountAmazon(string $shipmentId, AccountAmazon $accountAmazon): ?AwdInboundShipment
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->select(self::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(self::getAlias('azShipmentId'), ':shipmentId'))
            ->andWhere($qb->expr()->eq(self::getAlias('accountAmazon'), ':accountAmazon'))
            ->setParameter('shipmentId', $shipmentId)
            ->setParameter('accountAmazon', $accountAmazon)
        ;

        try {
            return $qb
                ->getQuery()
                ->getSingleResult()
            ;
        } catch (NoResultException) {
            return null;
        }
    }

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AwdInboundShipment::class);
    }

//    /**
//     * @return DataSpaAwdInboundShipments[] Returns an array of DataSpaAwdInboundShipments objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataSpaAwdInboundShipments
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
