<?php

namespace App\Repository;

use App\Entity\AmazonReportSpa;
use App\Entity\DataSpaInvReferralFee;
use Doctrine\ORM\Query\QueryException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<DataSpaInvReferralFee>
 *
 * @method DataSpaInvReferralFee|null find($id, $lockMode = null, $lockVersion = null)
 * @method DataSpaInvReferralFee|null findOneBy(array $criteria, array $orderBy = null)
 * @method DataSpaInvReferralFee[]    findAll()
 * @method DataSpaInvReferralFee[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DataSpaInvReferralFeeRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'dInvRF';

    /**
     * @return DataSpaInvReferralFee[]
     * @throws QueryException
     */
    public function retrieveExistingRecordsThatCouldBeLinkedToReport(AmazonReportSpa $report): array
    {
        $qb = $this->createQueryBuilder(static::ROOT_ALIAS)
            ->select(static::ROOT_ALIAS)
        ;
        $qb
            ->andWhere($qb->expr()->eq(static::getAlias('createdFromReportSpa'), ':report'))
            ->setParameter('report', $report)
            ->indexBy(static::ROOT_ALIAS, static::getAliasId())
        ;
        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DataSpaInvReferralFee::class);
    }

    public function add(DataSpaInvReferralFee $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DataSpaInvReferralFee $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return DataSpaInvReferralFee[] Returns an array of DataSpaInvReferralFee objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?DataSpaInvReferralFee
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
