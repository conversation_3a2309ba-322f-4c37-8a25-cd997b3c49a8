<?php

namespace App\Repository\TaskMan;

use App\Entity\Sonata\User;
use App\Entity\TaskMan\CreativeTaskDay;
use App\Repository\GravitiqRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<CreativeTaskDay>
 *
 * @method CreativeTaskDay|null find($id, $lockMode = null, $lockVersion = null)
 * @method CreativeTaskDay|null findOneBy(array $criteria, array $orderBy = null)
 * @method CreativeTaskDay[]    findAll()
 * @method CreativeTaskDay[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CreativeTaskDayRepository extends GravitiqRepository
{
    public const string ROOT_ALIAS = 'tm_ctd';

    public function calculateHoursWorkedByUserOnDate(User $user, \DateTimeInterface $workDate): float
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS)
            ->select('SUM(' . self::getAlias('taskHours') . ')');
        $qb
            ->leftJoin(self::getAlia<PERSON>('task'), CreativeTaskRepository::ROOT_ALIAS)
            ->andWhere($qb->expr()->eq(CreativeTaskRepository::getAlias('assignedUser'), ':user'))
            ->andWhere($qb->expr()->eq(self::getAlias('workDate'), ':workDate'))
            ->setParameter('user', $user)
            ->setParameter('workDate', $workDate)
        ;
        $result = $qb
            ->getQuery()
            ->getSingleScalarResult()
        ;
        if (empty($result)) {
            return 0.0;
        } else {
            return (float) $result;
        }
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CreativeTaskDay::class);
    }

//    /**
//     * @return CreativeTaskDay[] Returns an array of CreativeTaskDay objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?CreativeTaskDay
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
