<?php

namespace App\Repository\TaskMan;

use App\Entity\TaskMan\TranslationCreativeRequest;
use App\Repository\GravitiqRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<TranslationCreativeRequest>
 *
 * @method TranslationCreativeRequest|null find($id, $lockMode = null, $lockVersion = null)
 * @method TranslationCreativeRequest|null findOneBy(array $criteria, array $orderBy = null)
 * @method TranslationCreativeRequest[]    findAll()
 * @method TranslationCreativeRequest[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TranslationCreativeRequestRepository extends GravitiqRepository
{
    public const string ROOT_ALIAS = 'tm_euct';

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TranslationCreativeRequest::class);
    }

//    /**
//     * @return TranslationCreativeRequest[] Returns an array of TranslationCreativeRequest objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('e.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?TranslationCreativeRequest
//    {
//        return $this->createQueryBuilder('e')
//            ->andWhere('e.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
