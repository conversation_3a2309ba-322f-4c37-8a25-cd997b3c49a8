<?php

namespace App\Repository\Tracker;

use App\Entity\Tracker\BlueprintDocCompetitor3Image;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BlueprintDocCompetitor3Image>
 *
 * @method BlueprintDocCompetitor3Image|null find($id, $lockMode = null, $lockVersion = null)
 * @method BlueprintDocCompetitor3Image|null findOneBy(array $criteria, array $orderBy = null)
 * @method BlueprintDocCompetitor3Image[]    findAll()
 * @method BlueprintDocCompetitor3Image[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class BlueprintDocCompetitor3ImageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BlueprintDocCompetitor3Image::class);
    }

//    /**
//     * @return BlueprintDocCompetitor3Image[] Returns an array of BlueprintDocCompetitor3Image objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('b')
//            ->andWhere('b.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('b.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?BlueprintDocCompetitor3Image
//    {
//        return $this->createQueryBuilder('b')
//            ->andWhere('b.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
