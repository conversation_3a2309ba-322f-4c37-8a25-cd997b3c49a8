<?php

namespace App\Repository\Tracker;

use App\Entity\Tracker\LogisticCompany;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LogisticCompany>
 *
 * @method LogisticCompany|null find($id, $lockMode = null, $lockVersion = null)
 * @method LogisticCompany|null findOneBy(array $criteria, array $orderBy = null)
 * @method LogisticCompany[]    findAll()
 * @method LogisticCompany[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LogisticCompanyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LogisticCompany::class);
    }

//    /**
//     * @return LogisticCompany[] Returns an array of LogisticCompany objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('l')
//            ->andWhere('l.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('l.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?LogisticCompany
//    {
//        return $this->createQueryBuilder('l')
//            ->andWhere('l.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
