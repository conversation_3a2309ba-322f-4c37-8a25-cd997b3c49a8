<?php

namespace App\Repository\Tracker;

use App\Entity\Tracker\BlueprintDocCompetitor2Image;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BlueprintDocCompetitor2Image>
 *
 * @method BlueprintDocCompetitor2Image|null find($id, $lockMode = null, $lockVersion = null)
 * @method BlueprintDocCompetitor2Image|null findOneBy(array $criteria, array $orderBy = null)
 * @method BlueprintDocCompetitor2Image[]    findAll()
 * @method BlueprintDocCompetitor2Image[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class BlueprintDocCompetitor2ImageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BlueprintDocCompetitor2Image::class);
    }

//    /**
//     * @return BlueprintDocCompetitor2Image[] Returns an array of BlueprintDocCompetitor2Image objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('b')
//            ->andWhere('b.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('b.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?BlueprintDocCompetitor2Image
//    {
//        return $this->createQueryBuilder('b')
//            ->andWhere('b.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
