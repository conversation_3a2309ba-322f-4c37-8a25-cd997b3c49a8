<?php

namespace App\Repository\Tracker;

use App\Entity\Tracker\SalesForecastGroup;
use App\Repository\GravitiqRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends GravitiqRepository<SalesForecastGroup>
 *
 * @method SalesForecastGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method SalesForecastGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method SalesForecastGroup[]    findAll()
 * @method SalesForecastGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SalesForecastGroupRepository extends GravitiqRepository
{
    const string ROOT_ALIAS = 'sfg';

    /**
     * @param list<array{blueprintId: int, sfRegion: string, channelFamily: string}> $arrayOfBlueprintsAndRegions
     * @param \DateTimeInterface $targetMonth
     * @return array<string, SalesForecastGroup>
     */
    public function retrieveForBlueprintRegionMonth(array $arrayOfBlueprintsAndRegions, \DateTimeInterface $targetMonth): array
    {
        if (empty($arrayOfBlueprintsAndRegions)) {
            return [];
        }

        $qb = $this
            ->createQueryBuilder(self::ROOT_ALIAS)
            ->leftJoin(self::getAlias('sfDetails'), SalesForecastDetailRepository::ROOT_ALIAS)
            ->select(self::ROOT_ALIAS, SalesForecastDetailRepository::ROOT_ALIAS)
        ;

        $qb
            ->andWhere($qb->expr()->eq(self::getAlias('forecastMadeInMonth'), ':targetMonth'))
            ->setParameter('targetMonth', $targetMonth)
        ;

        $orClauses = [];
        foreach ($arrayOfBlueprintsAndRegions as $i => $data) {
            $orClauses[] = $qb->expr()->andX(
                $qb->expr()->eq(self::getAlias('blueprint'), ":blueprint{$i}"),
                $qb->expr()->eq(self::getAlias('sfRegion'), ":region{$i}"),
                $qb->expr()->eq(self::getAlias('channelFamily'), ":family{$i}")
            );
            $qb
                ->setParameter("blueprint{$i}", $data['blueprintId'])
                ->setParameter("region{$i}", $data['sfRegion'])
                ->setParameter("family{$i}", $data['channelFamily'])
            ;
        }
        $qb->andWhere($qb->expr()->orX(...$orClauses));

        $results = $qb
            ->getQuery()
            ->getResult()
        ;

        $output = [];
        /** @var SalesForecastGroup $result */
        foreach ($results as $result) {
            $key = $result->getBlueprint()->getId() . '~' . $result->getSfRegion() . '~' . $result->getChannelFamily();
            $output[$key] = $result;
        }
        return $output;
    }

    public function hasFirstSaleDateForSalesForecast(SalesForecastGroup $sfg): bool
    {
        $sql = <<<SQL
            SELECT dmm.firstDate
              FROM data_int_dsr_cached_minmax dmm
              JOIN top_channel tc ON dmm.channelId = tc.id
             WHERE dmm.blueprintId = :blueprintId
               AND tc.id LIKE :channelFamily
               AND tc.sfRegion = :sfRegion
              LIMIT 1
        SQL;

        $result = $this->executeSqlWithParams($sql, [
            'blueprintId'   => $sfg->getBlueprint()->getId(),
            'channelFamily' => $sfg->getChannelFamily() . '_%',
            'sfRegion'      => $sfg->getSfRegion(),
        ]);

        return !empty($result);
    }

    public function retrieveSfgByIskuChannelFamilyRegionMonth(string $blueprintIsku, string $channelFamily, string $sfRegion, \DateTimeImmutable $forecastMadeInMonth): ?SalesForecastGroup
    {
        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->leftJoin(self::getAlias('blueprint'), BlueprintRepository::ROOT_ALIAS)
            ->where($qb->expr()->eq(BlueprintRepository::getAlias('isku'), ':blueprintIsku'))
            ->andWhere($qb->expr()->eq(self::getAlias('channelFamily'), ':channelFamily'))
            ->andWhere($qb->expr()->eq(self::getAlias('sfRegion'), ':sfRegion'))
            ->andWhere($qb->expr()->eq(self::getAlias('forecastMadeInMonth'), ':forecastMadeInMonth'))
            ->setParameter('blueprintIsku', $blueprintIsku)
            ->setParameter('channelFamily', $channelFamily)
            ->setParameter('sfRegion', $sfRegion)
            ->setParameter('forecastMadeInMonth', $forecastMadeInMonth);

        try {
            $result = $qb->getQuery()->getOneOrNullResult();
        } catch (\Exception) {
            throw new \RuntimeException("Non-unique result looking for SFG by $blueprintIsku with Channel: $channelFamily and Region: $sfRegion");
        }

        return $result;
    }

    public function retrieveSfgForGivenMonth(?string $forecastMadeInMonth = null): array
    {
        if (is_null($forecastMadeInMonth)) {
            $forecastMadeInMonth = new \DateTimeImmutable('midnight first day of this month');
            $forecastMadeInMonth = \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $forecastMadeInMonth->format('Y-m-01'));
        }

        $qb = $this->createQueryBuilder(self::ROOT_ALIAS);
        $qb
            ->where($qb->expr()->eq(self::getAlias('forecastMadeInMonth'), ':forecastMadeInMonth'))
            ->setParameter('forecastMadeInMonth', $forecastMadeInMonth);

        return $qb->getQuery()->getResult();
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SalesForecastGroup::class);
    }

    public function save(SalesForecastGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SalesForecastGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return SalesForecastGroup[] Returns an array of SalesForecastGroup objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('s.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?SalesForecastGroup
//    {
//        return $this->createQueryBuilder('s')
//            ->andWhere('s.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
