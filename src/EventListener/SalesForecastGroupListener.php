<?php

namespace App\EventListener;

use App\Entity\Tracker\SalesForecastGroup;
use App\Service\Internal\SalesForecastManager;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;

class SalesForecastGroupListener
{
    /**
     * @var list<array{object: SalesForecastGroup, oldScCat: string, newScCat: string}> $changesToNotify
     */
    private array $changesToNotify = [];

    public function __construct(
        protected LoggerInterface $logger,
        protected SalesForecastManager $salesForecastManager
    )
    {}

    public function preUpdate(SalesForecastGroup $object, PreUpdateEventArgs $eventArgs): void
    {
        if ($eventArgs->hasChangedField('supplyChainCategory')) {
            $oldScCat = $eventArgs->getOldValue('supplyChainCategory');
            $newScCat = $eventArgs->getNewValue('supplyChainCategory');

            if ($oldScCat !== $newScCat) {
                $this->changesToNotify[] = [
                    'object' => $object,
                    'oldScCat' => $oldScCat,
                    'newScCat' => $newScCat
                ];
            }
        }
    }

    public function postFlush(PostFlushEventArgs $eventArgs): void
    {
        if (!empty($this->changesToNotify)) {
            foreach ($this->changesToNotify as $change) {
                $this->salesForecastManager->sendSlackMessage($change['object'], $change['oldScCat'], $change['newScCat']);
            }

            $this->changesToNotify = [];
        }
    }
}