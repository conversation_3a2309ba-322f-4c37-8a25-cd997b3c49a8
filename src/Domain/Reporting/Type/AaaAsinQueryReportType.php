<?php

namespace App\Domain\Reporting\Type;

use App\Domain\Reporting\DTO\AaaAsinQueryReportDTO;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class AaaAsinQueryReportType extends BaseReportType
{
    /**
     * @return class-string
     */
    public function getDataClass(): string
    {
        return AaaAsinQueryReportDTO::class;
    }

    /**
     * @return array<string, string|array{type:class-string, options:array<string, mixed>}>
     */
    protected function getFieldConfiguration(): array
    {
        return [
            'startDate'    => ['type'=>DateType::class, 'options' => ['widget' => 'single_text']],
            'endDate'      => ['type'=>DateType::class, 'options' => ['widget' => 'single_text']],
            'cAsin'        => ['type'=>TextType::class, 'options' => ['label' => 'cASIN', 'help' => 'Can enter multiple separated by comma']],
            'brandCountry' => ['type'=>TextType::class, 'options'=>['required'=>false, 'help' => 'Used as prefix match on adAccount column']],
            'keywords'     => ['type'=>TextareaType::class, 'options' => ['help' => 'Use % as wildcard, e.g. xyz% will match anything starting with xyz. Can enter multiple keywords, one per line.']],
        ];
    }
}