<?php

namespace App\Domain\Reporting\DataTable;

use App\Domain\DataTable\Column\GravitiqNumberColumn;
use App\Domain\DataTable\Column\GravitiqTextColumn;
use App\Domain\DataTable\GravitiqDataTable;
use App\Domain\Reporting\DTO\BaseReportDTO;
use App\Domain\Reporting\DTO\ShipmentMonitoringReportDTO;
use App\Domain\Reporting\Type\BaseReportType;
use App\Domain\Reporting\Type\ShipmentMonitoringReportType;
use App\Entity\Tracker\Shipment;
use App\Repository\Tracker\ShipmentRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\NotSupported;
use Omines\DataTablesBundle\DataTable;

class ShipmentMonitoringDataTableConfig extends GravitiqDataTableConfig
{
    /**
     * @param GravitiqDataTable $dataTable
     * @param array<string, mixed> $options
     */
    public function configure(DataTable $dataTable, array $options): void
    {
        $dataTable
            ->add('shortName', GravitiqTextColumn::class)
            ->add('poNumber', GravitiqTextColumn::class)
            ->add('invoiceNumber', GravitiqTextColumn::class)
            ->add('orderDate', GravitiqTextColumn::class)
            ->add('unitsOrdered', GravitiqNumberColumn::class)
            ->add('isku', GravitiqTextColumn::class, GravitiqTextColumn::buildOptionsToRenderWithNonBreakingDashes())
            ->add('plannedDestinationCountry', GravitiqTextColumn::class, [
                'label' => 'toCountry',
            ])
            ->add('fbaCode', GravitiqTextColumn::class)
            ->add('paidAmount', GravitiqNumberColumn::class, GravitiqNumberColumn::buildOptionsToRenderIntAsDecimal())
            ->add('boxQtyTotal', GravitiqNumberColumn::class)
            ->add('boxQtyBooked', GravitiqNumberColumn::class)
            ->add('boxQtyAtEnd', GravitiqTextColumn::class)
            ->add('etd', GravitiqTextColumn::class, GravitiqTextColumn::buildOptionsToRenderWithNonBreakingDashes())
            ->add('eta', GravitiqTextColumn::class, GravitiqTextColumn::buildOptionsToRenderWithNonBreakingDashes())
            ->add('actualTimeArrival', GravitiqTextColumn::class)
            ->add('carrierTracking', GravitiqTextColumn::class)
            ->add('carrier', GravitiqTextColumn::class)
            ->add('amount', GravitiqNumberColumn::class, GravitiqNumberColumn::buildOptionsToRenderIntAsDecimal())
            ->add('cachedTransitCostUsd', GravitiqNumberColumn::class, GravitiqNumberColumn::buildOptionsToRenderIntAsDecimal('$') + [
                'label' => 'transitCost',
            ])
            ->add('internalTracking', GravitiqTextColumn::class)
        ;

        $dataTable->setDataTableConfig($this);
    }

    public function getFormType(): BaseReportType
    {
        return new ShipmentMonitoringReportType();
    }

    /**
     * @param ShipmentMonitoringReportDTO $dto
     * @param EntityManager $em
     * @return list<array<string, mixed>>
     */
    public function doBuildResultsFromDTO(BaseReportDTO $dto, EntityManager $em): array
    {
        try {
            /** @var ShipmentRepository $repo */
            $repo = $em->getRepository(Shipment::class);
        } catch (NotSupported $e) {
            throw new \RuntimeException('Repository not found: ' . $e->getMessage());
        }
        return $repo->retrieveShipmentMonitoringInDateRangeUsingDTO($dto);
    }
}