<?php

namespace App\Entity\Internal;

use App\Repository\Internal\TempReportHeaderRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'temp_report_header')]
#[ORM\Entity(repositoryClass: TempReportHeaderRepository::class)]
class TempReportHeader
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    protected $id;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $sourceId;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    protected string $notes;

    #[ORM\Column(type: Types::TEXT)]
    protected string $header;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSourceId(): ?int
    {
        return $this->sourceId;
    }

    public function setSourceId(int $sourceId): self
    {
        $this->sourceId = $sourceId;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getHeader(): ?string
    {
        return $this->header;
    }

    public function setHeader(string $header): self
    {
        $this->header = $header;

        return $this;
    }
}