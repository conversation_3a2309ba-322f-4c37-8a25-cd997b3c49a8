<?php

namespace App\Entity;

use App\Repository\DataSpaInvPanEuEligibilityRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'data_spa_rep_inv_pan_eu_elig')]
#[ORM\Entity(repositoryClass: DataSpaInvPanEuEligibilityRepository::class)]
class DataSpaInvPanEuEligibility
{
    use TsvSourcedDataTrait;

    public function __call($method, $args)
    {
        if (preg_match('/^translateAndSet(\w+)$/', $method, $matches)) {
            $propertyName = $matches[1];
            $setterMethod = 'set' . $propertyName;

            if (method_exists($this, $setterMethod)) {
                $arg = $args[0] ?? null;
                if (!empty($arg)) {
                    $arg = $this->translateOffer($arg);
                }
                $this->$setterMethod($arg);
            } else {
                throw new \BadMethodCallException("The method {$setterMethod} does not exist (called from $method).");
            }
        } else {
            throw new \BadMethodCallException("The method {$method} does not exist.");
        }
    }

    public const array REPORT_OBJECT_SETTER_MAP = [
        'ASIN'                              => 'setAsin',
        'MerchantSKU'                       => 'setSku',
        'SKU venditore'                     => 'setSku',
        'Enrol'                             => 'setEnrol',
        'Iscrizione'                        => 'setEnrol',
        'FNSKU'                             => 'setFnsku',
        'Pan-EU status'                     => 'translateAndSetPanEuStatus',
        'Stato Pan UE'                      => 'translateAndSetPanEuStatus',
        'Enrollment Date'                   => 'setEnrollmentDate',
        'Title'                             => 'setTitle',
        'UK offer status'                   => 'translateAndSetOfferUK',
        "Stato dell'offerta UK"             => 'translateAndSetOfferUK',
        'DE offer status'                   => 'translateAndSetOfferDE',
        "Stato dell'offerta DE"             => 'translateAndSetOfferDE',
        'FR offer status'                   => 'translateAndSetOfferFR',
        "Stato dell'offerta FR"             => 'translateAndSetOfferFR',
        'IT offer status'                   => 'translateAndSetOfferIT',
        "Stato dell'offerta IT"             => 'translateAndSetOfferIT',
        'ES offer status'                   => 'translateAndSetOfferES',
        "Stato dell'offerta ES"             => 'translateAndSetOfferES',
        'NL offer status'                   => 'translateAndSetOfferNL',
        "Stato dell'offerta NL"             => 'translateAndSetOfferNL',
        'SE offer status'                   => 'translateAndSetOfferSE',
        "Stato dell'offerta SE"             => 'translateAndSetOfferSE',
        'PL offer status'                   => 'translateAndSetOfferPL',
        "Stato dell'offerta PL"             => 'translateAndSetOfferPL',
        'BE Offer Status'                   => 'translateAndSetOfferBE',
        "Stato dell'offerta in Belgio"      => 'translateAndSetOfferBE',
        'IE Offer Status'                   => 'translateAndSetOfferIE',
        'Last active on'                    => 'setLastActiveDate',
        "Ultima volta attivo il"            => 'setLastActiveDate',
        'Date Pan-EU expires'               => 'setPanEuEndsOnDate',
        "Data di scadenza Pan EU"           => 'setPanEuEndsOnDate',
        'Product comments'                  => 'setProductComments',
        "Commenti al prodotto"              => 'setProductComments',
        'UK PanEU benefits'                 => 'setPanEuBenefitsUK',
        "Vantaggi PanEU UK"                 => 'setPanEuBenefitsUK',
        'DE PanEU benefits'                 => 'setPanEuBenefitsDE',
        "Vantaggi PanEU DE"                 => 'setPanEuBenefitsDE',
        'FR PanEU benefits'                 => 'setPanEuBenefitsFR',
        "Vantaggi PanEU FR"                 => 'setPanEuBenefitsFR',
        'IT PanEU benefits'                 => 'setPanEuBenefitsIT',
        "Vantaggi PanEU IT"                 => 'setPanEuBenefitsIT',
        'ES PanEU benefits'                 => 'setPanEuBenefitsES',
        "Vantaggi PanEU ES"                 => 'setPanEuBenefitsES',
        'NL PanEU benefits'                 => 'setPanEuBenefitsNL',
        "Vantaggi PanEU NL"                 => 'setPanEuBenefitsNL',
        'SE PanEU benefits'                 => 'setPanEuBenefitsSE',
        "Vantaggi PanEU SE"                 => 'setPanEuBenefitsSE',
        'PL PanEU benefits'                 => 'setPanEuBenefitsPL',
        "Vantaggi PanEU PL"                 => 'setPanEuBenefitsPL',
        'BE PanEU Benefits'                 => 'setPanEuBenefitsBE',
        "Vantaggi Paneuropei in Belgio"     => 'setPanEuBenefitsBE',
        'IE PanEU Benefits'                 => 'setPanEuBenefitsIE',
        'UK SnL Status'                     => 'setPanEuSnlUK',
        "Stato SNL UK"                      => 'setPanEuSnlUK',
        'DE SnL Status'                     => 'setPanEuSnlDE',
        "Stato SnL DE"                      => 'setPanEuSnlDE',
        'FR SnL Status'                     => 'setPanEuSnlFR',
        "Stato SnL FR"                      => 'setPanEuSnlFR',
        'IT SnL Status'                     => 'setPanEuSnlIT',
        "Stato SnL IT"                      => 'setPanEuSnlIT',
        'ES SnL Status'                     => 'setPanEuSnlES',
        "Stato SnL ES"                      => 'setPanEuSnlES',
    ];

    protected function translateOffer(string $input): string
    {
        $dataTranslation = [
            'Idoneo'                        => 'Eligible',
            'Nessun offerta'                => 'No listing',
            'Nessuna offerta obbligatoria'  => 'No offer required',
// Other known values for panEuStatus which may need translation
//            'Ineligible'
//            'Enrolled'
//            'Enrolment ended'
//            'Enrolment ending soon'
//            'Failed to retrieve data'
// Other known values for panEuBenefitsXX which may need translation
//            'Product type'
//            'Food'
//            'Hazmat'
        ];

        return $dataTranslation[$input] ?? $input;
    }
    public static function getPotentialSetters(): array
    {
        return static::REPORT_OBJECT_SETTER_MAP;
    }


    public function setEnrollmentDate(\DateTime|string $date): self
    {
        if ($date instanceof \DateTime) {
            $this->enrollmentDate = $date;
        } else {
            $this->enrollmentDate = new \DateTime($date);
        }

        return $this;
    }
    public function setLastActiveDate(\DateTime|string $date): self
    {
        if ($date instanceof \DateTime) {
            $this->lastActiveDate = $date;
        } else {
            $this->lastActiveDate = new \DateTime($date);
        }

        return $this;
    }
    public function setPanEuEndsOnDate(\DateTime|string $date): self
    {
        if ($date instanceof \DateTime) {
            $this->panEuEndsOnDate = $date;
        } else {
            $this->panEuEndsOnDate = new \DateTime($date);
        }

        return $this;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    protected $id;

    #[ORM\ManyToOne(targetEntity: AccountProfileSpa::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected AccountProfileSpa $spaProfile;

    #[ORM\ManyToOne(targetEntity: AmazonReportSpa::class)]
    #[ORM\JoinColumn(name: 'reportId', nullable: false)]
    protected ?AmazonReportSpa $createdFromReportSpa;

    #[ORM\Column(name: 'asin', type: Types::STRING, length: 10)]
    protected string $asin;

    #[ORM\Column(name: 'sku', type: Types::STRING, length: 40)]
    protected string $sku;

    #[ORM\Column(name: 'enrol', type: Types::BOOLEAN)]
    protected bool $enrol;

    #[ORM\Column(name: 'fnsku', type: Types::STRING, length: 10)]
    protected string $fnsku;

    #[ORM\Column(name: 'panEuStatus', type: Types::STRING, length: 30)]
    protected string $panEuStatus;

    #[ORM\Column(name: 'enrollmentDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $enrollmentDate = null;

    #[ORM\Column(name: 'offerUK', type: Types::STRING, length: 20)]
    protected string $offerUK = '';

    #[ORM\Column(name: 'offerDE', type: Types::STRING, length: 20)]
    protected string $offerDE = '';

    #[ORM\Column(name: 'offerFR', type: Types::STRING, length: 20)]
    protected string $offerFR = '';

    #[ORM\Column(name: 'offerIT', type: Types::STRING, length: 20)]
    protected string $offerIT = '';

    #[ORM\Column(name: 'offerES', type: Types::STRING, length: 20)]
    protected string $offerES = '';

    #[ORM\Column(name: 'offerNL', type: Types::STRING, length: 20)]
    protected string $offerNL = '';

    #[ORM\Column(name: 'offerSE', type: Types::STRING, length: 20)]
    protected string $offerSE = '';

    #[ORM\Column(name: 'offerPL', type: Types::STRING, length: 20)]
    protected string $offerPL = '';

    #[ORM\Column(name: 'offerBE', type: Types::STRING, length: 20)]
    protected string $offerBE = '';

    #[ORM\Column(name: 'offerIE', type: Types::STRING, length: 20)]
    protected string $offerIE = '';

    #[ORM\Column(name: 'lastActiveDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $lastActiveDate = null;

    #[ORM\Column(name: 'panEuEndsOnDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $panEuEndsOnDate = null;

    #[ORM\Column(name: 'panEuBenefitsUK', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsUK = '';

    #[ORM\Column(name: 'panEuBenefitsDE', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsDE = '';

    #[ORM\Column(name: 'panEuBenefitsFR', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsFR = '';

    #[ORM\Column(name: 'panEuBenefitsIT', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsIT = '';

    #[ORM\Column(name: 'panEuBenefitsES', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsES = '';

    #[ORM\Column(name: 'panEuBenefitsNL', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsNL = '';

    #[ORM\Column(name: 'panEuBenefitsSE', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsSE = '';

    #[ORM\Column(name: 'panEuBenefitsPL', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsPL = '';

    #[ORM\Column(name: 'panEuBenefitsBE', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsBE = '';

    #[ORM\Column(name: 'panEuBenefitsIE', type: Types::STRING, length: 1)]
    protected string $panEuBenefitsIE = '';

    #[ORM\Column(name: 'panEuSnlUK', type: Types::STRING, length: 1)]
    protected string $panEuSnlUK = '';

    #[ORM\Column(name: 'panEuSnlDE', type: Types::STRING, length: 1)]
    protected string $panEuSnlDE = '';

    #[ORM\Column(name: 'panEuSnlFR', type: Types::STRING, length: 1)]
    protected string $panEuSnlFR = '';

    #[ORM\Column(name: 'panEuSnlIT', type: Types::STRING, length: 1)]
    protected string $panEuSnlIT = '';

    #[ORM\Column(name: 'panEuSnlES', type: Types::STRING, length: 1)]
    protected string $panEuSnlES = '';

    #[ORM\Column(name: 'title', type: Types::TEXT)]
    protected string $title;

    #[ORM\Column(name: 'productComments', type: Types::TEXT, nullable: true)]
    protected ?string $productComments = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAsin(): ?string
    {
        return $this->asin;
    }

    public function setAsin(string $asin): static
    {
        $this->asin = $asin;

        return $this;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    public function isEnrol(): ?bool
    {
        return $this->enrol;
    }

    public function setEnrol(bool $enrol): static
    {
        $this->enrol = $enrol;

        return $this;
    }

    public function getFnsku(): ?string
    {
        return $this->fnsku;
    }

    public function setFnsku(string $fnsku): static
    {
        $this->fnsku = $fnsku;

        return $this;
    }

    public function getPanEuStatus(): ?string
    {
        return $this->panEuStatus;
    }

    public function setPanEuStatus(string $panEuStatus): static
    {
        $this->panEuStatus = $panEuStatus;

        return $this;
    }

    public function getEnrollmentDate(): ?\DateTimeInterface
    {
        return $this->enrollmentDate;
    }

    public function getOfferUK(): ?string
    {
        return $this->offerUK;
    }

    public function setOfferUK(string $offerUK): static
    {
        $this->offerUK = $offerUK;

        return $this;
    }

    public function getOfferDE(): ?string
    {
        return $this->offerDE;
    }

    public function setOfferDE(string $offerDE): static
    {
        $this->offerDE = $offerDE;

        return $this;
    }

    public function getOfferFR(): ?string
    {
        return $this->offerFR;
    }

    public function setOfferFR(string $offerFR): static
    {
        $this->offerFR = $offerFR;

        return $this;
    }

    public function getOfferIT(): ?string
    {
        return $this->offerIT;
    }

    public function setOfferIT(string $offerIT): static
    {
        $this->offerIT = $offerIT;

        return $this;
    }

    public function getOfferES(): ?string
    {
        return $this->offerES;
    }

    public function setOfferES(string $offerES): static
    {
        $this->offerES = $offerES;

        return $this;
    }

    public function getOfferNL(): ?string
    {
        return $this->offerNL;
    }

    public function setOfferNL(string $offerNL): static
    {
        $this->offerNL = $offerNL;

        return $this;
    }

    public function getOfferSE(): ?string
    {
        return $this->offerSE;
    }

    public function setOfferSE(string $offerSE): static
    {
        $this->offerSE = $offerSE;

        return $this;
    }

    public function getOfferPL(): ?string
    {
        return $this->offerPL;
    }

    public function setOfferPL(string $offerPL): static
    {
        $this->offerPL = $offerPL;

        return $this;
    }

    public function getOfferBE(): ?string
    {
        return $this->offerBE;
    }

    public function setOfferBE(string $offerBE): static
    {
        $this->offerBE = $offerBE;

        return $this;
    }

    public function getOfferIE(): ?string
    {
        return $this->offerIE;
    }

    public function setOfferIE(string $offerIE): static
    {
        $this->offerIE = $offerIE;

        return $this;
    }

    public function getLastActiveDate(): ?\DateTimeInterface
    {
        return $this->lastActiveDate;
    }

    public function getPanEuEndsOnDate(): ?\DateTimeInterface
    {
        return $this->panEuEndsOnDate;
    }

    public function getPanEuBenefitsUK(): ?string
    {
        return $this->panEuBenefitsUK;
    }

    public function setPanEuBenefitsUK(string $panEuBenefitsUK): static
    {
        $this->panEuBenefitsUK = $panEuBenefitsUK;

        return $this;
    }

    public function getPanEuBenefitsDE(): ?string
    {
        return $this->panEuBenefitsDE;
    }

    public function setPanEuBenefitsDE(string $panEuBenefitsDE): static
    {
        $this->panEuBenefitsDE = $panEuBenefitsDE;

        return $this;
    }

    public function getPanEuBenefitsFR(): ?string
    {
        return $this->panEuBenefitsFR;
    }

    public function setPanEuBenefitsFR(string $panEuBenefitsFR): static
    {
        $this->panEuBenefitsFR = $panEuBenefitsFR;

        return $this;
    }

    public function getPanEuBenefitsIT(): ?string
    {
        return $this->panEuBenefitsIT;
    }

    public function setPanEuBenefitsIT(string $panEuBenefitsIT): static
    {
        $this->panEuBenefitsIT = $panEuBenefitsIT;

        return $this;
    }

    public function getPanEuBenefitsES(): ?string
    {
        return $this->panEuBenefitsES;
    }

    public function setPanEuBenefitsES(string $panEuBenefitsES): static
    {
        $this->panEuBenefitsES = $panEuBenefitsES;

        return $this;
    }

    public function getPanEuBenefitsNL(): ?string
    {
        return $this->panEuBenefitsNL;
    }

    public function setPanEuBenefitsNL(string $panEuBenefitsNL): static
    {
        $this->panEuBenefitsNL = $panEuBenefitsNL;

        return $this;
    }

    public function getPanEuBenefitsSE(): ?string
    {
        return $this->panEuBenefitsSE;
    }

    public function setPanEuBenefitsSE(string $panEuBenefitsSE): static
    {
        $this->panEuBenefitsSE = $panEuBenefitsSE;

        return $this;
    }

    public function getPanEuBenefitsPL(): ?string
    {
        return $this->panEuBenefitsPL;
    }

    public function setPanEuBenefitsPL(string $panEuBenefitsPL): static
    {
        $this->panEuBenefitsPL = $panEuBenefitsPL;

        return $this;
    }

    public function getPanEuBenefitsBE(): ?string
    {
        return $this->panEuBenefitsBE;
    }

    public function setPanEuBenefitsBE(string $panEuBenefitsBE): static
    {
        $this->panEuBenefitsBE = $panEuBenefitsBE;

        return $this;
    }

    public function getPanEuBenefitsIE(): ?string
    {
        return $this->panEuBenefitsIE;
    }

    public function setPanEuBenefitsIE(string $panEuBenefitsIE): static
    {
        $this->panEuBenefitsIE = $panEuBenefitsIE;

        return $this;
    }

    public function getPanEuSnlUK(): ?string
    {
        return $this->panEuSnlUK;
    }

    public function setPanEuSnlUK(string $panEuSnlUK): static
    {
        $this->panEuSnlUK = $panEuSnlUK;

        return $this;
    }

    public function getPanEuSnlDE(): ?string
    {
        return $this->panEuSnlDE;
    }

    public function setPanEuSnlDE(string $panEuSnlDE): static
    {
        $this->panEuSnlDE = $panEuSnlDE;

        return $this;
    }

    public function getPanEuSnlFR(): ?string
    {
        return $this->panEuSnlFR;
    }

    public function setPanEuSnlFR(string $panEuSnlFR): static
    {
        $this->panEuSnlFR = $panEuSnlFR;

        return $this;
    }

    public function getPanEuSnlIT(): ?string
    {
        return $this->panEuSnlIT;
    }

    public function setPanEuSnlIT(string $panEuSnlIT): static
    {
        $this->panEuSnlIT = $panEuSnlIT;

        return $this;
    }

    public function getPanEuSnlES(): ?string
    {
        return $this->panEuSnlES;
    }

    public function setPanEuSnlES(string $panEuSnlES): static
    {
        $this->panEuSnlES = $panEuSnlES;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getProductComments(): ?string
    {
        return $this->productComments;
    }

    public function setProductComments(?string $productComments): static
    {
        $this->productComments = $productComments;

        return $this;
    }

    public function getSpaProfile(): ?AccountProfileSpa
    {
        return $this->spaProfile;
    }

    public function setSpaProfile(?AccountProfileSpa $spaProfile): static
    {
        $this->spaProfile = $spaProfile;

        return $this;
    }

    public function getCreatedFromReportSpa(): ?AmazonReportSpa
    {
        return $this->createdFromReportSpa;
    }

    public function setCreatedFromReportSpa(?AmazonReportSpa $createdFromReportSpa): static
    {
        $this->createdFromReportSpa = $createdFromReportSpa;

        return $this;
    }
}
