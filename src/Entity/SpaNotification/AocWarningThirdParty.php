<?php

namespace App\Entity\SpaNotification;

use App\Entity\AccountAmazon;
use App\Entity\AccountProfileSpa;
use App\Repository\SpaNotification\AocWarningThirdPartyRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'spa_ntf_aoc_warning_3p')]
#[ORM\Entity(repositoryClass: AocWarningThirdPartyRepository::class)]
class AocWarningThirdParty
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    protected int $id;

    #[ORM\ManyToOne(targetEntity: AccountProfileSpa::class)]
    #[ORM\JoinColumn(nullable: true)]
    protected ?AccountProfileSpa $accountProfile = null;

    #[ORM\ManyToOne(targetEntity: AccountAmazon::class)]
    #[ORM\JoinColumn(nullable: true)]
    protected ?AccountAmazon $accountAmazon = null;

     #[ORM\ManyToOne(targetEntity: Subscription::class)]
    #[ORM\JoinColumn(referencedColumnName: 'azSubscriptionId', nullable: true)]
    protected ?Subscription $subscription = null;

    #[ORM\Column(type: Types::STRING, length: 10, name: 'asin')]
    protected string $asin;

    #[ORM\Column(type: Types::STRING, length: 16, name: 'thirdPartySellerCode')]
    protected string $thirdPartySellerCode;

    #[ORM\Column(type: Types::INTEGER, name: 'thirdPartySellerPrice')]
    protected int $thirdPartySellerPrice;

    #[ORM\Column(type: Types::STRING, length: 30, nullable: true, name: 'itemCondition')]
    protected ?string $itemCondition = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, name: 'firstSeenDate')]
    protected \DateTimeInterface $firstSeenDate;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, name: 'lastUpdateDate')]
    protected \DateTimeInterface $lastUpdateDate;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true, name: 'lastSentWarningDate')]
    protected ?\DateTimeInterface $lastSentWarningDate = null;

    #[ORM\Column(type: Types::BOOLEAN, name: 'active')]
    protected bool $active = true;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAsin(): ?string
    {
        return $this->asin;
    }

    public function setAsin(string $asin): self
    {
        $this->asin = $asin;

        return $this;
    }

    public function getThirdPartySellerCode(): ?string
    {
        return $this->thirdPartySellerCode;
    }

    public function setThirdPartySellerCode(string $thirdPartySellerCode): self
    {
        $this->thirdPartySellerCode = $thirdPartySellerCode;

        return $this;
    }

    public function getThirdPartySellerPrice(): ?int
    {
        return $this->thirdPartySellerPrice;
    }

    public function setThirdPartySellerPrice(int $thirdPartySellerPrice): self
    {
        $this->thirdPartySellerPrice = $thirdPartySellerPrice;

        return $this;
    }

    public function getItemCondition(): ?string
    {
        return $this->itemCondition;
    }

    public function setItemCondition(?string $itemCondition): self
    {
        $this->itemCondition = $itemCondition;

        return $this;
    }

    public function getFirstSeenDate(): ?\DateTimeInterface
    {
        return $this->firstSeenDate;
    }

    public function setFirstSeenDate(\DateTimeInterface $firstSeenDate): self
    {
        $this->firstSeenDate = $firstSeenDate;

        return $this;
    }

    public function getLastUpdateDate(): ?\DateTimeInterface
    {
        return $this->lastUpdateDate;
    }

    public function setLastUpdateDate(\DateTimeInterface $lastUpdateDate): self
    {
        $this->lastUpdateDate = $lastUpdateDate;

        return $this;
    }

    public function getLastSentWarningDate(): ?\DateTimeInterface
    {
        return $this->lastSentWarningDate;
    }

    public function setLastSentWarningDate(?\DateTimeInterface $lastSentWarningDate): self
    {
        $this->lastSentWarningDate = $lastSentWarningDate;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getAccountProfile(): ?AccountProfileSpa
    {
        return $this->accountProfile;
    }

    public function setAccountProfile(?AccountProfileSpa $accountProfile): self
    {
        $this->accountProfile = $accountProfile;

        return $this;
    }

    public function getAccountAmazon(): ?AccountAmazon
    {
        return $this->accountAmazon;
    }

    public function setAccountAmazon(?AccountAmazon $accountAmazon): self
    {
        $this->accountAmazon = $accountAmazon;

        return $this;
    }

    public function getSubscription(): ?Subscription
    {
        return $this->subscription;
    }

    public function setSubscription(?Subscription $subscription): self
    {
        $this->subscription = $subscription;

        return $this;
    }

}
