<?php

namespace App\Entity\Spa\Fba;

use App\Entity\AccountAmazon;
use App\Entity\AccountProfileSpa;
use App\Entity\AmazonReportSpa;
use App\Entity\ComparableTrait;
use App\Entity\ComparableTraitInterface;
use App\Entity\MagicMutatorTrait;
use App\Entity\TsvSourcedDataTrait;
use App\Repository\Spa\Fba\DataSpaFbaSnsForecastRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'data_spa_fba_sns_forecast')]
#[ORM\Entity(repositoryClass: DataSpaFbaSnsForecastRepository::class)]
class DataSpaFbaSnsForecast implements ComparableTraitInterface
{
    use TsvSourcedDataTrait, ComparableTrait, MagicMutatorTrait;

    public const array REPORT_OBJECT_SETTER_MAP = [
        'offer-state'                             => 'setOfferState',
        'snapshot-date'                           => 'setSnapshotDateFromTzString',
        'sku'                                     => 'setSku',
        'fnsku'                                   => null,
        'asin'                                    => 'setAsin',
        'estimated-avg-sns-discount-next-8-weeks' => 'setEstAvgSnsDiscountNext8Weeks',
        'product-name'                            => null,
        'country'                                 => 'setCountry',
        'active-subscriptions'                    => 'setActiveSubscriptions',
        'week-1-start-date'                       => 'setWeek1StartDateFromTzString',
        'scheduled-sns-units-week-1'              => 'setScheduledSnsUnitsWeek1',
        'scheduled-sns-units-week-2'              => 'setScheduledSnsUnitsWeek2',
        'scheduled-sns-units-week-3'              => 'setScheduledSnsUnitsWeek3',
        'scheduled-sns-units-week-4'              => 'setScheduledSnsUnitsWeek4',
        'scheduled-sns-units-week-5'              => 'setScheduledSnsUnitsWeek5',
        'scheduled-sns-units-week-6'              => 'setScheduledSnsUnitsWeek6',
        'scheduled-sns-units-week-7'              => 'setScheduledSnsUnitsWeek7',
        'scheduled-sns-units-week-8'              => 'setScheduledSnsUnitsWeek8',
    ];

    /**
     * @return array<string, string>
     */
    public static function getPotentialSetters(): array
    {
        return static::REPORT_OBJECT_SETTER_MAP;
    }

    /**
     * @param AccountProfileSpa $profile
     * @return array<string, ?string>
     */
    public static function getExpectedHeaders(AccountProfileSpa $profile): array
    {
        throw new \InvalidArgumentException('getExpectedHeaders() is not implemented in DataSpaOrdItem');
    }

    /**
     * @return string[]
     */
    protected function comparisonIgnoresKeys(): array
    {
        return ['createdFromReportSpa','updatedFromReportSpa'];
    }

    public function buildKey(): string
    {
        $key = '';
        if ($this->getAccountAmazon()) {
            $key .= $this->getAccountAmazon()->getAlias() . '-';
        } else {
            $key .= 'AA?-';
        }
        if ($this->getSku()) {
            $key .= $this->getSku() . '-';
        } else {
            $key .= 'S?-';
        }
        if ($this->getAsin()) {
            $key .= $this->getAsin() . '-';
        } else {
            $key .= 'A?-';
        }
        if ($this->getCountry()) {
            $key .= $this->getCountry() . '-';
        } else {
            $key .= 'C?-';
        }
        if ($this->getSnapshotDate()) {
            $key .= $this->getSnapshotDate()->format('c') . '-';
        } else {
            $key .= 'D?-';
        }

        return $key;
    }

    /** @noinspection PhpUnused - actually called via REPORT_OBJECT_SETTER_MAP */
    public function setSnapshotDateFromTzString(string $dateWithOffset): static
    {
        try {
            $originalDate = GravitiqTools::castToDateTimeImmutable($dateWithOffset);
            /** @var \DateTimeImmutable $utcDate */
            $utcDate = GravitiqTools::convertDateToDatabaseTimeZone($originalDate);
        } catch (\Exception) {
            throw new \InvalidArgumentException("Invalid date string: $dateWithOffset in DataSpaFbaSnsForecast::setSnapshotDateFromTzString()");
        }
        return $this->setSnapshotDate($utcDate);
    }

    /** @noinspection PhpUnused - actually called via REPORT_OBJECT_SETTER_MAP */
    public function setWeek1StartDateFromTzString(string $dateWithOffset): static
    {
        try {
            $originalDate = GravitiqTools::castToDateTimeImmutable($dateWithOffset);
            /** @var \DateTimeImmutable $utcDate */
            $utcDate = GravitiqTools::convertDateToDatabaseTimeZone($originalDate);
        } catch (\Exception) {
            throw new \InvalidArgumentException("Invalid date string: $dateWithOffset in DataSpaFbaSnsForecast::setWeek1StartDateFromTzString()");
        }
        return $this->setWeek1StartDate($utcDate);
    }

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: AmazonReportSpa::class)]
    #[ORM\JoinColumn(name: 'reportCreateId', nullable: false)]
    private ?AmazonReportSpa $createdFromReportSpa;

    #[ORM\ManyToOne(targetEntity: AmazonReportSpa::class)]
    #[ORM\JoinColumn(name: 'reportUpdateId', nullable: true)]
    private ?AmazonReportSpa $updatedFromReportSpa;

    #[ORM\ManyToOne(targetEntity: AccountAmazon::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected AccountAmazon $accountAmazon;

    /*********************************************
     *** Field definitions from report mapping ***
     *********************************************/

    #[ORM\Column(type: Types::STRING, length: 20)]
    private string $offerState;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTimeInterface $snapshotDate;

    #[ORM\Column(type: Types::STRING, length: 40)]
    #[Assert\Length(min: 1, max: 40)]
    private string $sku;

    #[ORM\Column(type: Types::STRING, length: 10)]
    private string $asin;

    #[ORM\Column(type: Types::STRING, length: 2)]
    private string $country;

    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 4, nullable: true)]
    private ?float $estAvgSnsDiscountNext8Weeks = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $activeSubscriptions;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $week1StartDate;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek1 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek2 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek3 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek4 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek5 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek6 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek7 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $scheduledSnsUnitsWeek8 = null;

    /*******************************
     *** Modified generated code ***
     ******************************/

    public function getEstAvgSnsDiscountNext8Weeks(): ?float
    {
        return (float)$this->estAvgSnsDiscountNext8Weeks;
    }

    public function setEstAvgSnsDiscountNext8Weeks(int|float|string|null $estAvgSnsDiscountNext8Weeks): static
    {
        if (is_string($estAvgSnsDiscountNext8Weeks)) {
            if (str_contains($estAvgSnsDiscountNext8Weeks, '%')) {
                $estAvgSnsDiscountNext8Weeks = str_replace('%', '', $estAvgSnsDiscountNext8Weeks);
            }
            $estAvgSnsDiscountNext8Weeks = (float)$estAvgSnsDiscountNext8Weeks;
        }

        $this->estAvgSnsDiscountNext8Weeks = $estAvgSnsDiscountNext8Weeks;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOfferState(): ?string
    {
        return $this->offerState;
    }

    public function setOfferState(string $offerState): static
    {
        $this->offerState = $offerState;

        return $this;
    }

    public function getSnapshotDate(): ?\DateTimeInterface
    {
        return $this->snapshotDate;
    }

    public function setSnapshotDate(\DateTimeInterface $snapshotDate): static
    {
        $this->snapshotDate = $snapshotDate;

        return $this;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    public function getAsin(): ?string
    {
        return $this->asin;
    }

    public function setAsin(string $asin): static
    {
        $this->asin = $asin;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getActiveSubscriptions(): ?int
    {
        return $this->activeSubscriptions;
    }

    public function setActiveSubscriptions(int $activeSubscriptions): static
    {
        $this->activeSubscriptions = $activeSubscriptions;

        return $this;
    }

    public function getWeek1StartDate(): ?\DateTimeImmutable
    {
        return $this->week1StartDate;
    }

    public function setWeek1StartDate(\DateTimeImmutable $week1StartDate): static
    {
        $this->week1StartDate = $week1StartDate;

        return $this;
    }

    public function getScheduledSnsUnitsWeek1(): ?int
    {
        return $this->scheduledSnsUnitsWeek1;
    }

    public function setScheduledSnsUnitsWeek1(?int $scheduledSnsUnitsWeek1): static
    {
        $this->scheduledSnsUnitsWeek1 = $scheduledSnsUnitsWeek1;

        return $this;
    }

    public function getScheduledSnsUnitsWeek2(): ?int
    {
        return $this->scheduledSnsUnitsWeek2;
    }

    public function setScheduledSnsUnitsWeek2(?int $scheduledSnsUnitsWeek2): static
    {
        $this->scheduledSnsUnitsWeek2 = $scheduledSnsUnitsWeek2;

        return $this;
    }

    public function getScheduledSnsUnitsWeek3(): ?int
    {
        return $this->scheduledSnsUnitsWeek3;
    }

    public function setScheduledSnsUnitsWeek3(?int $scheduledSnsUnitsWeek3): static
    {
        $this->scheduledSnsUnitsWeek3 = $scheduledSnsUnitsWeek3;

        return $this;
    }

    public function getScheduledSnsUnitsWeek4(): ?int
    {
        return $this->scheduledSnsUnitsWeek4;
    }

    public function setScheduledSnsUnitsWeek4(?int $scheduledSnsUnitsWeek4): static
    {
        $this->scheduledSnsUnitsWeek4 = $scheduledSnsUnitsWeek4;

        return $this;
    }

    public function getScheduledSnsUnitsWeek5(): ?int
    {
        return $this->scheduledSnsUnitsWeek5;
    }

    public function setScheduledSnsUnitsWeek5(?int $scheduledSnsUnitsWeek5): static
    {
        $this->scheduledSnsUnitsWeek5 = $scheduledSnsUnitsWeek5;

        return $this;
    }

    public function getScheduledSnsUnitsWeek6(): ?int
    {
        return $this->scheduledSnsUnitsWeek6;
    }

    public function setScheduledSnsUnitsWeek6(?int $scheduledSnsUnitsWeek6): static
    {
        $this->scheduledSnsUnitsWeek6 = $scheduledSnsUnitsWeek6;

        return $this;
    }

    public function getScheduledSnsUnitsWeek7(): ?int
    {
        return $this->scheduledSnsUnitsWeek7;
    }

    public function setScheduledSnsUnitsWeek7(?int $scheduledSnsUnitsWeek7): static
    {
        $this->scheduledSnsUnitsWeek7 = $scheduledSnsUnitsWeek7;

        return $this;
    }

    public function getScheduledSnsUnitsWeek8(): ?int
    {
        return $this->scheduledSnsUnitsWeek8;
    }

    public function setScheduledSnsUnitsWeek8(?int $scheduledSnsUnitsWeek8): static
    {
        $this->scheduledSnsUnitsWeek8 = $scheduledSnsUnitsWeek8;

        return $this;
    }

    public function getCreatedFromReportSpa(): ?AmazonReportSpa
    {
        return $this->createdFromReportSpa;
    }

    public function setCreatedFromReportSpa(?AmazonReportSpa $createdFromReportSpa): static
    {
        $this->createdFromReportSpa = $createdFromReportSpa;

        return $this;
    }

    public function getUpdatedFromReportSpa(): ?AmazonReportSpa
    {
        return $this->updatedFromReportSpa;
    }

    public function setUpdatedFromReportSpa(?AmazonReportSpa $updatedFromReportSpa): static
    {
        $this->updatedFromReportSpa = $updatedFromReportSpa;

        return $this;
    }

    public function getAccountAmazon(): ?AccountAmazon
    {
        return $this->accountAmazon;
    }

    public function setAccountAmazon(?AccountAmazon $accountAmazon): static
    {
        $this->accountAmazon = $accountAmazon;

        return $this;
    }
}
