<?php /** @noinspection PhpUnused - mutators are called from entity manager related code */

namespace App\Entity\Spa\Inventory;

use App\Repository\Spa\Inventory\DataSpaInvListingsActiveRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'data_spa_inv_listings_active')]
#[ORM\Index(columns: ['spaProfileId', 'sku'], name: 'IDX_PROFILE_SKU')]
#[ORM\Entity(repositoryClass: DataSpaInvListingsActiveRepository::class)]
class DataSpaInvListingsActive extends DataSpaInvListingsBase
{
    /**
     * @return array<string, string>
     */
    public static function getArrayParseNotYetImplementedFields(): array
    {
        return [
            'Progressive Price Type'    => "",
            'Progressive Lower Bound 1' => "",
            'Progressive Price 1'       => "",
            'Progressive Lower Bound 2' => "",
            'Progressive Price 2'       => "",
            'Progressive Lower Bound 3' => "",
            'Progressive Price 3'       => "",
            '累積購入割引価格タイプ'       => "",
            '累積購入割引下限1'           => "",
            '累積購入割引価格1'           => "",
            '累積購入割引下限2'           => "",
            '累積購入割引価格2'           => "",
            '累積購入割引下限3'           => "",
            '累積購入割引価格3'           => "",
        ];
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $businessPrice;

    #[ORM\Column(type: Types::STRING, length: 40, nullable: true)]
    protected ?string $quantityPriceType = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $quantityLowerBound1 = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $quantityPrice1 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $quantityLowerBound2 = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $quantityPrice2 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $quantityLowerBound3 = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $quantityPrice3 = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $quantityLowerBound4 = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $quantityPrice4 = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getBusinessPrice(): ?int
    {
        return $this->businessPrice;
    }

    public function setBusinessPrice(?int $businessPrice): static
    {
        $this->businessPrice = $businessPrice;

        return $this;
    }

    public function getQuantityPriceType(): ?string
    {
        return $this->quantityPriceType;
    }

    public function setQuantityPriceType(?string $quantityPriceType): static
    {
        $this->quantityPriceType = $quantityPriceType;

        return $this;
    }

    public function getQuantityLowerBound1(): ?int
    {
        return $this->quantityLowerBound1;
    }

    public function setQuantityLowerBound1(?int $quantityLowerBound1): static
    {
        $this->quantityLowerBound1 = $quantityLowerBound1;

        return $this;
    }

    public function getQuantityPrice1(): ?int
    {
        return $this->quantityPrice1;
    }

    public function setQuantityPrice1(?int $quantityPrice1): static
    {
        $this->quantityPrice1 = $quantityPrice1;

        return $this;
    }

    public function getQuantityLowerBound2(): ?int
    {
        return $this->quantityLowerBound2;
    }

    public function setQuantityLowerBound2(?int $quantityLowerBound2): static
    {
        $this->quantityLowerBound2 = $quantityLowerBound2;

        return $this;
    }

    public function getQuantityPrice2(): ?int
    {
        return $this->quantityPrice2;
    }

    public function setQuantityPrice2(?int $quantityPrice2): static
    {
        $this->quantityPrice2 = $quantityPrice2;

        return $this;
    }

    public function getQuantityLowerBound3(): ?int
    {
        return $this->quantityLowerBound3;
    }

    public function setQuantityLowerBound3(?int $quantityLowerBound3): static
    {
        $this->quantityLowerBound3 = $quantityLowerBound3;

        return $this;
    }

    public function getQuantityPrice3(): ?int
    {
        return $this->quantityPrice3;
    }

    public function setQuantityPrice3(?int $quantityPrice3): static
    {
        $this->quantityPrice3 = $quantityPrice3;

        return $this;
    }

    public function getQuantityLowerBound4(): ?int
    {
        return $this->quantityLowerBound4;
    }

    public function setQuantityLowerBound4(?int $quantityLowerBound4): static
    {
        $this->quantityLowerBound4 = $quantityLowerBound4;

        return $this;
    }

    public function getQuantityPrice4(): ?int
    {
        return $this->quantityPrice4;
    }

    public function setQuantityPrice4(?int $quantityPrice4): static
    {
        $this->quantityPrice4 = $quantityPrice4;

        return $this;
    }
}
