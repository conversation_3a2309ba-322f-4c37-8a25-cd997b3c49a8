<?php /** @noinspection PhpUnused - mutators are called from entity manager related code */

namespace App\Entity\Spa\Inventory;

use App\Entity\MagicMutatorTrait;
use App\Entity\Spa\DataSpaBase;
use App\Repository\Spa\Inventory\DataSpaInvPlanningRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'data_spa_inv_planning')]
#[ORM\Index(columns: ['spaProfileId', 'snapshotDate'], name: 'IDX_SPRO_DATE')]
#[ORM\Index(columns: ['snapshotDate'], name: 'IDX_SDATE')]
#[ORM\Index(columns: ['sku'], name: 'idx_sku')]
#[ORM\Index(columns: ['asin'], name: 'idx_asin')]
#[ORM\Entity(repositoryClass: DataSpaInvPlanningRepository::class)]
class DataSpaInvPlanning extends DataSpaBase
{
    use MagicMutatorTrait;

    public const int SPECIAL_VALUE_INFINITE_DAYS_OF_STOCK = 9999;

    public const array REPORT_OBJECT_SETTER_MAP = [
        'snapshot-date'                                 => 'setSnapshotDate',
        'sku'                                           => 'setSku',
        'fnsku'                                         => 'setFnsku',
        'asin'                                          => 'setAsin',
        'product-name'                                  => 'setProductName',
        'condition'                                     => 'setItemCondition',
        'available'                                     => 'setAvailable',
        'pending-removal-quantity'                      => 'setPendingRemovalQuantity',
        'inv-age-0-to-90-days'                          => 'setInvAge0To90Days',
        'inv-age-91-to-180-days'                        => 'setInvAge91To180Days',
        'inv-age-181-to-270-days'                       => 'setInvAge181To270Days',
        'inv-age-271-to-365-days'                       => 'setInvAge271To365Days',
        'inv-age-365-plus-days'                         => 'setInvAge365PlusDays',
        'currency'                                      => 'setCurrency',
        'units-shipped-t7'                              => 'setUnitsShippedT7',
        'units-shipped-t30'                             => 'setUnitsShippedT30',
        'units-shipped-t60'                             => 'setUnitsShippedT60',
        'units-shipped-t90'                             => 'setUnitsShippedT90',
        'alert'                                         => 'setAlert',
        'your-price'                                    => 'setYourPriceFromDecimal',
        'sales-price'                                   => 'setSalesPriceFromDecimal',
        'lowest-price-new-plus-shipping'                => 'setLowestPriceNewPlusShippingFromDecimal',
        'lowest-price-used'                             => 'setLowestPriceUsedFromDecimal',
        'recommended-action'                            => 'setRecommendedAction',
        'healthy-inventory-level'                       => 'setHealthyInventoryLevel',
        'DEPRECATED healthy-inventory-level'            => 'setHealthyInventoryLevel',
        'recommended-sales-price'                       => 'setRecommendedSalesPriceFromDecimal',
        'recommended-sale-duration-days'                => 'setRecommendedSaleDurationDays',
        'recommended-removal-quantity'                  => 'setRecommendedRemovalQuantity',
        'estimated-cost-savings-of-recommended-actions' => 'setEstimatedCostSavingsOfRecommendedActionsFromDecimal',
        'sell-through'                                  => 'setSellThroughFromDecimal',
        'item-volume'                                   => 'setItemVolume',
        'volume-unit-measurement'                       => 'setVolumeUnitMeasurement',
        'storage-type'                                  => 'setStorageType',
        'storage-volume'                                => 'setStorageVolume',
        'marketplace'                                   => 'setMarketplace',
        'product-group'                                 => 'setProductGroup',
        'sales-rank'                                    => 'setSalesRank',
        'days-of-supply'                                => 'setDaysOfSupply',
        'estimated-excess-quantity'                     => 'setEstimatedExcessQuantity',
        'weeks-of-cover-t30'                            => 'setWeeksOfCoverT30',
        'weeks-of-cover-t90'                            => 'setWeeksOfCoverT90',
        'featuredoffer-price'                           => 'setFeaturedOfferPriceFromDecimal',
        'sales-shipped-last-7-days'                     => 'setSalesShippedLast7DaysFromDecimal',
        'sales-shipped-last-30-days'                    => 'setSalesShippedLast30DaysFromDecimal',
        'sales-shipped-last-60-days'                    => 'setSalesShippedLast60DaysFromDecimal',
        'sales-shipped-last-90-days'                    => 'setSalesShippedLast90DaysFromDecimal',
        'inv-age-0-to-30-days'                          => 'setInvAge0To30Days',
        'inv-age-31-to-60-days'                         => 'setInvAge31To60Days',
        'inv-age-61-to-90-days'                         => 'setInvAge61To90Days',
        'inv-age-181-to-330-days'                       => 'setInvAge181To330Days',
        'inv-age-331-to-365-days'                       => 'setInvAge331To365Days',
        'estimated-storage-cost-next-month'             => 'setEstimatedStorageCostNextMonthFromDecimal',
        'inbound-quantity'                              => 'setInboundQuantity',
        'inbound-working'                               => 'setInboundWorking',
        'inbound-shipped'                               => 'setInboundShipped',
        'inbound-received'                              => 'setInboundReceived',
        'no-sale-last-6-months'                         => 'setNoSaleLast6Months',
        'reserved-quantity'                             => 'setReservedQuantity',
        'unfulfillable-quantity'                        => 'setUnfulfillableQuantity',
        'qty-to-be-charged-ltsf-6-mo'                   => 'setQtyToBeChargedLtsf6Mo',
        'projected-ltsf-6-mo'                           => 'setProjectedLtsf6MoFromDecimal',
        'qty-to-be-charged-ltsf-9-mo'                   => 'setQtyToBeChargedLtsf9Mo',
        'projected-ltsf-9-mo'                           => 'setProjectedLtsf9MoFromDecimal',
        'qty-to-be-charged-ltsf-11-mo'                  => 'setQtyToBeChargedLtsf11Mo',
        'projected-ltsf-11-mo'                          => 'setProjectedLtsf11MoFromDecimal',
        'qty-to-be-charged-ltsf-12-mo'                  => 'setQtyToBeChargedLtsf12Mo',
        'estimated-ltsf-next-charge'                    => 'setEstimatedLtsfNextChargeFromDecimal',
        'weeks-of-cover'                                => 'setWeeksOfCover',
        'weeks-of-cover-estimated-excess-quantity'      => 'setWeeksOfCoverEstimatedExcessQuantity',
        'recommended-order-quantity'                    => null,
        'recommended-order-date'                        => null,
        'Recommended ship-in quantity'                  => null,
        'Recommended ship-in date'                      => null,
        'estimated-ais-181-210-days'                    => 'setAisCharge181FromDecimal',
        'estimated-ais-211-240-days'                    => 'setAisCharge211FromDecimal',
        'estimated-ais-241-270-days'                    => 'setAisCharge241FromDecimal',
        'estimated-ais-271-300-days'                    => 'setAisCharge271FromDecimal',
        'estimated-ais-301-330-days'                    => 'setAisCharge301FromDecimal',
        'estimated-ais-331-365-days'                    => 'setAisCharge331FromDecimal',
        'estimated-ais-271-365-days'                    => 'setAisCharge271To365FromDecimal',
        'estimated-ais-365-plus-days'                   => 'setAisCharge365FromDecimal',
        'quantity-to-be-charged-ais-181-210-days'       => 'setAisQty181',
        'quantity-to-be-charged-ais-211-240-days'       => 'setAisQty211',
        'quantity-to-be-charged-ais-241-270-days'       => 'setAisQty241',
        'quantity-to-be-charged-ais-271-300-days'       => 'setAisQty271',
        'quantity-to-be-charged-ais-301-330-days'       => 'setAisQty301',
        'quantity-to-be-charged-ais-331-365-days'       => 'setAisQty331',
        'quantity-to-be-charged-ais-271-365-days'       => 'setAisQty271To365',
        'quantity-to-be-charged-ais-365-plus-days'      => 'setAisQty365',
        'quantity-to-be-charged-ais-365-PLUS-days'      => 'setAisQty365',
        'Inventory Supply at FBA'                       => 'setAvailable',                  // alias of column heading
        'Total Reserved Quantity'                       => 'setReservedQuantity',           // alias of column heading
        'Reserved FC Transfer'                          => 'setReservedTransfer',
        'Reserved FC Processing'                        => 'setReservedProcessing',
        'Reserved Customer Order'                       => 'setReservedCustomerOrder',
        'Total Days of Supply (including units from open shipments)' => 'setDaysOfSupply',  // alias of column heading
        'Inventory age snapshot date'                   => 'setInventoryAgeSnapshotDate',

        'historical-days-of-supply'                     => 'setHistoricalDaysOfSupply',
        'fba-minimum-inventory-level'                   => 'setFbaMinimumInventoryLevel',
        'fba-inventory-level-health-status'             => 'setFbaInventoryLevelHealthStatus',

        'Last updated date for Historical Days of Supply'   => 'setHistoricalDosUpdateDateFromString',
        'Exempted from Low-Inventory-Level fee?'            => 'setExemptFromLowInvFeeFromBooleanText',
        'Exempted from Low-Inventory cost coverage fee?'    => 'setExemptFromLowInvFeeFromBooleanText',
        'Low-Inventory-Level fee applied in current week?'  => 'setChargedLowInvFeeThisWeekFromBooleanText',
        'Low-Inventory cost coverage fee applied in current week?'  => 'setChargedLowInvFeeThisWeekFromBooleanText',
        'Short term historical days of supply'              => 'setShortTermHistoricDos',
        'Long term historical days of supply'               => 'setLongTermHistoricDos',

        // AU versions of column headings
        'quantity-with-removals-in-progress'            => 'setPendingRemovalQuantity',
        'inventory-age-0-to-90-days'                    => 'setInvAge0To90Days',
        'inventory-age-91-to-180-days'                  => 'setInvAge91To180Days',
        'inventory-age-181-to-270-days'                 => 'setInvAge181To270Days',
        'inventory-age-271-to-365-days'                 => 'setInvAge271To365Days',
        'inventory-age-365-plus-days'                   => 'setInvAge365PlusDays',
        'quantity-to-be-charged-ltsf-6-month'           => 'setQtyToBeChargedLtsf6Mo',
        'projected-ltsf-6-month'                        => 'setProjectedLtsf6MoFromDecimal',
        'quantity-to-be-charged-ltsf-12-month'          => 'setQtyToBeChargedLtsf12Mo',
        'units-shipped-last-7-days'                     => 'setUnitsShippedT7',
        'units-shipped-last-30-days'                    => 'setUnitsShippedT30',
        'units-shipped-last-60-days'                    => 'setUnitsShippedT60',
        'units-shipped-last-90-days'                    => 'setUnitsShippedT90',
        'lowest-price-new'                              => 'setLowestPriceNewPlusShippingFromDecimal',
        'volume-units'                                  => 'setVolumeUnitMeasurement',
        'store'                                         => 'setMarketplace',
        'weeks-of-cover-last-30-days'                   => 'setWeeksOfCoverT30',
        'weeks-of-cover-last-90-days'                   => 'setWeeksOfCoverT90',
        'inventory-age-0-30-days'                       => 'setInvAge0To30Days',
        'inventory-age-31-60-days'                      => 'setInvAge31To60Days',
        'inventory-age-61-90-days'                      => 'setInvAge61To90Days',
        'inventory-age-181-to-330-days'                 => 'setInvAge181To330Days',
        'inventory-age-331-365-days'                    => 'setInvAge331To365Days',
        'estimated-storage-costs-next-30-days'          => 'setEstimatedStorageCostNextMonthFromDecimal',
    ];

    public function getDaysOfStockT7(): float|int
    {
        if (0 >= $this->getAvailable()) {
            return 0;
        }
        if (0 >= $this->getUnitsShippedT7()) {
            return self::SPECIAL_VALUE_INFINITE_DAYS_OF_STOCK;
        }
        return round($this->getAvailable() / $this->getDailyUnitsShippedT7(), 2);
    }

    public function getDailyUnitsShippedT7(): float
    {
        return $this->getUnitsShippedT7() / 7;
    }

    public function buildKey(): string
    {
        $key = $this->addProfileFnskuSkuToKey();
        if ($this->getItemCondition()) {
            $key .= $this->getItemCondition() . '-';
        } else {
            $key .= 'C?-';
        }
        if ($this->getCurrency()) {
            $key .= $this->getCurrency() . '-';
        } else {
            $key .= 'K?-';
        }
        if ($this->getSnapshotDate()) {
            $key .= $this->getSnapshotDate()->format('c') . '-';
        } else {
            $key .= 'D?-';
        }

        return $key;
    }

    public function setProjectedLtsf9MoFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setEstimatedLtsfNextChargeFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setYourPriceFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSalesPriceFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setLowestPriceNewPlusShippingFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setLowestPriceUsedFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setRecommendedSalesPriceFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setEstimatedCostSavingsOfRecommendedActionsFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSellThroughFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setFeaturedOfferPriceFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSalesShippedLast7DaysFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSalesShippedLast30DaysFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSalesShippedLast60DaysFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setSalesShippedLast90DaysFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setEstimatedStorageCostNextMonthFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setProjectedLtsf11MoFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    public function setProjectedLtsf6MoFromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    /**
     * @throws \Exception
     */
    public function setSnapshotDate(\DateTime|string $date): self
    {
        $this->snapshotDate = GravitiqTools::castToDateTime($date);

        return $this;
    }

    /**
     * @throws \Exception
     */
    public function setInventoryAgeSnapshotDate(\DateTimeInterface|string|null $date): self
    {
        $this->inventoryAgeSnapshotDate = GravitiqTools::castToDateTimeImmutable($date);

        return $this;
    }

    public function setAisCharge181FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge211FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge241FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge271FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge301FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge331FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge271To365FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }
    public function setAisCharge365FromDecimal(string|float|int $val): static
    {
        return $this->setIntegerFromDecimal(__METHOD__, $val);
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\Column(type: Types::DATETIME_MUTABLE, length: 10)]
    protected \DateTime $snapshotDate;

    #[ORM\Column(type: Types::DATE_IMMUTABLE, nullable: true)]
    protected ?\DateTimeImmutable $inventoryAgeSnapshotDate = null;

    #[ORM\Column(type: Types::STRING, length: 40)]
    protected string $sku;

    #[ORM\Column(type: Types::STRING, length: 10)]
    protected string $fnsku;

    #[ORM\Column(type: Types::STRING, length: 10)]
    protected string $asin;

    #[ORM\Column(type: Types::STRING, length: 251, nullable: true)]
    protected ?string $productName = null;

    #[ORM\Column(type: Types::STRING, length: 4, nullable: true)]
    protected ?string $itemCondition = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $available = null;

    #[ORM\Column(type: Types::INTEGER, length: 1, nullable: true)]
    protected ?int $pendingRemovalQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $invAge0To30Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $invAge31To60Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $invAge61To90Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $invAge181To330Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $invAge331To365Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $invAge0To90Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $invAge91To180Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $invAge181To270Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $invAge271To365Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $invAge365PlusDays = null;

    #[ORM\Column(type: Types::STRING, length: 3, nullable: true)]
    protected ?string $currency = null;

    #[ORM\Column(type: Types::INTEGER, length: 1, nullable: true)]
    protected ?int $qtyToBeChargedLtsf6Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $projectedLtsf6Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $qtyToBeChargedLtsf9Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $projectedLtsf9Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $qtyToBeChargedLtsf11Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $projectedLtsf11Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $qtyToBeChargedLtsf12Mo = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $estimatedLtsfNextCharge = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $unitsShippedT7 = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $unitsShippedT30 = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $unitsShippedT60 = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $unitsShippedT90 = null;

    #[ORM\Column(type: Types::STRING, length: 14, nullable: true)]
    protected ?string $alert = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $yourPrice = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $salesPrice = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $lowestPriceNewPlusShipping = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $lowestPriceUsed = null;

    #[ORM\Column(type: Types::STRING, length: 29, nullable: true)]
    protected ?string $recommendedAction = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $healthyInventoryLevel = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $recommendedSalesPrice = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $recommendedSaleDurationDays = null;

    #[ORM\Column(type: Types::INTEGER, length: 1, nullable: true)]
    protected ?int $recommendedRemovalQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $estimatedCostSavingsOfRecommendedActions = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $sellThrough = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $itemVolume = null;

    #[ORM\Column(type: Types::STRING, length: 11, nullable: true)]
    protected ?string $volumeUnitMeasurement = null;

    #[ORM\Column(type: Types::STRING, length: 16, nullable: true)]
    protected ?string $storageType = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $storageVolume = null;

    #[ORM\Column(type: Types::STRING, length: 2)]
    protected string $marketplace;

    #[ORM\Column(type: Types::STRING, length: 27, nullable: true)]
    protected ?string $productGroup = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $salesRank = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $daysOfSupply = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $estimatedExcessQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $weeksOfCoverT30 = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $weeksOfCoverT90 = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $featuredOfferPrice = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $salesShippedLast7Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 9, nullable: true)]
    protected ?int $salesShippedLast30Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 9, nullable: true)]
    protected ?int $salesShippedLast60Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 9, nullable: true)]
    protected ?int $salesShippedLast90Days = null;

    #[ORM\Column(type: Types::INTEGER, length: 7, nullable: true)]
    protected ?int $estimatedStorageCostNextMonth = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $inboundQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $inboundWorking = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $inboundShipped = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $inboundReceived = null;

    #[ORM\Column(type: Types::INTEGER, length: 2, nullable: true)]
    protected ?int $noSaleLast6Months = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $reservedQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $reservedTransfer = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $reservedProcessing = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $reservedCustomerOrder = null;

    #[ORM\Column(type: Types::INTEGER, length: 3, nullable: true)]
    protected ?int $unfulfillableQuantity = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $weeksOfCover = null;

    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: true)]
    protected ?int $weeksOfCoverEstimatedExcessQuantity = null;

    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge181 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge211 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge241 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge271 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge301 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge331 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge271To365 = null;
    #[ORM\Column(type: Types::INTEGER, length: 6, nullable: true)]
    protected ?int $aisCharge365 = null;

    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty181 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty211 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty241 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty271 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty301 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty331 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty271To365 = null;
    #[ORM\Column(type: Types::INTEGER, length: 5, nullable: true)]
    protected ?int $aisQty365 = null;
    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $historicalDaysOfSupply = null;
    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $fbaMinimumInventoryLevel = null;
    #[ORM\Column(type: Types::STRING, nullable: true)]
    protected ?string $fbaInventoryLevelHealthStatus = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $historicalDosUpdateDate = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $exemptFromLowInvFee = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $chargedLowInvFeeThisWeek = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $shortTermHistoricDos = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $longTermHistoricDos = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getSnapshotDate(): ?\DateTimeInterface
    {
        return $this->snapshotDate;
    }

    public function getInventoryAgeSnapshotDate(): ?\DateTimeImmutable
    {
        return $this->inventoryAgeSnapshotDate;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    public function getFnsku(): ?string
    {
        return $this->fnsku;
    }

    public function setFnsku(string $fnsku): static
    {
        $this->fnsku = $fnsku;

        return $this;
    }

    public function getAsin(): ?string
    {
        return $this->asin;
    }

    public function setAsin(string $asin): static
    {
        $this->asin = $asin;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(?string $productName): static
    {
        $this->productName = $productName;

        return $this;
    }

    public function getItemCondition(): ?string
    {
        return $this->itemCondition;
    }

    public function setItemCondition(?string $itemCondition): static
    {
        $this->itemCondition = $itemCondition;

        return $this;
    }

    public function getAvailable(): ?int
    {
        return $this->available;
    }

    public function setAvailable(?int $available): static
    {
        $this->available = $available;

        return $this;
    }

    public function getPendingRemovalQuantity(): ?int
    {
        return $this->pendingRemovalQuantity;
    }

    public function setPendingRemovalQuantity(?int $pendingRemovalQuantity): static
    {
        $this->pendingRemovalQuantity = $pendingRemovalQuantity;

        return $this;
    }

    public function getInvAge0To30Days(): ?int
    {
        return $this->invAge0To30Days;
    }

    public function setInvAge0To30Days(?int $invAge0To30Days): static
    {
        $this->invAge0To30Days = $invAge0To30Days;

        return $this;
    }

    public function getInvAge31To60Days(): ?int
    {
        return $this->invAge31To60Days;
    }

    public function setInvAge31To60Days(?int $invAge31To60Days): static
    {
        $this->invAge31To60Days = $invAge31To60Days;

        return $this;
    }

    public function getInvAge61To90Days(): ?int
    {
        return $this->invAge61To90Days;
    }

    public function setInvAge61To90Days(?int $invAge61To90Days): static
    {
        $this->invAge61To90Days = $invAge61To90Days;

        return $this;
    }

    public function getInvAge181To330Days(): ?int
    {
        return $this->invAge181To330Days;
    }

    public function setInvAge181To330Days(?int $invAge181To330Days): static
    {
        $this->invAge181To330Days = $invAge181To330Days;

        return $this;
    }

    public function getInvAge331To365Days(): ?int
    {
        return $this->invAge331To365Days;
    }

    public function setInvAge331To365Days(?int $invAge331To365Days): static
    {
        $this->invAge331To365Days = $invAge331To365Days;

        return $this;
    }

    public function getInvAge0To90Days(): ?int
    {
        return $this->invAge0To90Days;
    }

    public function setInvAge0To90Days(?int $invAge0To90Days): static
    {
        $this->invAge0To90Days = $invAge0To90Days;

        return $this;
    }

    public function getInvAge91To180Days(): ?int
    {
        return $this->invAge91To180Days;
    }

    public function setInvAge91To180Days(?int $invAge91To180Days): static
    {
        $this->invAge91To180Days = $invAge91To180Days;

        return $this;
    }

    public function getInvAge181To270Days(): ?int
    {
        return $this->invAge181To270Days;
    }

    public function setInvAge181To270Days(?int $invAge181To270Days): static
    {
        $this->invAge181To270Days = $invAge181To270Days;

        return $this;
    }

    public function getInvAge271To365Days(): ?int
    {
        return $this->invAge271To365Days;
    }

    public function setInvAge271To365Days(?int $invAge271To365Days): static
    {
        $this->invAge271To365Days = $invAge271To365Days;

        return $this;
    }

    public function getInvAge365PlusDays(): ?int
    {
        return $this->invAge365PlusDays;
    }

    public function setInvAge365PlusDays(?int $invAge365PlusDays): static
    {
        $this->invAge365PlusDays = $invAge365PlusDays;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getQtyToBeChargedLtsf6Mo(): ?int
    {
        return $this->qtyToBeChargedLtsf6Mo;
    }

    public function setQtyToBeChargedLtsf6Mo(?int $qtyToBeChargedLtsf6Mo): static
    {
        $this->qtyToBeChargedLtsf6Mo = $qtyToBeChargedLtsf6Mo;

        return $this;
    }

    public function getProjectedLtsf6Mo(): ?int
    {
        return $this->projectedLtsf6Mo;
    }

    public function setProjectedLtsf6Mo(?int $projectedLtsf6Mo): static
    {
        $this->projectedLtsf6Mo = $projectedLtsf6Mo;

        return $this;
    }

    public function getQtyToBeChargedLtsf9Mo(): ?int
    {
        return $this->qtyToBeChargedLtsf9Mo;
    }

    public function setQtyToBeChargedLtsf9Mo(?int $qtyToBeChargedLtsf9Mo): static
    {
        $this->qtyToBeChargedLtsf9Mo = $qtyToBeChargedLtsf9Mo;

        return $this;
    }

    public function getProjectedLtsf9Mo(): ?int
    {
        return $this->projectedLtsf9Mo;
    }

    public function setProjectedLtsf9Mo(?int $projectedLtsf9Mo): static
    {
        $this->projectedLtsf9Mo = $projectedLtsf9Mo;

        return $this;
    }

    public function getQtyToBeChargedLtsf11Mo(): ?int
    {
        return $this->qtyToBeChargedLtsf11Mo;
    }

    public function setQtyToBeChargedLtsf11Mo(?int $qtyToBeChargedLtsf11Mo): static
    {
        $this->qtyToBeChargedLtsf11Mo = $qtyToBeChargedLtsf11Mo;

        return $this;
    }

    public function getProjectedLtsf11Mo(): ?int
    {
        return $this->projectedLtsf11Mo;
    }

    public function setProjectedLtsf11Mo(?int $projectedLtsf11Mo): static
    {
        $this->projectedLtsf11Mo = $projectedLtsf11Mo;

        return $this;
    }

    public function getQtyToBeChargedLtsf12Mo(): ?int
    {
        return $this->qtyToBeChargedLtsf12Mo;
    }

    public function setQtyToBeChargedLtsf12Mo(?int $qtyToBeChargedLtsf12Mo): static
    {
        $this->qtyToBeChargedLtsf12Mo = $qtyToBeChargedLtsf12Mo;

        return $this;
    }

    public function getEstimatedLtsfNextCharge(): ?int
    {
        return $this->estimatedLtsfNextCharge;
    }

    public function setEstimatedLtsfNextCharge(?int $estimatedLtsfNextCharge): static
    {
        $this->estimatedLtsfNextCharge = $estimatedLtsfNextCharge;

        return $this;
    }

    public function getUnitsShippedT7(): ?int
    {
        return $this->unitsShippedT7;
    }

    public function setUnitsShippedT7(?int $unitsShippedT7): static
    {
        $this->unitsShippedT7 = $unitsShippedT7;

        return $this;
    }

    public function getUnitsShippedT30(): ?int
    {
        return $this->unitsShippedT30;
    }

    public function setUnitsShippedT30(?int $unitsShippedT30): static
    {
        $this->unitsShippedT30 = $unitsShippedT30;

        return $this;
    }

    public function getUnitsShippedT60(): ?int
    {
        return $this->unitsShippedT60;
    }

    public function setUnitsShippedT60(?int $unitsShippedT60): static
    {
        $this->unitsShippedT60 = $unitsShippedT60;

        return $this;
    }

    public function getUnitsShippedT90(): ?int
    {
        return $this->unitsShippedT90;
    }

    public function setUnitsShippedT90(?int $unitsShippedT90): static
    {
        $this->unitsShippedT90 = $unitsShippedT90;

        return $this;
    }

    public function getAlert(): ?string
    {
        return $this->alert;
    }

    public function setAlert(?string $alert): static
    {
        $this->alert = $alert;

        return $this;
    }

    public function getYourPrice(): ?int
    {
        return $this->yourPrice;
    }

    public function setYourPrice(?int $yourPrice): static
    {
        $this->yourPrice = $yourPrice;

        return $this;
    }

    public function getSalesPrice(): ?int
    {
        return $this->salesPrice;
    }

    public function setSalesPrice(?int $salesPrice): static
    {
        $this->salesPrice = $salesPrice;

        return $this;
    }

    public function getLowestPriceNewPlusShipping(): ?int
    {
        return $this->lowestPriceNewPlusShipping;
    }

    public function setLowestPriceNewPlusShipping(?int $lowestPriceNewPlusShipping): static
    {
        $this->lowestPriceNewPlusShipping = $lowestPriceNewPlusShipping;

        return $this;
    }

    public function getLowestPriceUsed(): ?int
    {
        return $this->lowestPriceUsed;
    }

    public function setLowestPriceUsed(?int $lowestPriceUsed): static
    {
        $this->lowestPriceUsed = $lowestPriceUsed;

        return $this;
    }

    public function getRecommendedAction(): ?string
    {
        return $this->recommendedAction;
    }

    public function setRecommendedAction(?string $recommendedAction): static
    {
        $this->recommendedAction = $recommendedAction;

        return $this;
    }

    public function getHealthyInventoryLevel(): ?int
    {
        return $this->healthyInventoryLevel;
    }

    public function setHealthyInventoryLevel(?int $healthyInventoryLevel): static
    {
        $this->healthyInventoryLevel = $healthyInventoryLevel;

        return $this;
    }

    public function getRecommendedSalesPrice(): ?int
    {
        return $this->recommendedSalesPrice;
    }

    public function setRecommendedSalesPrice(?int $recommendedSalesPrice): static
    {
        $this->recommendedSalesPrice = $recommendedSalesPrice;

        return $this;
    }

    public function getRecommendedSaleDurationDays(): ?int
    {
        return $this->recommendedSaleDurationDays;
    }

    public function setRecommendedSaleDurationDays(?int $recommendedSaleDurationDays): static
    {
        $this->recommendedSaleDurationDays = $recommendedSaleDurationDays;

        return $this;
    }

    public function getRecommendedRemovalQuantity(): ?int
    {
        return $this->recommendedRemovalQuantity;
    }

    public function setRecommendedRemovalQuantity(?int $recommendedRemovalQuantity): static
    {
        $this->recommendedRemovalQuantity = $recommendedRemovalQuantity;

        return $this;
    }

    public function getEstimatedCostSavingsOfRecommendedActions(): ?int
    {
        return $this->estimatedCostSavingsOfRecommendedActions;
    }

    public function setEstimatedCostSavingsOfRecommendedActions(?int $estimatedCostSavingsOfRecommendedActions): static
    {
        $this->estimatedCostSavingsOfRecommendedActions = $estimatedCostSavingsOfRecommendedActions;

        return $this;
    }

    public function getSellThrough(): ?float
    {
        return $this->sellThrough;
    }

    public function setSellThrough(?float $sellThrough): static
    {
        $this->sellThrough = $sellThrough;

        return $this;
    }

    public function getItemVolume(): ?float
    {
        return $this->itemVolume;
    }

    public function setItemVolume(?float $itemVolume): static
    {
        $this->itemVolume = $itemVolume;

        return $this;
    }

    public function getVolumeUnitMeasurement(): ?string
    {
        return $this->volumeUnitMeasurement;
    }

    public function setVolumeUnitMeasurement(?string $volumeUnitMeasurement): static
    {
        $this->volumeUnitMeasurement = $volumeUnitMeasurement;

        return $this;
    }

    public function getStorageType(): ?string
    {
        return $this->storageType;
    }

    public function setStorageType(?string $storageType): static
    {
        $this->storageType = $storageType;

        return $this;
    }

    public function getStorageVolume(): ?float
    {
        return $this->storageVolume;
    }

    public function setStorageVolume(?float $storageVolume): static
    {
        $this->storageVolume = $storageVolume;

        return $this;
    }

    public function getMarketplace(): ?string
    {
        return $this->marketplace;
    }

    public function setMarketplace(string $marketplace): static
    {
        $this->marketplace = $marketplace;

        return $this;
    }

    public function getProductGroup(): ?string
    {
        return $this->productGroup;
    }

    public function setProductGroup(?string $productGroup): static
    {
        $this->productGroup = $productGroup;

        return $this;
    }

    public function getSalesRank(): ?int
    {
        return $this->salesRank;
    }

    public function setSalesRank(?int $salesRank): static
    {
        $this->salesRank = $salesRank;

        return $this;
    }

    public function getDaysOfSupply(): ?int
    {
        return $this->daysOfSupply;
    }

    public function setDaysOfSupply(?int $daysOfSupply): static
    {
        $this->daysOfSupply = $daysOfSupply;

        return $this;
    }

    public function getEstimatedExcessQuantity(): ?int
    {
        return $this->estimatedExcessQuantity;
    }

    public function setEstimatedExcessQuantity(?int $estimatedExcessQuantity): static
    {
        $this->estimatedExcessQuantity = $estimatedExcessQuantity;

        return $this;
    }

    public function getWeeksOfCoverT30(): ?int
    {
        return $this->weeksOfCoverT30;
    }

    public function setWeeksOfCoverT30(?int $weeksOfCoverT30): static
    {
        $this->weeksOfCoverT30 = $weeksOfCoverT30;

        return $this;
    }

    public function getWeeksOfCoverT90(): ?int
    {
        return $this->weeksOfCoverT90;
    }

    public function setWeeksOfCoverT90(?int $weeksOfCoverT90): static
    {
        $this->weeksOfCoverT90 = $weeksOfCoverT90;

        return $this;
    }

    public function getFeaturedOfferPrice(): ?int
    {
        return $this->featuredOfferPrice;
    }

    public function setFeaturedOfferPrice(?int $featuredOfferPrice): static
    {
        $this->featuredOfferPrice = $featuredOfferPrice;

        return $this;
    }

    public function getSalesShippedLast7Days(): ?int
    {
        return $this->salesShippedLast7Days;
    }

    public function setSalesShippedLast7Days(?int $salesShippedLast7Days): static
    {
        $this->salesShippedLast7Days = $salesShippedLast7Days;

        return $this;
    }

    public function getSalesShippedLast30Days(): ?int
    {
        return $this->salesShippedLast30Days;
    }

    public function setSalesShippedLast30Days(?int $salesShippedLast30Days): static
    {
        $this->salesShippedLast30Days = $salesShippedLast30Days;

        return $this;
    }

    public function getSalesShippedLast60Days(): ?int
    {
        return $this->salesShippedLast60Days;
    }

    public function setSalesShippedLast60Days(?int $salesShippedLast60Days): static
    {
        $this->salesShippedLast60Days = $salesShippedLast60Days;

        return $this;
    }

    public function getSalesShippedLast90Days(): ?int
    {
        return $this->salesShippedLast90Days;
    }

    public function setSalesShippedLast90Days(?int $salesShippedLast90Days): static
    {
        $this->salesShippedLast90Days = $salesShippedLast90Days;

        return $this;
    }

    public function getEstimatedStorageCostNextMonth(): ?int
    {
        return $this->estimatedStorageCostNextMonth;
    }

    public function setEstimatedStorageCostNextMonth(?int $estimatedStorageCostNextMonth): static
    {
        $this->estimatedStorageCostNextMonth = $estimatedStorageCostNextMonth;

        return $this;
    }

    public function getInboundQuantity(): ?int
    {
        return $this->inboundQuantity;
    }

    public function setInboundQuantity(?int $inboundQuantity): static
    {
        $this->inboundQuantity = $inboundQuantity;

        return $this;
    }

    public function getInboundWorking(): ?int
    {
        return $this->inboundWorking;
    }

    public function setInboundWorking(?int $inboundWorking): static
    {
        $this->inboundWorking = $inboundWorking;

        return $this;
    }

    public function getInboundShipped(): ?int
    {
        return $this->inboundShipped;
    }

    public function setInboundShipped(?int $inboundShipped): static
    {
        $this->inboundShipped = $inboundShipped;

        return $this;
    }

    public function getInboundReceived(): ?int
    {
        return $this->inboundReceived;
    }

    public function setInboundReceived(?int $inboundReceived): static
    {
        $this->inboundReceived = $inboundReceived;

        return $this;
    }

    public function getNoSaleLast6Months(): ?int
    {
        return $this->noSaleLast6Months;
    }

    public function setNoSaleLast6Months(?int $noSaleLast6Months): static
    {
        $this->noSaleLast6Months = $noSaleLast6Months;

        return $this;
    }

    public function getReservedQuantity(): ?int
    {
        return $this->reservedQuantity;
    }

    public function setReservedQuantity(?int $reservedQuantity): static
    {
        $this->reservedQuantity = $reservedQuantity;

        return $this;
    }

    public function getReservedTransfer(): ?int
    {
        return $this->reservedTransfer;
    }

    public function setReservedTransfer(?int $reservedTransfer): static
    {
        $this->reservedTransfer = $reservedTransfer;

        return $this;
    }

    public function getReservedProcessing(): ?int
    {
        return $this->reservedProcessing;
    }

    public function setReservedProcessing(?int $reservedProcessing): static
    {
        $this->reservedProcessing = $reservedProcessing;

        return $this;
    }

    public function getReservedCustomerOrder(): ?int
    {
        return $this->reservedCustomerOrder;
    }

    public function setReservedCustomerOrder(?int $reservedCustomerOrder): static
    {
        $this->reservedCustomerOrder = $reservedCustomerOrder;

        return $this;
    }

    public function getUnfulfillableQuantity(): ?int
    {
        return $this->unfulfillableQuantity;
    }

    public function setUnfulfillableQuantity(?int $unfulfillableQuantity): static
    {
        $this->unfulfillableQuantity = $unfulfillableQuantity;

        return $this;
    }

    public function getWeeksOfCover(): ?float
    {
        return $this->weeksOfCover;
    }

    public function setWeeksOfCover(?float $weeksOfCover): static
    {
        $this->weeksOfCover = $weeksOfCover;

        return $this;
    }

    public function getWeeksOfCoverEstimatedExcessQuantity(): ?int
    {
        return $this->weeksOfCoverEstimatedExcessQuantity;
    }

    public function setWeeksOfCoverEstimatedExcessQuantity(?int $weeksOfCoverEstimatedExcessQuantity): static
    {
        $this->weeksOfCoverEstimatedExcessQuantity = $weeksOfCoverEstimatedExcessQuantity;

        return $this;
    }

    public function getAisCharge181(): ?int
    {
        return $this->aisCharge181;
    }

    public function setAisCharge181(?int $aisCharge181): static
    {
        $this->aisCharge181 = $aisCharge181;

        return $this;
    }

    public function getAisCharge211(): ?int
    {
        return $this->aisCharge211;
    }

    public function setAisCharge211(?int $aisCharge211): static
    {
        $this->aisCharge211 = $aisCharge211;

        return $this;
    }

    public function getAisCharge241(): ?int
    {
        return $this->aisCharge241;
    }

    public function setAisCharge241(?int $aisCharge241): static
    {
        $this->aisCharge241 = $aisCharge241;

        return $this;
    }

    public function getAisCharge271(): ?int
    {
        return $this->aisCharge271;
    }

    public function setAisCharge271(?int $aisCharge271): static
    {
        $this->aisCharge271 = $aisCharge271;

        return $this;
    }

    public function getAisCharge301(): ?int
    {
        return $this->aisCharge301;
    }

    public function setAisCharge301(?int $aisCharge301): static
    {
        $this->aisCharge301 = $aisCharge301;

        return $this;
    }

    public function getAisCharge331(): ?int
    {
        return $this->aisCharge331;
    }

    public function setAisCharge331(?int $aisCharge331): static
    {
        $this->aisCharge331 = $aisCharge331;

        return $this;
    }

    public function getAisCharge271To365(): ?int
    {
        return $this->aisCharge271To365;
    }

    public function setAisCharge271To365(?int $aisCharge271To365): static
    {
        $this->aisCharge271To365 = $aisCharge271To365;

        return $this;
    }

    public function getAisCharge365(): ?int
    {
        return $this->aisCharge365;
    }

    public function setAisCharge365(?int $aisCharge365): static
    {
        $this->aisCharge365 = $aisCharge365;

        return $this;
    }

    public function getAisQty181(): ?int
    {
        return $this->aisQty181;
    }

    public function setAisQty181(?int $aisQty181): static
    {
        $this->aisQty181 = $aisQty181;

        return $this;
    }

    public function getAisQty211(): ?int
    {
        return $this->aisQty211;
    }

    public function setAisQty211(?int $aisQty211): static
    {
        $this->aisQty211 = $aisQty211;

        return $this;
    }

    public function getAisQty241(): ?int
    {
        return $this->aisQty241;
    }

    public function setAisQty241(?int $aisQty241): static
    {
        $this->aisQty241 = $aisQty241;

        return $this;
    }

    public function getAisQty271(): ?int
    {
        return $this->aisQty271;
    }

    public function setAisQty271(?int $aisQty271): static
    {
        $this->aisQty271 = $aisQty271;

        return $this;
    }

    public function getAisQty301(): ?int
    {
        return $this->aisQty301;
    }

    public function setAisQty301(?int $aisQty301): static
    {
        $this->aisQty301 = $aisQty301;

        return $this;
    }

    public function getAisQty331(): ?int
    {
        return $this->aisQty331;
    }

    public function setAisQty331(?int $aisQty331): static
    {
        $this->aisQty331 = $aisQty331;

        return $this;
    }

    public function getAisQty271To365(): ?int
    {
        return $this->aisQty271To365;
    }

    public function setAisQty271To365(?int $aisQty271To365): static
    {
        $this->aisQty271To365 = $aisQty271To365;

        return $this;
    }

    public function getAisQty365(): ?int
    {
        return $this->aisQty365;
    }

    public function setAisQty365(?int $aisQty365): static
    {
        $this->aisQty365 = $aisQty365;

        return $this;
    }

    public function getHistoricalDaysOfSupply(): ?float
    {
        return $this->historicalDaysOfSupply;
    }

    public function setHistoricalDaysOfSupply(?float $historicalDaysOfSupply): static
    {
        $this->historicalDaysOfSupply = $historicalDaysOfSupply;

        return $this;
    }

    public function getFbaMinimumInventoryLevel(): ?int
    {
        return $this->fbaMinimumInventoryLevel;
    }

    public function setFbaMinimumInventoryLevel(?int $fbaMinimumInventoryLevel): static
    {
        $this->fbaMinimumInventoryLevel = $fbaMinimumInventoryLevel;

        return $this;
    }

    public function getFbaInventoryLevelHealthStatus(): ?string
    {
        return $this->fbaInventoryLevelHealthStatus;
    }

    public function setFbaInventoryLevelHealthStatus(?string $fbaInventoryLevelHealthStatus): static
    {
        $this->fbaInventoryLevelHealthStatus = $fbaInventoryLevelHealthStatus;

        return $this;
    }

    public function getHistoricalDosUpdateDate(): ?\DateTimeInterface
    {
        return $this->historicalDosUpdateDate;
    }

    public function setHistoricalDosUpdateDate(?\DateTimeInterface $historicalDosUpdateDate): static
    {
        $this->historicalDosUpdateDate = $historicalDosUpdateDate;

        return $this;
    }

    public function isExemptFromLowInvFee(): ?bool
    {
        return $this->exemptFromLowInvFee;
    }

    public function setExemptFromLowInvFee(?bool $exemptFromLowInvFee): static
    {
        $this->exemptFromLowInvFee = $exemptFromLowInvFee;

        return $this;
    }

    public function isChargedLowInvFeeThisWeek(): ?bool
    {
        return $this->chargedLowInvFeeThisWeek;
    }

    public function setChargedLowInvFeeThisWeek(?bool $chargedLowInvFeeThisWeek): static
    {
        $this->chargedLowInvFeeThisWeek = $chargedLowInvFeeThisWeek;

        return $this;
    }

    public function getShortTermHistoricDos(): ?float
    {
        return $this->shortTermHistoricDos;
    }

    public function setShortTermHistoricDos(?float $shortTermHistoricDos): static
    {
        $this->shortTermHistoricDos = $shortTermHistoricDos;

        return $this;
    }

    public function getLongTermHistoricDos(): ?float
    {
        return $this->longTermHistoricDos;
    }

    public function setLongTermHistoricDos(?float $longTermHistoricDos): static
    {
        $this->longTermHistoricDos = $longTermHistoricDos;

        return $this;
    }
}
