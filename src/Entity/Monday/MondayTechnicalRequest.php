<?php

namespace App\Entity\Monday;

use App\Entity\GravitiqBaseEntity;
use App\Entity\Sonata\User;
use App\Repository\Monday\MondayTechnicalRequestRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'mon_technical_request')]
#[ORM\Entity(repositoryClass: MondayTechnicalRequestRepository::class)]
#[ORM\HasLifecycleCallbacks]
class MondayTechnicalRequest extends GravitiqBaseEntity
{
    public const array DEPARTMENTS = [
        'AAD'     => 'Amazon Ads',
        'BOP'     => 'BrandOps',
        'CAT'     => 'Catalog',
        'COP'     => 'Compliance',
        'CBM'     => 'Creative',
        'CC'      => 'Creator Connections',
        'FIN'     => 'Finance',
        'NAD'     => 'Non-Amazon Ads',
        'RET'     => 'Retail',
        'SPF'     => 'Shopify',
        'SCH'     => 'SupplyChain',
        'TEC'     => 'Technical',
        'OTH'     => 'Other',
        'CWR'     => 'CWR',
        'PML'     => 'PML',
        'PRISM'   => 'PRISM Product Database',
        'TaskMan' => 'TaskManager',
        'VECTOR'  => 'VECTOR Supply Chain System',
        'WISH'    => 'WISH',
    ];

    public const array IMPACT = [
        'Tiny'   => 'Tiny (One-off benefit or Less than $100/Month)',
        'Low'    => 'Low (More than $100/Month)',
        'Medium' => 'Medium (More than $1,000/Month)',
        'High'   => 'High (More than $10,000/Month)',
    ];

    public function getTechnicalRequestTitle(): string
    {
        $department = implode(' / ', $this->getDepartment());
        return $department . ': ' . $this->getTitle();
    }

//    #[Assert\IsTrue(message: 'Only users with a linked MondayUserId can create requests. Please contact the technical team to get your Monday user id linked to your Newton user.')]
//    public function hasUserWithMondayId(): bool
//    {
//        $author = $this->getAuthor();
//        if (empty($author) || empty($author->getMondayUserId())) {
//            return false;
//        }
//        return true;
//    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected ?User $author = null;

    #[ORM\Column(name: 'mondayId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $mondayId = null;

    #[ORM\Column(name: 'mondayUrl', type: Types::STRING, length: 200, nullable: true)]
    protected ?string $mondayUrl = null;

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(name: 'dateCreated',type: Types::DATETIME_IMMUTABLE, nullable: false)]
    protected \DateTimeImmutable $dateCreated;

    /** The date that we last pulled the data related to this Task from Monday */
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $lastUpdatedFromMondayAt = null;

    /**
     * @var string[] $department
     */
    #[ORM\Column(type: Types::SIMPLE_ARRAY, nullable: false)]
    protected array $department = [];

    #[ORM\Column(type: Types::STRING, length: 255, nullable: false)]
    protected string $title;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    protected string $description;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: false)]
    protected string $impact;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: false)]
    protected string $status;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    protected bool $deletedOnMonday = false;

    #[ORM\Column(type: Types::STRING, length:8, nullable: true)]
    protected ?string $mondayState = null;

    /*******************************************
     *** Modified generated code here        ***
     *******************************************/

    /**
     * @return string[]
     */
    public function getDepartment(): array
    {
        return $this->department;
    }

    /**
     * @param string[] $department
     * @return $this
     */
    public function setDepartment(array $department): static
    {
        sort($department);
        $this->department = $department;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getMondayId(): ?string
    {
        return $this->mondayId;
    }

    public function setMondayId(?string $mondayId): static
    {
        $this->mondayId = $mondayId;

        return $this;
    }

    public function getMondayUrl(): ?string
    {
        return $this->mondayUrl;
    }

    public function setMondayUrl(?string $mondayUrl): static
    {
        $this->mondayUrl = $mondayUrl;

        return $this;
    }

    public function getDateCreated(): ?\DateTimeImmutable
    {
        return $this->dateCreated;
    }

    public function setDateCreated(\DateTimeImmutable $dateCreated): static
    {
        $this->dateCreated = $dateCreated;

        return $this;
    }

    public function getLastUpdatedFromMondayAt(): ?\DateTimeInterface
    {
        return $this->lastUpdatedFromMondayAt;
    }

    public function setLastUpdatedFromMondayAt(?\DateTimeInterface $lastUpdatedFromMondayAt): static
    {
        $this->lastUpdatedFromMondayAt = $lastUpdatedFromMondayAt;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getImpact(): ?string
    {
        return $this->impact;
    }

    public function setImpact(string $impact): static
    {
        $this->impact = $impact;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function isDeletedOnMonday(): ?bool
    {
        return $this->deletedOnMonday;
    }

    public function setDeletedOnMonday(bool $deletedOnMonday): static
    {
        $this->deletedOnMonday = $deletedOnMonday;

        return $this;
    }

    public function getMondayState(): ?string
    {
        return $this->mondayState;
    }

    public function setMondayState(?string $mondayState): static
    {
        $this->mondayState = $mondayState;

        return $this;
    }

    public function getAuthor(): ?User
    {
        return $this->author;
    }

    public function setAuthor(?User $author): static
    {
        $this->author = $author;

        return $this;
    }
}