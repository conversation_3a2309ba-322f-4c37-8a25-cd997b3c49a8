<?php

namespace App\Entity\SpaApiRequest;

use App\Entity\Spa\Fba\SpaFbaInboundPlan;
use App\Repository\Spa\Fba\ApiRequestSpaFbaInboundRepository;
use App\Service\BaseApiBridge;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'api_request_spa_fba_inbound')]
#[ORM\Index(columns: ['genHasParseDate', 'status'], name: 'gen_pdate_status')]
#[ORM\Entity(repositoryClass: ApiRequestSpaFbaInboundRepository::class)]
class ApiRequestSpaFbaInbound extends ApiRequestSpaBase
{
    public const string SHIPMENT_STATUS_WORKING = 'WORKING';
    public const string SHIPMENT_STATUS_READY_TO_SHIP = 'READY_TO_SHIP';
    public const string SHIPMENT_STATUS_SHIPPED = 'SHIPPED';
    public const string SHIPMENT_STATUS_RECEIVING = 'RECEIVING';
    public const string SHIPMENT_STATUS_CANCELLED = 'CANCELLED';
    public const string SHIPMENT_STATUS_DELETED = 'DELETED';
    public const string SHIPMENT_STATUS_CLOSED = 'CLOSED';
    public const string SHIPMENT_STATUS_ERROR = 'ERROR';
    public const string SHIPMENT_STATUS_IN_TRANSIT = 'IN_TRANSIT';
    public const string SHIPMENT_STATUS_DELIVERED = 'DELIVERED';
    public const string SHIPMENT_STATUS_CHECKED_IN = 'CHECKED_IN';
    public const array SHIPMENT_STATUS_OPTIONS = [
        self::SHIPMENT_STATUS_WORKING,
        self::SHIPMENT_STATUS_READY_TO_SHIP,
        self::SHIPMENT_STATUS_SHIPPED,
        self::SHIPMENT_STATUS_RECEIVING,
        self::SHIPMENT_STATUS_CANCELLED,
        self::SHIPMENT_STATUS_DELETED,
        self::SHIPMENT_STATUS_CLOSED,
        self::SHIPMENT_STATUS_ERROR,
        self::SHIPMENT_STATUS_IN_TRANSIT,
        self::SHIPMENT_STATUS_DELIVERED,
        self::SHIPMENT_STATUS_CHECKED_IN,
    ];

    public const string SORT_BY_LAST_UPDATED_TIME = 'LAST_UPDATED_TIME';
    public const string SORT_BY_CREATION_TIME = 'CREATION_TIME';
    /** @var list<string>  */
    public const array SORT_BY_OPTIONS = [
        self::SORT_BY_LAST_UPDATED_TIME,
        self::SORT_BY_CREATION_TIME,
    ];

    public const string SORT_ORDER_ASC = 'ASC';
    public const string SORT_ORDER_DESC = 'DESC';
    /** @var list<string>  */
    public const array SORT_ORDER_OPTIONS = [
        self::SORT_ORDER_ASC,
        self::SORT_ORDER_DESC,
    ];

    public const string REPORT_OPTION_INTERNAL_MAX_PAGE = 'iMaxPage';
    public const string REPORT_OPTION_PAGE_SIZE = 'pageSize';
    public const string REPORT_OPTION_SORT_BY = 'sortBy';
    public const string REPORT_OPTION_SORT_ORDER = 'sortOrder';

    public const string REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENTS = 'inboundShipments';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLANS = 'inboundPlans';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_PLAN_DETAIL= 'inboundPlanDetail';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_BOXES = 'inboundShipmentBoxes';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_PALLETS = 'inboundShipmentPallets';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLAN_ITEMS = 'inboundPlanItems';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLAN_PALLETS = 'inboundPlanPallets';
    public const string REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_DETAILS = 'inboundShipmentDetails';

    public function isRequestTypeFbaInboundShipments(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENTS === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundShipmentDetails(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_DETAILS === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundListInboundPlans(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLANS === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundPlanDetail(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_PLAN_DETAIL === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundShipmentBoxes(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_BOXES === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundShipmentPallets(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_SHIPMENT_PALLETS === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundListInboundPlanItems(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLAN_ITEMS === $this->getRequestType();
    }

    public function isRequestTypeFbaInboundListInboundPlanPallets(): bool
    {
        return self::REQUEST_TYPE_GET_FBA_INBOUND_LIST_INBOUND_PLAN_PALLETS === $this->getRequestType();
    }

    protected function getOptionValue(string $optionName): mixed
    {
        $options = $this->getReportOptions();
        if (empty($options)) {
            return null;
        }

        return $options[$optionName] ?? null;
    }

    public function getOptionInternalMaxPage(?int $defaultValue = null): ?int
    {
        return $this->getOptionValue(self::REPORT_OPTION_INTERNAL_MAX_PAGE) ?? $defaultValue;
    }

    public function getOptionPageSize(?int $defaultValue = null): ?int
    {
        return $this->getOptionValue(self::REPORT_OPTION_PAGE_SIZE) ?? $defaultValue;
    }

    public function getOptionSortBy(?string $defaultValue = null): ?string
    {
        return $this->getOptionValue(self::REPORT_OPTION_SORT_BY) ?? $defaultValue;
    }

    public function getOptionSortOrder(?string $defaultValue = null): ?string
    {
        return $this->getOptionValue(self::REPORT_OPTION_SORT_ORDER) ?? $defaultValue;
    }

    public function getSingleItemFromStatusList(): ?string
    {
        if (empty($this->statusList)) {
            return null;
        }
        if (1 < count($this->statusList)) {
            throw new \InvalidArgumentException("statusList has more than one item");
        }
        return reset($this->statusList);
    }

    /*************************************
     *** ParsableEntity implementation ***
     *************************************/

    /**
     * @throws \Exception
     */
    public function getRecordClass(): string
    {
        throw new \RuntimeException('getRecordClass should not be used by ' . get_class($this));
    }

    /**
     * @throws \Exception
     */
    public function getArrayParseNotYetImplementedFields(): array
    {
        throw new \RuntimeException('getArrayParseNotYetImplementedFields should not be used by ' . get_class($this));
    }

    /**
     * @throws \Exception
     */
    public function getArrayParseFieldMapping(): array
    {
        throw new \RuntimeException('getArrayParseFieldMapping should not be used by ' . get_class($this));
    }

    /**
     * @throws \Exception
     */
    public function getTopLevelIdentifierFromArray(array $dataArray): string
    {
        throw new \RuntimeException('getTopLevelIdentifierFromArray should not be used by ' . get_class($this));
    }

    public function generateExpectedFilename(string $suggestedExtension=null): string
    {
        $filename = $this->generateFilenamePrefix();
        $filename .= $this->getAccountProfile()->getAlias() . '-' . $this->getId();
        if ($this->getStartDate()) {
            $filename .= '-S' . $this->getStartDate()->format('ymd');
        } elseif ($this->getRequestDate()) {
            $filename .= '-R' . $this->getRequestDate()->format('ymd');
        }
        $extension = $suggestedExtension ?: (BaseApiBridge::FILE_EXTENSION_JSON . BaseApiBridge::FILE_EXTENSION_GZIP);
        $filename .= $extension;

        return $filename;
    }

    public function buildDateKey(): string
    {
        throw new \InvalidArgumentException("buildDateKey() is not implemented in ApiRequestSpaFbaInbound");
    }

    /****************************************
     *** ApiRequestSpaBase implementation ***
     ****************************************/

    /** @var ?static $parentRequest */
    #[ORM\ManyToOne(targetEntity: ApiRequestSpaFbaInbound::class)]
    #[ORM\JoinColumn(nullable: true)]
    protected ?ApiRequestSpaFbaInbound $parentRequest = null;

    public function getParentRequest(): ?static
    {
        return $this->parentRequest;
    }

    /**
     * @param static $parentRequest
     * @return $this
     */
    public function setParentRequest($parentRequest): static
    {
        $this->parentRequest = $parentRequest;

        return $this;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(targetEntity: SpaFbaInboundPlan::class)]
    #[ORM\JoinColumn(nullable: true)]
    protected ?SpaFbaInboundPlan $inboundPlan = null;

    #[ORM\Column(type: TYPES::STRING, length: 25, nullable: true)]
    protected ?string $queryType = null;

    /**
     * @var list<string>|null
     */
    #[ORM\Column(type: TYPES::SIMPLE_ARRAY, nullable: true)]
    protected ?array $statusList = null;

    /**
     * @var list<string>|null
     */
    #[ORM\Column(type: TYPES::SIMPLE_ARRAY, nullable: true)]
    protected ?array $shipmentIds = null;

    #[ORM\Column(type: Types::STRING, length: 38, nullable: true)]
    protected ?string $azInboundPlanId = null;

    /**
     * @var array<string, null|int|string|float>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $reportOptions = null;

    /*******************************
     *** Modified generated code ***
     ******************************/

    /**
     * @param array<string, null|int|string|float>|null $reportOptions
     * @return $this
     */
    public function setReportOptions(?array $reportOptions): static
    {
        $reportOptions = array_filter($reportOptions);
        if (empty($reportOptions)) {
            $reportOptions = null;
        }

        $this->reportOptions = $reportOptions;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getQueryType(): ?string
    {
        return $this->queryType;
    }

    public function setQueryType(?string $queryType): static
    {
        $this->queryType = $queryType;

        return $this;
    }

    public function getStatusList(): ?array
    {
        return $this->statusList;
    }

    public function setStatusList(?array $statusList): static
    {
        $this->statusList = $statusList;

        return $this;
    }

    public function getShipmentIds(): ?array
    {
        return $this->shipmentIds;
    }

    public function setShipmentIds(?array $shipmentIds): static
    {
        $this->shipmentIds = $shipmentIds;

        return $this;
    }

    public function getAzInboundPlanId(): ?string
    {
        return $this->azInboundPlanId;
    }

    public function setAzInboundPlanId(?string $azInboundPlanId): static
    {
        $this->azInboundPlanId = $azInboundPlanId;

        return $this;
    }

    public function getReportOptions(): ?array
    {
        return $this->reportOptions;
    }

    public function getInboundPlan(): ?SpaFbaInboundPlan
    {
        return $this->inboundPlan;
    }

    public function setInboundPlan(?SpaFbaInboundPlan $inboundPlan): static
    {
        $this->inboundPlan = $inboundPlan;

        return $this;
    }
}
