<?php

namespace App\Entity\SpaAwd;

use App\Entity\GravitiqBaseEntity;
use App\Entity\MagicMutatorTrait;
use App\Entity\SpaApiRequest\ApiRequestSpaAwd;
use App\Repository\SpaAwd\AwdInboundShipmentCartonContentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method setExpirationDateFromString(mixed $value)
 * @phpstan-import-type ShipmentSkuQuantitiesArrayShape from AwdInboundShipment
 */
#[ORM\Entity(repositoryClass: AwdInboundShipmentCartonContentRepository::class)]
class AwdInboundShipmentCartonContent extends GravitiqBaseEntity
{
    use MagicMutatorTrait;

    public const array REQUIRED_SKU_QUANTITY_KEYS = ['sku', 'expectedQuantity', 'receivedQuantity'];

    /**
     * @param array<int, array{name:string, value:string}> $attributes
     * @return $this
     */
    public function setAttributesFromArray(array $attributes): static
    {
        $dataArray = [];
        foreach ($attributes as $attribute) {
            if ($attribute['name'] === 'asin') {
                continue;
            }
            $dataArray[$attribute['name']] = $attribute['value'];
        }
        $this->setAttributes($dataArray);

        return $this;
    }

    /**
     * @param array<string, ShipmentSkuQuantitiesArrayShape> $shipmentSkuQuantities
     * @return $this
     * @note ampersand symbol + var name "&$shipmentSkuQuantities" is used to pass by reference - meaning any changes to the array/variable will be reflected in the original array/variable, e.g. unsetting the used data
     */
    public function setExpectedAndReceivedQuantityFromArray(array &$shipmentSkuQuantities): static
    {
        if (empty($shipmentSkuQuantities)) {
            return $this;
        }

        if (isset($shipmentSkuQuantities[$this->getSku()])) {
            $shipmentSkuQuantity = $shipmentSkuQuantities[$this->getSku()];
            foreach (self::REQUIRED_SKU_QUANTITY_KEYS as $keys) {
                if (!array_key_exists($keys, $shipmentSkuQuantity)) {
                    throw new \InvalidArgumentException('Missing required key ' . $keys . ' in shipment sku quantity array');
                }
            }
            $this->setExpectedQuantity($shipmentSkuQuantity['expectedQuantity']['quantity']);
            $this->setExpectedQuantityUnit($shipmentSkuQuantity['expectedQuantity']['unitOfMeasurement']);
            $this->setReceivedQuantity($shipmentSkuQuantity['receivedQuantity']['quantity']);
            $this->setReceivedQuantityUnit($shipmentSkuQuantity['receivedQuantity']['unitOfMeasurement']);
            unset($shipmentSkuQuantities[$this->getSku()]);
        }

        return $this;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(targetEntity: AwdInboundShipmentCarton::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected AwdInboundShipmentCarton $awdInboundShipmentCarton;

    #[ORM\ManyToOne(targetEntity: ApiRequestSpaAwd::class)]
    #[ORM\JoinColumn(name: 'requestId', nullable: false)]
    protected ?ApiRequestSpaAwd $createdFromRequest;

    #[ORM\ManyToOne(targetEntity: ApiRequestSpaAwd::class)]
    #[ORM\JoinColumn(name: 'updateRequestId', nullable: true)]
    protected ?ApiRequestSpaAwd $updatedFromRequest = null;

    #[ORM\Column(type: Types::STRING, length: 10)]
    protected string $asin;

    #[ORM\Column(type: Types::STRING, length: 50)]
    protected string $sku;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $quantity;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $expirationDate = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $prepDetails = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $expectedQuantity = null;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $expectedQuantityUnit = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $receivedQuantity = null;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $receivedQuantityUnit = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $attributes = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getAsin(): ?string
    {
        return $this->asin;
    }

    public function setAsin(string $asin): static
    {
        $this->asin = $asin;

        return $this;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getPrepDetails(): ?array
    {
        return $this->prepDetails;
    }

    public function setPrepDetails(?array $prepDetails): static
    {
        $this->prepDetails = $prepDetails;

        return $this;
    }

    public function getExpectedQuantity(): ?int
    {
        return $this->expectedQuantity;
    }

    public function setExpectedQuantity(?int $expectedQuantity): static
    {
        $this->expectedQuantity = $expectedQuantity;

        return $this;
    }

    public function getExpectedQuantityUnit(): ?string
    {
        return $this->expectedQuantityUnit;
    }

    public function setExpectedQuantityUnit(?string $expectedQuantityUnit): static
    {
        $this->expectedQuantityUnit = $expectedQuantityUnit;

        return $this;
    }

    public function getReceivedQuantity(): ?int
    {
        return $this->receivedQuantity;
    }

    public function setReceivedQuantity(?int $receivedQuantity): static
    {
        $this->receivedQuantity = $receivedQuantity;

        return $this;
    }

    public function getReceivedQuantityUnit(): ?string
    {
        return $this->receivedQuantityUnit;
    }

    public function setReceivedQuantityUnit(?string $receivedQuantityUnit): static
    {
        $this->receivedQuantityUnit = $receivedQuantityUnit;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function setAttributes(?array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getAwdInboundShipmentCarton(): ?AwdInboundShipmentCarton
    {
        return $this->awdInboundShipmentCarton;
    }

    public function setAwdInboundShipmentCarton(?AwdInboundShipmentCarton $awdInboundShipmentCarton): static
    {
        $this->awdInboundShipmentCarton = $awdInboundShipmentCarton;

        return $this;
    }

    public function getExpirationDate(): ?\DateTimeInterface
    {
        return $this->expirationDate;
    }

    public function setExpirationDate(?\DateTimeInterface $expirationDate): static
    {
        $this->expirationDate = $expirationDate;

        return $this;
    }

    public function getCreatedFromRequest(): ?ApiRequestSpaAwd
    {
        return $this->createdFromRequest;
    }

    public function setCreatedFromRequest(?ApiRequestSpaAwd $createdFromRequest): static
    {
        $this->createdFromRequest = $createdFromRequest;

        return $this;
    }

    public function getUpdatedFromRequest(): ?ApiRequestSpaAwd
    {
        return $this->updatedFromRequest;
    }

    public function setUpdatedFromRequest(?ApiRequestSpaAwd $updatedFromRequest): static
    {
        $this->updatedFromRequest = $updatedFromRequest;

        return $this;
    }


}
