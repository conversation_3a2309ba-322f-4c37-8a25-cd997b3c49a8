<?php

namespace App\Entity\AzRank;

use App\Entity\GravitiqBaseEntity;
use App\Entity\MagicMutatorTrait;
use App\Entity\UpdatableRecordTrait;
use App\Repository\AzRank\AzRankCustomerOrderRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method static setAmountPaidFromDecimal(string|float|int|null $value)
 */
#[ORM\Table(name: 'azr_customer_order')]
#[ORM\Index(columns: ['azrOrderId'], name: 'idx_orderId')]
#[ORM\Index(columns: ['azrDirtyOrderId'], name: 'idx_dirtyOrderId')]
#[ORM\Entity(repositoryClass: AzRankCustomerOrderRepository::class)]
class AzRankCustomerOrder extends GravitiqBaseEntity
{
    /** @use UpdatableRecordTrait<ApiRequestAzRank> */
    use MagicMutatorTrait, UpdatableRecordTrait;

    protected const string Q_OPTION_YES = 'YES';
    protected const string Q_OPTION_NO = 'NO';
    protected const array Q_OPTIONS = [self::Q_OPTION_YES, self::Q_OPTION_NO];

    public function convertBooleanToYesOrNo(?bool $booleanYesOrNo): ?string
    {
        if (is_null($booleanYesOrNo)) {
            return null;
        }
        return $booleanYesOrNo ? self::Q_OPTION_YES : self::Q_OPTION_NO;
    }

    public function setSearchKeywordAndShortcode(?string $searchKeyword): static
    {
        list($searchKeyword, $shortcode) = ApiRequestAzRank::extractSearchKeywordAndShortcode($searchKeyword);
        $this->setShortcode($shortcode);
        $this->setSearchKeyword($searchKeyword);

        return $this;
    }

    /*******************************************
     *** UpdatableRecordTrait implementation ***
     *******************************************/

    #[ORM\ManyToOne(targetEntity: ApiRequestAzRank::class)]
    #[ORM\JoinColumn(name: 'requestId', nullable: false)]
    protected ApiRequestAzRank $createdFromRequest;

    #[ORM\ManyToOne(targetEntity: ApiRequestAzRank::class)]
    #[ORM\JoinColumn(name: 'updateRequestId', nullable: true)]
    protected ?ApiRequestAzRank $updatedFromRequest = null;

    public function __toString(): string
    {
        if (!empty($this->getAzrOrderId())) {
            return "order: {$this->getAzrOrderId()}";
        } else {
            return "order: {$this->getAzrDirtyOrderId()}";
        }
    }

    public function getId(): int
    {
        return $this->id ?? 0;
    }

    /**
     * @param self $record
     * @return bool
     */
    public function matchesRecord($record): bool
    {
        if (!empty($record->getAzrOrderId())) {
            if ($record->getAzrOrderId() !== $this->getAzrOrderId()) {
                return false;
            }
        } else if (!empty($record->getAzrDirtyOrderId())) {
            if ($record->getAzrDirtyOrderId() !== $this->getAzrDirtyOrderId()) {
                return false;
            }
        }

        return true;
    }

    /*************************
     ** Field definitions ***
     ************************/

    #[ORM\ManyToOne(targetEntity: AzRankProject::class, cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'projectId', nullable: false)]
    protected AzRankProject $project;

    #[ORM\Column(type: Types::STRING, length: 100)]
    protected string $spreadsheetId;

    #[ORM\Column(type: Types::STRING, length: 10)]
    protected string $gid;

    #[ORM\Column(type: Types::STRING, length: 20, options: ['default' => ''])]
    protected string $azrOrderId;

    #[ORM\Column(type: Types::STRING, length: 255, options: ['default' => ''])]
    protected string $azrDirtyOrderId = '';

    #[ORM\Column(type: Types::STRING, length: 19, options: ['default' => ''])]
    protected string $manOrderId = '';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $orderTimestamp = null;

    #[ORM\Column(type: Types::STRING, length: 255, options: ['default' => ''])]
    protected string $dirtyOrderTimestamp = '';

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $orderDate = null;

    #[ORM\Column(type: Types::STRING, length: 255, options: ['default' => ''])]
    protected string $dirtyOrderDate = '';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $spaOrderDate = null;


    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    protected ?string $orderStatus = null;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $firstName = null;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $lastName = null;

    #[ORM\Column(type: Types::STRING, length: 32, nullable: true)]
    protected ?string $fbName = null;

    #[ORM\Column(type: Types::STRING, length: 32, nullable: true)]
    protected ?string $customerEmail = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    protected ?string $productOrdered = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $amountPaid = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    protected ?string $searchKeyword = null;

    #[ORM\Column(type: Types::STRING, length: 1, nullable: true)]
    protected ?string $shortcode = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $qUseDifferentKeyword = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $qUseFiltersForSimilarProduct = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $qLikeAboutProductDescription = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $qIsMainPhotoEyeCatching = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    protected ?string $qIsThereMoreAppealingPhoto = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $qAskSellerAnythingAboutTheProduct = null;

    #[ORM\Column(type: Types::STRING, length:15, nullable: true)]
    protected ?string $qIfYouWereInstructedToUseCoupon = null;

    #[ORM\Column(type: Types::STRING, length: 30, nullable: true)]
    protected ?string $qHowDidYouOrderThisToday = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $qNotesForAdmin = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $customerComment = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $refund = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $refundComment = null;

    /*******************************
     *** Modified generated code ***
     ******************************/

    public function setAzrOrderId(?string $azrOrderId): static
    {
        if ($this->getManOrderId() !== '') {
            $newOrderIdValueToUse = $this->getManOrderId();
        } else {
            $newOrderIdValueToUse = $azrOrderId ?? '';
            if (strlen($newOrderIdValueToUse) > 20) {
                throw new \InvalidArgumentException('Order ID cannot be longer than 20 characters: ' . $newOrderIdValueToUse);
            }
        }

        $this->azrOrderId = $newOrderIdValueToUse;

        return $this;
    }

    public function setOrderTimestamp(\DateTimeInterface|string|null $orderTimestamp): static
    {
        $this->orderTimestamp = GravitiqTools::castToDateTime($orderTimestamp);

        return $this;
    }

    public function setOrderDate(\DateTimeInterface|string|null $orderDate): static
    {
        $this->orderDate = GravitiqTools::castToDateTime($orderDate);

        return $this;
    }

    public function setQUseFiltersForSimilarProduct(?string $qUseFiltersForSimilarProduct): static//received YES or NO
    {
        if (empty($qUseFiltersForSimilarProduct)) {
            $this->qUseFiltersForSimilarProduct = null;
        } else {
            $qUseFiltersForSimilarProduct = strtoupper($qUseFiltersForSimilarProduct);
            if (!in_array($qUseFiltersForSimilarProduct, self::Q_OPTIONS)) {
                $this->qUseFiltersForSimilarProduct = null;
            } else {
                $this->qUseFiltersForSimilarProduct = self::Q_OPTION_YES === $qUseFiltersForSimilarProduct;
            }
        }

        return $this;
    }

    public function getQUseFiltersForSimilarProduct(bool $convertBoolToYesOrNo = true): bool|string|null// had to do this because its causing error on parsing an update
    {
        if ($convertBoolToYesOrNo) {
            return $this->convertBooleanToYesOrNo($this->isQUseFiltersForSimilarProduct());
        }

        return $this->isQUseFiltersForSimilarProduct();
    }

    public function setQIsMainPhotoEyeCatching(?string $qIsMainPhotoEyeCatching): static//received YES or NO
    {
        if (empty($qIsMainPhotoEyeCatching)) {
            $this->qIsMainPhotoEyeCatching = null;
        } else {
            $qIsMainPhotoEyeCatching = strtoupper($qIsMainPhotoEyeCatching);
            if (!in_array($qIsMainPhotoEyeCatching, self::Q_OPTIONS)) {
                $this->qIsMainPhotoEyeCatching = null;
            } else {
                $this->qIsMainPhotoEyeCatching = self::Q_OPTION_YES === $qIsMainPhotoEyeCatching;
            }
        }

        return $this;
    }

    public function getQIsMainPhotoEyeCatching(bool $convertBoolToYesOrNo = true): bool|string|null// had to do this because its causing error on parsing an update
    {
        if ($convertBoolToYesOrNo) {
            return $this->convertBooleanToYesOrNo($this->isQIsMainPhotoEyeCatching());
        }

        return $this->isQIsMainPhotoEyeCatching();
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getSpreadsheetId(): ?string
    {
        return $this->spreadsheetId;
    }

    public function setSpreadsheetId(string $spreadsheetId): static
    {
        $this->spreadsheetId = $spreadsheetId;

        return $this;
    }

    public function getGid(): ?string
    {
        return $this->gid;
    }

    public function setGid(string $gid): static
    {
        $this->gid = $gid;

        return $this;
    }

    public function getAzrOrderId(): ?string
    {
        return $this->azrOrderId;
    }

    public function getAzrDirtyOrderId(): ?string
    {
        return $this->azrDirtyOrderId;
    }

    public function setAzrDirtyOrderId(string $azrDirtyOrderId): static
    {
        $this->azrDirtyOrderId = $azrDirtyOrderId;

        return $this;
    }

    public function getManOrderId(): ?string
    {
        return $this->manOrderId;
    }

    public function setManOrderId(string $manOrderId): static
    {
        $this->manOrderId = $manOrderId;

        return $this;
    }

    public function getOrderTimestamp(): ?\DateTimeInterface
    {
        return $this->orderTimestamp;
    }

    public function getDirtyOrderTimestamp(): ?string
    {
        return $this->dirtyOrderTimestamp;
    }

    public function setDirtyOrderTimestamp(string $dirtyOrderTimestamp): static
    {
        $this->dirtyOrderTimestamp = $dirtyOrderTimestamp;

        return $this;
    }

    public function getOrderDate(): ?\DateTimeInterface
    {
        return $this->orderDate;
    }

    public function getDirtyOrderDate(): ?string
    {
        return $this->dirtyOrderDate;
    }

    public function setDirtyOrderDate(string $dirtyOrderDate): static
    {
        $this->dirtyOrderDate = $dirtyOrderDate;

        return $this;
    }

    public function getSpaOrderDate(): ?\DateTimeInterface
    {
        return $this->spaOrderDate;
    }

    public function setSpaOrderDate(?\DateTimeInterface $spaOrderDate): static
    {
        $this->spaOrderDate = $spaOrderDate;

        return $this;
    }

    public function getOrderStatus(): ?string
    {
        return $this->orderStatus;
    }

    public function setOrderStatus(?string $orderStatus): static
    {
        $this->orderStatus = $orderStatus;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): static
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): static
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFbName(): ?string
    {
        return $this->fbName;
    }

    public function setFbName(?string $fbName): static
    {
        $this->fbName = $fbName;

        return $this;
    }

    public function getCustomerEmail(): ?string
    {
        return $this->customerEmail;
    }

    public function setCustomerEmail(?string $customerEmail): static
    {
        $this->customerEmail = $customerEmail;

        return $this;
    }

    public function getProductOrdered(): ?string
    {
        return $this->productOrdered;
    }

    public function setProductOrdered(?string $productOrdered): static
    {
        $this->productOrdered = $productOrdered;

        return $this;
    }

    public function getAmountPaid(): ?int
    {
        return $this->amountPaid;
    }

    public function setAmountPaid(?int $amountPaid): static
    {
        $this->amountPaid = $amountPaid;

        return $this;
    }

    public function getSearchKeyword(): ?string
    {
        return $this->searchKeyword;
    }

    public function setSearchKeyword(?string $searchKeyword): static
    {
        $this->searchKeyword = $searchKeyword;

        return $this;
    }

    public function getShortcode(): ?string
    {
        return $this->shortcode;
    }

    public function setShortcode(?string $shortcode): static
    {
        $this->shortcode = $shortcode;

        return $this;
    }

    public function getQUseDifferentKeyword(): ?string
    {
        return $this->qUseDifferentKeyword;
    }

    public function setQUseDifferentKeyword(?string $qUseDifferentKeyword): static
    {
        $this->qUseDifferentKeyword = $qUseDifferentKeyword;

        return $this;
    }

    public function isQUseFiltersForSimilarProduct(): ?bool
    {
        return $this->qUseFiltersForSimilarProduct;
    }

    public function getQLikeAboutProductDescription(): ?string
    {
        return $this->qLikeAboutProductDescription;
    }

    public function setQLikeAboutProductDescription(?string $qLikeAboutProductDescription): static
    {
        $this->qLikeAboutProductDescription = $qLikeAboutProductDescription;

        return $this;
    }

    public function isQIsMainPhotoEyeCatching(): ?bool
    {
        return $this->qIsMainPhotoEyeCatching;
    }

    public function getQIsThereMoreAppealingPhoto(): ?string
    {
        return $this->qIsThereMoreAppealingPhoto;
    }

    public function setQIsThereMoreAppealingPhoto(?string $qIsThereMoreAppealingPhoto): static
    {
        $this->qIsThereMoreAppealingPhoto = $qIsThereMoreAppealingPhoto;

        return $this;
    }

    public function getQAskSellerAnythingAboutTheProduct(): ?string
    {
        return $this->qAskSellerAnythingAboutTheProduct;
    }

    public function setQAskSellerAnythingAboutTheProduct(?string $qAskSellerAnythingAboutTheProduct): static
    {
        $this->qAskSellerAnythingAboutTheProduct = $qAskSellerAnythingAboutTheProduct;

        return $this;
    }

    public function getQIfYouWereInstructedToUseCoupon(): ?string
    {
        return $this->qIfYouWereInstructedToUseCoupon;
    }

    public function setQIfYouWereInstructedToUseCoupon(?string $qIfYouWereInstructedToUseCoupon): static
    {
        $this->qIfYouWereInstructedToUseCoupon = $qIfYouWereInstructedToUseCoupon;

        return $this;
    }

    public function getQHowDidYouOrderThisToday(): ?string
    {
        return $this->qHowDidYouOrderThisToday;
    }

    public function setQHowDidYouOrderThisToday(?string $qHowDidYouOrderThisToday): static
    {
        $this->qHowDidYouOrderThisToday = $qHowDidYouOrderThisToday;

        return $this;
    }

    public function getQNotesForAdmin(): ?string
    {
        return $this->qNotesForAdmin;
    }

    public function setQNotesForAdmin(?string $qNotesForAdmin): static
    {
        $this->qNotesForAdmin = $qNotesForAdmin;

        return $this;
    }

    public function getCustomerComment(): ?string
    {
        return $this->customerComment;
    }

    public function setCustomerComment(?string $customerComment): static
    {
        $this->customerComment = $customerComment;

        return $this;
    }

    public function getRefund(): ?string
    {
        return $this->refund;
    }

    public function setRefund(?string $refund): static
    {
        $this->refund = $refund;

        return $this;
    }

    public function getRefundComment(): ?string
    {
        return $this->refundComment;
    }

    public function setRefundComment(?string $refundComment): static
    {
        $this->refundComment = $refundComment;

        return $this;
    }

    public function getProject(): ?AzRankProject
    {
        return $this->project;
    }

    public function setProject(?AzRankProject $project): static
    {
        $this->project = $project;

        return $this;
    }
}
