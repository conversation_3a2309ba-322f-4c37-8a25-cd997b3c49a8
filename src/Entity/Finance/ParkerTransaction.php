<?php

namespace App\Entity\Finance;

use App\Entity\Company;
use App\Repository\Finance\ParkerTransactionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'fin_parker_transaction')]
#[ORM\Entity(repositoryClass: ParkerTransactionRepository::class)]
class ParkerTransaction
{
    public const string SOURCE_PAYMENT = 'P';
    public const string SOURCE_SPEND = 'S';

    protected const string DATETIME_FORMAT_PAYMENT = 'M j, Y'; // e.g. Aug 7, 2022
    protected const string DATETIME_FORMAT_TRANSACTION = 'M j, h:i a'; // e.g. Jun 7, 04:59 pm
    protected const int DAYS_UNTIL_DUE_DATE = 62;
    protected const string REPAYMENT_BENEFICIARY = 'PARKER REPAYMENT';

    public function alreadyExistsInCollection(array $transactions): bool
    {
        /** @var ParkerTransaction $transaction */
        foreach ($transactions as $transaction) {
            if ($this->getEventDate()->format('c') !== $transaction->getEventDate()->format('c')) {
                continue;
            }
            if ($this->getTotalAmount() !== $transaction->getTotalAmount()) {
                continue;
            }
            if ($this->getBeneficiary() !== $transaction->getBeneficiary()) {
                continue;
            }
            if ($this->getCompany()->getId() !== $transaction->getCompany()->getId()) {
                continue;
            }
            if ($this->getSource() !== $transaction->getSource()) {
                continue;
            }
            return true;
        }
        return false;
    }

    public function setEventDateFromParkerDateString(string $dateString): static
    {
        $eventDate = \DateTime::createFromFormat(self::DATETIME_FORMAT_TRANSACTION, $dateString);
        $this->setEventDate($eventDate);
        $dueDate = (clone $eventDate)->add(new \DateInterval('P' . self::DAYS_UNTIL_DUE_DATE . 'D'));
        return $this->setDueDate($dueDate);
    }

    public function setPaymentEventDateFromParkerDateString(string $dateString): static
    {
        $eventDate = \DateTime::createFromFormat(self::DATETIME_FORMAT_PAYMENT, $dateString);
        $eventDate = new \DateTime($eventDate->format('Y-m-d 00:00:00'));
        return $this->setEventDate($eventDate)->setDueDate($eventDate);
    }

    /**
     * @param string $amountString  From Parker site, e.g. $55,696.50
     * @return $this
     */
    public function setTotalAmountFromParkerPaymentAmountString(string $amountString): static
    {
        return $this->setTotalAmount(self::convertAmountStringToInteger($amountString));
    }
    /**
     * @param string $amountString  From Parker site, e.g. $55,696.50
     * @return $this
     */
    public function setTotalAmountFromParkerSpendAmountString(string $amountString): static
    {
        return $this->setTotalAmount(-1 * self::convertAmountStringToInteger($amountString));
    }

    public function getTotalAmountDecimal(): float
    {
        return $this->getTotalAmount() / 100;
    }

    public function getEventDateExcel() {
        return \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($this->getEventDate());
    }

    public function getDueDateExcel() {
        return \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($this->getDueDate());
    }

    public function setSourceToPayment(): self
    {
        $this->source = self::SOURCE_PAYMENT;

        return $this;
    }
    public function setSourceToSpend(): self
    {
        $this->source = self::SOURCE_SPEND;

        return $this;
    }


    public static function convertAmountStringToInteger(string $amountString): int
    {
        $amountString = str_replace('$', '', $amountString);
        $amountString = str_replace(',', '', $amountString);
        return round(100 * $amountString);
    }

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    protected $id;

    #[ORM\ManyToOne(targetEntity: Company::class, inversedBy: 'parkerTransactions')]
    #[ORM\JoinColumn(nullable: false)]
    protected Company $company;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    protected \DateTimeInterface $eventDate;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    protected \DateTimeInterface $dueDate;

    #[ORM\Column(type: Types::STRING)]
    protected string $beneficiary;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $totalAmount;

    #[ORM\Column(type: Types::STRING, length: 1, options: ['default' => self::SOURCE_SPEND])]
    protected string $source = self::SOURCE_SPEND;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEventDate(): ?\DateTimeInterface
    {
        return $this->eventDate;
    }

    public function setEventDate(\DateTimeInterface $eventDate): self
    {
        $this->eventDate = $eventDate;

        return $this;
    }

    public function getDueDate(): ?\DateTimeInterface
    {
        return $this->dueDate;
    }

    public function setDueDate(\DateTimeInterface $dueDate): self
    {
        $this->dueDate = $dueDate;

        return $this;
    }

    public function getBeneficiary(): ?string
    {
        return $this->beneficiary;
    }

    public function setBeneficiary(string $beneficiary): self
    {
        $this->beneficiary = $beneficiary;

        return $this;
    }

    public function getTotalAmount(): ?int
    {
        return $this->totalAmount;
    }

    public function setTotalAmount(int $totalAmount): self
    {
        $this->totalAmount = $totalAmount;

        return $this;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

}