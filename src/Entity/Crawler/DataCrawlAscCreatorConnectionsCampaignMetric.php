<?php

namespace App\Entity\Crawler;

use App\Entity\MagicMutatorTrait;
use App\Entity\UpdatableRecordTrait;
use App\Repository\Crawler\DataCrawlAscCreatorConnectionsCampaignMetricRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method static setSpendFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setSalesFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setSalesInCurrencyAmountFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setEarningsAmountFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setAsinInfoPriceFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setAsinInfoMaxPriceFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setAsinInfoMinPriceFromDecimal(string|float|int|null $val) - via MagicMutatorTrait
 * @method static setMetricsAvailableFromBooleanText(string|bool|null $val) - via MagicMutatorTrait
 */
#[ORM\Table(name: 'data_crawl_asc_cc_campaign_metric')]
#[ORM\Entity(repositoryClass: DataCrawlAscCreatorConnectionsCampaignMetricRepository::class)]
class DataCrawlAscCreatorConnectionsCampaignMetric
{
    /** @use UpdatableRecordTrait<ApiRequestCrawlAsc> */
    use MagicMutatorTrait, UpdatableRecordTrait {
        UpdatableRecordTrait::getSettersExcludedFromUpdate as private updatableGetSettersExcludedFromUpdate;
    }

    public const string METRIC_TYPE_LIFETIME = 'lifetimeMetrics';
    public const string METRIC_TYPE_ALL_ASINS = 'metricsForAllAsins';

    /**
     * @return float|null
     */
    public function getSpendAsDecimal(): ?float
    {
        return $this->getSpend() / 100;
    }

    /**
     * @return float|null
     */
    public function getSalesAsDecimal(): ?float
    {
        return $this->getSales() / 100;
    }


    /**
     * @param array<string, mixed> $metricItemArray
     * @return $this
     */
    public function updateFromLifetimeMetricsArray(array $metricItemArray): static
    {
        $this->setMetricType(self::METRIC_TYPE_LIFETIME);
        $this->setMetricKey($metricItemArray['metricKey']);
        $this->setMetricsAvailableFromBooleanText($metricItemArray['areMetricsAvailable']);

        if (empty($metricItemArray['campaignMetrics'])) {
            return $this;
        }
        $this->setSpendFromDecimal($metricItemArray['campaignMetrics']['spend']);
        $this->setSalesFromDecimal($metricItemArray['campaignMetrics']['sales']);
        $this->setSalesInCurrencyAmountFromDecimal($metricItemArray['campaignMetrics']['salesInCurrency']['amount']);
        $this->setClicks($metricItemArray['campaignMetrics']['clicks']);
        $this->setOrders($metricItemArray['campaignMetrics']['orders']);
        $this->setCurrencyCode($metricItemArray['campaignMetrics']['currencyCode']);
        $this->setEarningsAmountFromDecimal($metricItemArray['campaignMetrics']['earnings']['amount']);

        return $this;
    }

    /**
     * @param array<string, mixed> $metricItemArray
     * @return $this
     */
    public function updateFromAllAsinsArray(array $metricItemArray): static
    {
        $this->setMetricType(self::METRIC_TYPE_ALL_ASINS);
        $this->setMetricKey($metricItemArray['metricKey']);
        if (empty($metricItemArray['campaignMetrics'])) {
            return $this;
        }

        $priceString = $metricItemArray['campaignMetrics']['asinInfo']['price'] ?? null;
        $priceFloat = floatval(preg_replace('/[^\d.]/', '', $priceString));
        $this->setAsinInfoPriceFromDecimal($priceFloat);
        $this->setSpendFromDecimal($metricItemArray['campaignMetrics']['spend'] ?? null);
        $this->setSalesFromDecimal($metricItemArray['campaignMetrics']['sales'] ?? null);
        $this->setClicks($metricItemArray['campaignMetrics']['clicks'] ?? null);
        $this->setOrders($metricItemArray['campaignMetrics']['orders'] ?? null);
        $this->setCurrencyCode($metricItemArray['campaignMetrics']['currencyCode'] ?? null);
        if (!empty($metricItemArray['campaignMetrics']['asinInfo'])) {
            $this->setAsinInfoAsin($metricItemArray['campaignMetrics']['asinInfo']['asin'] ?? null);
            $this->setAsinInfoTitle($metricItemArray['campaignMetrics']['asinInfo']['title'] ?? null);
            $this->setAsinInfoNumberOfStars($metricItemArray['campaignMetrics']['asinInfo']['numberOfStars'] ?? null);
            $this->setAsinInfoNumberOfReviews($metricItemArray['campaignMetrics']['asinInfo']['numberOfReviews'] ?? null);
            $this->setAsinInfoMaxPriceFromDecimal($metricItemArray['campaignMetrics']['asinInfo']['maxPrice'] ?? null);
            $this->setAsinInfoMinPriceFromDecimal($metricItemArray['campaignMetrics']['asinInfo']['minPrice'] ?? null);
            $this->setAsinInfoFullStars($metricItemArray['campaignMetrics']['asinInfo']['fullStars'] ?? null);
            $this->setAsinInfoHalfStar($metricItemArray['campaignMetrics']['asinInfo']['halfStar'] ?? null);
            $this->setAsinInfoParent($metricItemArray['campaignMetrics']['asinInfo']['parent'] ?? null);
            $this->setAsinInfoImage($metricItemArray['campaignMetrics']['asinInfo']['asinImage'] ?? null);
            $this->setAsinInfoTwisterVariationsV2($metricItemArray['campaignMetrics']['asinInfo']['twisterVariationsV2'] ?? null);
        }

        return $this;
    }

    /*******************************************
     *** UpdatableRecordTrait implementation ***
     *******************************************/

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'requestId', nullable: true)]
    protected ?ApiRequestCrawlAsc $createdFromRequest = null;

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'updateRequestId', nullable: true)]
    protected ?ApiRequestCrawlAsc $updatedFromRequest = null;

    public function __toString(): string
    {
        return "CcCampMetric for {$this->getCampaign()->getAzCampaignId()} ~ {$this->getMetricKey()} on {$this->getReportDate()->format('Y-m-d')}";
    }

    public function getId(): int
    {
        return $this->id ?? 0;
    }

    public function setCreatedFromRequest(?ApiRequestCrawlAsc $createdFromRequest): static
    {
        $this->createdFromRequest = $createdFromRequest;

        return $this->setReportDate(GravitiqTools::castToDateTimeImmutable($createdFromRequest->getRequestDate()));
    }

    public function setUpdatedFromRequest(?ApiRequestCrawlAsc $updatedFromRequest): static
    {
        $this->updatedFromRequest = $updatedFromRequest;

        return $this->setReportDate(GravitiqTools::castToDateTimeImmutable($updatedFromRequest->getRequestDate()));
    }

    /**
     * @param self $record
     * @return bool
     */
    public function matchesRecord($record): bool
    {
        if ($record->getCampaign()?->getAzCampaignId() !== $this->getCampaign()?->getAzCampaignId()) {
            return false;
        }
        if ($record->getMetricKey() !== $this->getMetricKey()) {
            return false;
        }
        if ($record->getReportDate()->format('Y-m-d') !== $this->getReportDate()->format('Y-m-d')) {
            return false;
        }
        return true;
    }

    /**
     * @return list<string>
     */
    protected function getSettersExcludedFromUpdate(): array
    {
        $excluded = $this->updatableGetSettersExcludedFromUpdate();
        $excluded[] = 'setCampaign';
        return $excluded;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(inversedBy: 'metrics')]
    #[ORM\JoinColumn(nullable: false)]
    protected ?AscCreatorConnectionsCampaign $campaign = null;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    protected ?int $id = null;

    #[ORM\Column(type: Types::DATE_IMMUTABLE, nullable: false)]
    protected \DateTimeImmutable $reportDate;

    #[ORM\Column(type: Types::STRING, length: 50)]
    protected string $metricKey;

    #[ORM\Column(type: Types::STRING, length: 50)]
    protected string $metricType;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $metricsAvailable = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $spend = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $sales = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $clicks = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $orders = null;

    #[ORM\Column(type: Types::STRING, length: 3, nullable: true)]
    protected ?string $currencyCode = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $salesInCurrencyAmount = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $earningsAmount = null;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: true)]
    protected ?string $asinInfoAsin = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $asinInfoPrice = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $asinInfoTitle = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    protected ?float $asinInfoNumberOfStars = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $asinInfoNumberOfReviews = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $asinInfoMaxPrice = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $asinInfoMinPrice = null;

    #[ORM\Column(type: Types::SMALLINT, nullable: true)]
    protected ?int $asinInfoFullStars = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $asinInfoHalfStar = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $asinInfoParent = null;

    /**
     * @var array<string, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $asinInfoImage = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $asinInfoTwisterVariationsV2 = null;

    /*******************************
     *** Modified generated code ***
     ******************************/

    /**
     * @return array<string, mixed>|null
     */
    public function getAsinInfoImage(): ?array
    {
        return $this->asinInfoImage;
    }

    /**
     * @param array<string, mixed>|null $asinInfoImage
     * @return $this
     */
    public function setAsinInfoImage(?array $asinInfoImage): static
    {
        $this->asinInfoImage = $asinInfoImage;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getAsinInfoTwisterVariationsV2(): ?array
    {
        return $this->asinInfoTwisterVariationsV2;
    }

    /**
     * @param array<int, mixed>|null $asinInfoTwisterVariationsV2
     * @return $this
     */
    public function setAsinInfoTwisterVariationsV2(?array $asinInfoTwisterVariationsV2): static
    {
        $this->asinInfoTwisterVariationsV2 = $asinInfoTwisterVariationsV2;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getReportDate(): ?\DateTimeImmutable
    {
        return $this->reportDate;
    }

    public function setReportDate(\DateTimeImmutable $reportDate): static
    {
        $this->reportDate = $reportDate;

        return $this;
    }

    public function getMetricKey(): ?string
    {
        return $this->metricKey;
    }

    public function setMetricKey(string $metricKey): static
    {
        $this->metricKey = $metricKey;

        return $this;
    }

    public function getMetricType(): ?string
    {
        return $this->metricType;
    }

    public function setMetricType(string $metricType): static
    {
        $this->metricType = $metricType;

        return $this;
    }

    public function isMetricsAvailable(): ?bool
    {
        return $this->metricsAvailable;
    }

    public function setMetricsAvailable(?bool $metricsAvailable): static
    {
        $this->metricsAvailable = $metricsAvailable;

        return $this;
    }

    public function getSpend(): ?int
    {
        return $this->spend;
    }

    public function setSpend(?int $spend): static
    {
        $this->spend = $spend;

        return $this;
    }

    public function getSales(): ?int
    {
        return $this->sales;
    }

    public function setSales(?int $sales): static
    {
        $this->sales = $sales;

        return $this;
    }

    public function getClicks(): ?int
    {
        return $this->clicks;
    }

    public function setClicks(?int $clicks): static
    {
        $this->clicks = $clicks;

        return $this;
    }

    public function getOrders(): ?int
    {
        return $this->orders;
    }

    public function setOrders(?int $orders): static
    {
        $this->orders = $orders;

        return $this;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(?string $currencyCode): static
    {
        $this->currencyCode = $currencyCode;

        return $this;
    }

    public function getSalesInCurrencyAmount(): ?int
    {
        return $this->salesInCurrencyAmount;
    }

    public function setSalesInCurrencyAmount(?int $salesInCurrencyAmount): static
    {
        $this->salesInCurrencyAmount = $salesInCurrencyAmount;

        return $this;
    }

    public function getEarningsAmount(): ?int
    {
        return $this->earningsAmount;
    }

    public function setEarningsAmount(?int $earningsAmount): static
    {
        $this->earningsAmount = $earningsAmount;

        return $this;
    }

    public function getAsinInfoAsin(): ?string
    {
        return $this->asinInfoAsin;
    }

    public function setAsinInfoAsin(?string $asinInfoAsin): static
    {
        $this->asinInfoAsin = $asinInfoAsin;

        return $this;
    }

    public function getAsinInfoPrice(): ?int
    {
        return $this->asinInfoPrice;
    }

    public function setAsinInfoPrice(?int $asinInfoPrice): static
    {
        $this->asinInfoPrice = $asinInfoPrice;

        return $this;
    }

    public function getAsinInfoTitle(): ?string
    {
        return $this->asinInfoTitle;
    }

    public function setAsinInfoTitle(?string $asinInfoTitle): static
    {
        $this->asinInfoTitle = $asinInfoTitle;

        return $this;
    }

    public function getAsinInfoNumberOfStars(): ?float
    {
        return $this->asinInfoNumberOfStars;
    }

    public function setAsinInfoNumberOfStars(?float $asinInfoNumberOfStars): static
    {
        $this->asinInfoNumberOfStars = $asinInfoNumberOfStars;

        return $this;
    }

    public function getAsinInfoNumberOfReviews(): ?int
    {
        return $this->asinInfoNumberOfReviews;
    }

    public function setAsinInfoNumberOfReviews(?int $asinInfoNumberOfReviews): static
    {
        $this->asinInfoNumberOfReviews = $asinInfoNumberOfReviews;

        return $this;
    }

    public function getAsinInfoMaxPrice(): ?int
    {
        return $this->asinInfoMaxPrice;
    }

    public function setAsinInfoMaxPrice(?int $asinInfoMaxPrice): static
    {
        $this->asinInfoMaxPrice = $asinInfoMaxPrice;

        return $this;
    }

    public function getAsinInfoMinPrice(): ?int
    {
        return $this->asinInfoMinPrice;
    }

    public function setAsinInfoMinPrice(?int $asinInfoMinPrice): static
    {
        $this->asinInfoMinPrice = $asinInfoMinPrice;

        return $this;
    }

    public function getAsinInfoFullStars(): ?int
    {
        return $this->asinInfoFullStars;
    }

    public function setAsinInfoFullStars(?int $asinInfoFullStars): static
    {
        $this->asinInfoFullStars = $asinInfoFullStars;

        return $this;
    }

    public function isAsinInfoHalfStar(): ?bool
    {
        return $this->asinInfoHalfStar;
    }

    public function setAsinInfoHalfStar(?bool $asinInfoHalfStar): static
    {
        $this->asinInfoHalfStar = $asinInfoHalfStar;

        return $this;
    }

    public function isAsinInfoParent(): ?bool
    {
        return $this->asinInfoParent;
    }

    public function setAsinInfoParent(?bool $asinInfoParent): static
    {
        $this->asinInfoParent = $asinInfoParent;

        return $this;
    }

    public function getCreatedFromRequest(): ?ApiRequestCrawlAsc
    {
        return $this->createdFromRequest;
    }

    public function getUpdatedFromRequest(): ?ApiRequestCrawlAsc
    {
        return $this->updatedFromRequest;
    }

    public function getCampaign(): ?AscCreatorConnectionsCampaign
    {
        return $this->campaign;
    }

    public function setCampaign(?AscCreatorConnectionsCampaign $campaign): static
    {
        $this->campaign = $campaign;

        return $this;
    }
}
