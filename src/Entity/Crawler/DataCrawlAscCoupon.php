<?php

namespace App\Entity\Crawler;

use App\Domain\AmazonSpaPool;
use App\Entity\AccountProfileSpa;
use App\Entity\GravitiqBaseEntity;
use App\Entity\MagicMutatorTrait;
use App\Entity\UpdatableRecordTrait;
use App\Repository\Crawler\DataCrawlAscCouponRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method float getBudgetAsDecimal()
 * @method float getDiscountValueAsDecimal()
 * @method null|\DateTime getStartDateOrNullIfNotSet()
 * @method null|\DateTime getEndDateOrNullIfNotSet()
 * @method null|string getDiscountTypeOrNullIfNotSet()
 * @method null|int getDiscountValueOrNullIfNotSet()
 * @method null|int getBudgetOrNullIfNotSet()
 * @method null|string getTitleOrNullIfNotSet()
 * @method null|bool isOncePerCustomerOrNullIfNotSet()
 * @method null|string getPromotionIdOrNullIfNotSet()
 * @method self setBudgetFromDecimal(mixed $cost)
 * @method self setBudgetSpentFromDecimal(mixed $cost)
 * @method self setDiscountValueFromDecimal(mixed $cost)
 * @method self setEndDateFromString(null|string|\DateTimeInterface $endDate)
 * @method self setSalesFromDecimal(mixed $cost)
 * @method self setStartDateFromString(null|string|\DateTimeInterface $startDate)
 */
#[ORM\Table(name: 'data_crawl_asc_coupon')]
#[ORM\Entity(repositoryClass: DataCrawlAscCouponRepository::class)]
#[ORM\Index(columns: ['promotionId'], name: 'IDX_PROMOTION_ID')]
class DataCrawlAscCoupon extends GravitiqBaseEntity
{
    /** @use UpdatableRecordTrait<ApiRequestCrawlAsc> */
    use UpdatableRecordTrait, MagicMutatorTrait;

    public const string COUPON_STATUS_RUNNING = 'RUNNING';

    public const string DISCOUNT_TYPE_PERCENT = 'PERCENT';
    public const string DISCOUNT_TYPE_MONEY = 'MONEY';

    public const int MAX_COUPON_LENGTH_DAYS = 30;
    public const int MIN_BUDGET_CENTS = 10000;

    public function isRunning(): bool
    {
        return $this->getStatus() == self::COUPON_STATUS_RUNNING;
    }

    public function getConcatenatedIdAndTitle(): string
    {
        return "#{$this->getId()} {$this->getTitle()}";
    }

    public function getBudgetForAmazonForm(): float
    {
        return $this->getBudgetAsDecimal();
    }

    public function getDiscountValueForAmazonForm(): float
    {
        if ($this->discountType === self::DISCOUNT_TYPE_PERCENT) {
            return floor($this->getDiscountValueAsDecimal());
        } elseif ($this->discountType === self::DISCOUNT_TYPE_MONEY) {
            return $this->getDiscountValueAsDecimal();
        } else {
            throw new \InvalidArgumentException("Invalid discount type {$this->discountType}");
        }
    }

    public function setEndDateLimitedBy(?\DateTimeInterface $endDate, ?\DateTimeInterface $maxEndDate): static
    {
        if (is_null($endDate)) {
            try {
                $maxCouponLength = 'P' . DataCrawlAscCoupon::MAX_COUPON_LENGTH_DAYS . 'D';
                $dateIntervalForMaxCouponLength = new \DateInterval($maxCouponLength);
            } catch (\DateMalformedIntervalStringException $e) { /** @phpstan-ignore-line */
                throw new \InvalidArgumentException("Invalid max coupon length: $maxCouponLength:" . $e->getMessage());
            }
            $endDate = (clone $this->getStartDate())->add($dateIntervalForMaxCouponLength);
        } else {
            $endDate = GravitiqTools::castToDateTime($endDate);
        }

        if (!is_null($maxEndDate)) {
            $endDate = min($endDate, $maxEndDate);
        }

        return $this->setEndDate($endDate);
    }

    public function createCloneForDates(\DateTimeInterface|string $startDate, \DateTimeInterface|string $endDate): DataCrawlAscCoupon
    {
        $couponEntity = clone $this;
        $couponEntity
            ->setStartDateFromString($startDate)
            ->setEndDateFromString($endDate)
            ->setBudgetBasedOnDailySpend($this->getBudgetPerDay())
        ;
        return $couponEntity;
    }

    public function getBudgetPerDay(): int
    {
        $numberOfDays = GravitiqTools::daysBetweenDates($this->getStartDate(), $this->getEndDate()) + 1;
        return (int)round($this->budget / $numberOfDays);
    }

    public function setBudgetBasedOnDailySpend(int $dailySpend): static
    {
        $numberOfDays = GravitiqTools::daysBetweenDates($this->getStartDate(), $this->getEndDate()) + 1;
        $totalBudget = max(self::MIN_BUDGET_CENTS, $dailySpend * $numberOfDays);

        return $this->setBudget($totalBudget);
    }

    /*******************************************
     *** UpdatableRecordTrait implementation ***
     *******************************************/

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'requestId', nullable: false)]
    protected ?ApiRequestCrawlAsc $createdFromRequest;

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'updateRequestId', nullable: true)]
    protected ?ApiRequestCrawlAsc $updatedFromRequest = null;

    #[ORM\ManyToOne(targetEntity: AccountProfileSpa::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected AccountProfileSpa $spaProfile;

    public function __toString(): string
    {
        return "coupon:{$this->getPromotionId()}";
    }

    public function getId(): int {
        return $this->id ?? 0;
    }

    public function getSpaProfile(): ?AccountProfileSpa
    {
        return $this->spaProfile;
    }

    public function setSpaProfile(?AccountProfileSpa $spaProfile): static
    {
        $this->spaProfile = $spaProfile;

        return $this;
    }

    /**
     * @return list<string>
     */
    protected function getSettersExcludedFromUpdate(): array
    {
        return ['setCreatedFromRequest','setUpdatedFromRequest','setCreatedAt','setUpdatedAt','setReportDate'];
    }

    /**
     * @param self $record
     * @return bool
     */
    public function matchesRecord($record): bool
    {
        if ($record->getPromotionId() !== $this->getPromotionId()) {
            return false;
        }
        if ($record->getReportDate()->format('Y-m-d') !== $this->getReportDate()->format('Y-m-d')) {
            return false;
        }
        if ($record->getSpaProfile()->getId() !== $this->getSpaProfile()->getId()) {
            return false;
        }
        return true;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $reportDate = null;

    #[ORM\Column(type: Types::STRING, length: 32)]
    protected string $customerId;

    #[ORM\Column(type: Types::STRING, length: 2)]
    protected string $countryCode;

    #[ORM\Column(type: Types::STRING, length: 36)]
    protected string $promotionId;

    #[ORM\Column(type: Types::STRING, length: 255)]
    protected string $title;

    /**
     * @var array<string>|null
     */
    #[ORM\Column(type: Types::SIMPLE_ARRAY, nullable: true)]
    protected ?array $asins = null;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $asinCount;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $budget;

    #[ORM\Column(type: Types::STRING, length: 15)]
    protected string $budgetType;

    #[ORM\Column(type: Types::STRING, length: 15)]
    protected string $discountType;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $discountValue;

    #[ORM\Column(type: Types::STRING, length: 50)]
    protected string $customerSegment;

    #[ORM\Column(type: Types::STRING, length: 30)]
    protected string $couponType;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $sales = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $clipCount = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $budgetSpent = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $redemptionCount = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $budgetUtilization = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    protected bool $oncePerCustomer = false;

    #[ORM\Column(type: Types::STRING, length: 15)]
    protected string $status;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    protected \DateTime $startDate;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    protected \DateTime $endDate;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $promotionSetupServiceId = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    protected bool $needsAttention = false;

    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    protected ?string $budgetStatus = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $combinability = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    protected ?string $psssStatus = null;

    /**
     * @var array<string, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $tailoringConfiguration = null;

    /*******************************
     *** Modified generated code ***
     ******************************/

    public function setCountryCode(string $countryCode): static
    {
        if (array_key_exists($countryCode, AmazonSpaPool::MARKETPLACE)) {
            $this->countryCode = $countryCode;
        } else {
            $parts = explode('.', $countryCode);
            $this->countryCode = AmazonSpaPool::convertMarketplaceIdToMarketplaceCode(end($parts));
        }

        return $this;
    }

    public function getStartDate(): ?\DateTime
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface|string $startDate): static
    {
        $this->startDate = GravitiqTools::castToDateTime($startDate);

        return $this;
    }

    public function getEndDate(): ?\DateTime
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeInterface|string $endDate): static
    {
        $this->endDate = GravitiqTools::castToDateTime($endDate);

        return $this;
    }

    /**
     * @return array<string, mixed>|null
     */
    public function getTailoringConfiguration(): ?array
    {
        return $this->tailoringConfiguration;
    }

    /**
     * @param array<string, mixed>|null $tailoringConfiguration
     * @return $this
     */
    public function setTailoringConfiguration(?array $tailoringConfiguration): static
    {
        $this->tailoringConfiguration = $tailoringConfiguration;

        return $this;
    }

    public function getOncePerCustomer(): ?bool
    {
        return $this->isOncePerCustomer();
    }

    public function getNeedsAttention(): ?bool
    {
        return $this->isNeedsAttention();
    }

    /**
     * @return string[]|null
     */
    public function getAsins(): ?array
    {
        return $this->asins;
    }

    /**
     * @param ?list<string> $asins
     * @return $this
     */
    public function setAsins(?array $asins): static
    {
        if (is_null($asins)) {
            $this->asins = null;
            return $this;
        }

        $asins = array_map('strtoupper', $asins);
        sort($asins);
        $this->asins = $asins;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): static
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function getPromotionId(): ?string
    {
        return $this->promotionId;
    }

    public function setPromotionId(string $promotionId): static
    {
        $this->promotionId = $promotionId;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getAsinCount(): ?int
    {
        return $this->asinCount;
    }

    public function setAsinCount(int $asinCount): static
    {
        $this->asinCount = $asinCount;

        return $this;
    }

    public function getBudgetType(): ?string
    {
        return $this->budgetType;
    }

    public function setBudgetType(string $budgetType): static
    {
        $this->budgetType = $budgetType;

        return $this;
    }

    public function getDiscountType(): ?string
    {
        return $this->discountType;
    }

    public function setDiscountType(string $discountType): static
    {
        $this->discountType = $discountType;

        return $this;
    }

    public function getCustomerSegment(): ?string
    {
        return $this->customerSegment;
    }

    public function setCustomerSegment(string $customerSegment): static
    {
        $this->customerSegment = $customerSegment;

        return $this;
    }

    public function getCouponType(): ?string
    {
        return $this->couponType;
    }

    public function setCouponType(string $couponType): static
    {
        $this->couponType = $couponType;

        return $this;
    }

    public function isOncePerCustomer(): ?bool
    {
        return $this->oncePerCustomer;
    }

    public function setOncePerCustomer(bool $oncePerCustomer): static
    {
        $this->oncePerCustomer = $oncePerCustomer;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getPromotionSetupServiceId(): ?string
    {
        return $this->promotionSetupServiceId;
    }

    public function setPromotionSetupServiceId(?string $promotionSetupServiceId): static
    {
        $this->promotionSetupServiceId = $promotionSetupServiceId;

        return $this;
    }

    public function isNeedsAttention(): ?bool
    {
        return $this->needsAttention;
    }

    public function setNeedsAttention(bool $needsAttention): static
    {
        $this->needsAttention = $needsAttention;

        return $this;
    }

    public function getBudgetStatus(): ?string
    {
        return $this->budgetStatus;
    }

    public function setBudgetStatus(?string $budgetStatus): static
    {
        $this->budgetStatus = $budgetStatus;

        return $this;
    }

    public function getSales(): ?int
    {
        return $this->sales;
    }

    public function setSales(?int $sales): static
    {
        $this->sales = $sales;

        return $this;
    }

    public function getClipCount(): ?int
    {
        return $this->clipCount;
    }

    public function setClipCount(?int $clipCount): static
    {
        $this->clipCount = $clipCount;

        return $this;
    }

    public function getBudgetSpent(): ?int
    {
        return $this->budgetSpent;
    }

    public function setBudgetSpent(?int $budgetSpent): static
    {
        $this->budgetSpent = $budgetSpent;

        return $this;
    }

    public function getRedemptionCount(): ?int
    {
        return $this->redemptionCount;
    }

    public function setRedemptionCount(?int $redemptionCount): static
    {
        $this->redemptionCount = $redemptionCount;

        return $this;
    }

    public function getBudgetUtilization(): ?int
    {
        return $this->budgetUtilization;
    }

    public function setBudgetUtilization(?int $budgetUtilization): static
    {
        $this->budgetUtilization = $budgetUtilization;

        return $this;
    }

    public function getCombinability(): ?string
    {
        return $this->combinability;
    }

    public function setCombinability(?string $combinability): static
    {
        $this->combinability = $combinability;

        return $this;
    }

    public function getPsssStatus(): ?string
    {
        return $this->psssStatus;
    }

    public function setPsssStatus(?string $psssStatus): static
    {
        $this->psssStatus = $psssStatus;

        return $this;
    }

    public function getBudget(): ?int
    {
        return $this->budget;
    }

    public function setBudget(int $budget): static
    {
        $this->budget = $budget;

        return $this;
    }

    public function getDiscountValue(): ?int
    {
        return $this->discountValue;
    }

    public function setDiscountValue(int $discountValue): static
    {
        $this->discountValue = $discountValue;

        return $this;
    }

    public function getReportDate(): ?\DateTimeInterface
    {
        return $this->reportDate;
    }

    public function setReportDate(?\DateTimeInterface $reportDate): static
    {
        $this->reportDate = $reportDate;

        return $this;
    }
}
