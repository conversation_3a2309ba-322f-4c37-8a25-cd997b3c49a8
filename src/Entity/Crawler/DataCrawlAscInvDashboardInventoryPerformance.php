<?php

namespace App\Entity\Crawler;

use App\Domain\AmazonSpaPool;
use App\Entity\AccountProfileSpa;
use App\Entity\GravitiqBaseEntity;
use App\Entity\MagicMutatorTrait;
use App\Entity\UpdatableRecordTrait;
use App\Repository\Crawler\DataCrawlAscInvDashboardInventoryPerformanceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method static setSnapshotDateFromString(string|float|int|null $value) - via MagicMutatorTrait
 * @method static setOverSizeSnapShotDateFromString(string|float|int|null $value) - via MagicMutatorTrait
 */
#[ORM\Table(name: 'data_crawl_asc_inv_dash_ipi')]
#[ORM\Index(columns: ['countryCode'], name: 'IDX_COUNTRY')]
#[ORM\Entity(repositoryClass: DataCrawlAscInvDashboardInventoryPerformanceRepository::class)]
class DataCrawlAscInvDashboardInventoryPerformance extends GravitiqBaseEntity
{
    /** @use UpdatableRecordTrait<ApiRequestCrawlAsc> */
    use MagicMutatorTrait, UpdatableRecordTrait {
        UpdatableRecordTrait::updateFromObject as private updatableUpdateFromObject;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setPlanInventoryFromArray(array $itemArray): static
    {
        $this->setCountryCode($itemArray['encryptedMarketplaceId']);
        $this->setInventoryPerformanceIndexFromArray($itemArray['inventoryPerformanceIndex']);
        $this->setExcessInventoryFromArray($itemArray['excessInventory']);
        $this->setInStockInventoryFromArray($itemArray['inStockInventory']);
        $this->setSellThroughFromArray($itemArray['sellThrough']);
        $this->setStrandedInventoryFromArray($itemArray['strandedInventory']);

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setInventoryPerformanceIndexFromArray(array $itemArray): static
    {
        $this->setIpiWeekNum($itemArray['weekCardinalNum']);
        $this->setIpiYear($itemArray['yearCardinalNum']);
        $this->setIpiScore($itemArray['score']);
        $this->setIpiTrend($itemArray['trend']);
        $this->setIpiThreshold($itemArray['threshold']);
        $this->setIpiHistory($itemArray['history']);

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setExcessInventoryFromArray(array $itemArray): static
    {
        $this->setExcessInvRate($itemArray['rate']);
        $this->setExcessInvSkuCount($itemArray['skuCount']);
        $this->setExcessInvUnitCount($itemArray['unitCount']);
        $this->setExcessInvRanges($itemArray['ranges']);
        $this->setExcessInvEstStorageCostAmount($itemArray['estStorageCost']['amount'] ?? null);
        $this->setExcessInvEstStorageCostCurrencyCode($itemArray['estStorageCost']['currencyCode'] ?? null);
        $this->setExcessInvRatingName($this->getRatingName($itemArray['rate'], $itemArray['ranges']));

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setInStockInventoryFromArray(array $itemArray): static
    {
        $this->setInStockInvRate($itemArray['rate']);
        $this->setInStockInvSkuCount($itemArray['skuCount']);
        $this->setInStockInvRanges($itemArray['ranges']);
        $this->setInStockInvEstLostSalesAmount($itemArray['estLostSales']['amount'] ?? null);
        $this->setInStockInvEstLostSalesCurrencyCode($itemArray['estLostSales']['currencyCode'] ?? null);
        $this->setInStockInvRatingName($this->getRatingName($itemArray['rate'], $itemArray['ranges']));

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setSellThroughFromArray(array $itemArray): static
    {
        $this->setSellThroughRate($itemArray['rate']);
        $this->setSellThroughRanges($itemArray['ranges']);
        $this->setSellThroughUnitCount($itemArray['unitCount']);
        $this->setSellThroughRatingName($this->getRatingName($itemArray['rate'], $itemArray['ranges']));

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setStrandedInventoryFromArray(array $itemArray): static
    {
        $this->setStrandedInvRate($itemArray['rate']);
        $this->setStrandedInvSkuCount($itemArray['skuCount']);
        $this->setStrandedInvUnitCount($itemArray['unitCount']);
        $this->setStrandedInvRanges($itemArray['ranges']);
        $this->setStrandedInvRatingName($this->getRatingName($itemArray['rate'], $itemArray['ranges']));

        return $this;
    }

    /**
     * @param array<string, mixed> $itemArray
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     */
    public function setStorageUtilizationFromArray(array $itemArray): static
    {
        $this->setStdSizeUtilizationRatio($itemArray['sortableUtilizationRatio'] ?? null);
        $this->setStdSizeAvgInventoryVolume($itemArray['sortableAvgInventoryVolume'] ?? null);
        $this->setStdSizeAvgShippedVolume($itemArray['sortableAvgShippedVolume'] ?? null);
        $this->setSnapshotDateFromString($itemArray['snapshotDate'] ?? $itemArray['sortableSnapShotDate'] ?? null);
        $this->setOverSizeUtilizationRatio($itemArray['nonSortableUtilizationRatio'] ?? null);
        $this->setOverSizeAvgInventoryVolume($itemArray['nonSortableAvgInventoryVolume'] ?? null);
        $this->setOverSizeAvgShippedVolume($itemArray['nonSortableAvgShippedVolume'] ?? null);
        $this->setOverSizeSnapShotDateFromString($itemArray['nonSortableSnapShotDate'] ?? null);
        $this->setVolumeUnit($itemArray['volumeUnit'] ?? null);
        $this->setSellerTier($itemArray['sellerTier'] ?? null);
        $this->setSellerAgeInDay($itemArray['sellerAgeInDay'] ?? null);

        return $this;
    }

    /**
     * @param array<string, mixed> $sourceData
     * @return $this
     * @noinspection PhpUnused - called via ApiRequestCrawl::getArrayParseFieldMapping()
     * @throws \Exception
     */
    public function setCapacityMonitorsFromArray(array $sourceData): static
    {
        foreach ($sourceData['storageTypeToUsageAndLimitBreakdownMap'] as $storageType => $itemArray) {
            $newCapacityMonitor = (new DataCrawlAscInvDashboardCapacityMonitor())->createInvDashboardCapacityMonitorFromArray($storageType, $itemArray);
            $newCapacityMonitor->setSnapshotDateFromTimestamp($sourceData['timestamp']);
            $this->addCapacityMonitor($newCapacityMonitor);
        }

        return $this;
    }

    /**
     * @param ?float $rate
     * @param array<int, array{ratingName: string, low: float, high: float}> $ranges
     * @return string
     */
    public function getRatingName(?float $rate, array $ranges): string
    {
        $ratingName = "NOT RATED";

        if (is_null($rate)) {
            return $ratingName;
        }

        foreach ($ranges as $range) {
            if ($rate >= $range['low'] && $rate <= $range['high']) {
                $ratingName = $range['ratingName'];
                break;
            }
        }

        return $ratingName;
    }

    /*******************************************
     *** UpdatableRecordTrait implementation ***
     *******************************************/

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'requestId', nullable: false)]
    protected ?ApiRequestCrawlAsc $createdFromRequest;

    #[ORM\ManyToOne(targetEntity: ApiRequestCrawlAsc::class)]
    #[ORM\JoinColumn(name: 'updateRequestId', nullable: true)]
    protected ?ApiRequestCrawlAsc $updatedFromRequest = null;

    #[ORM\ManyToOne(targetEntity: AccountProfileSpa::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected AccountProfileSpa $spaProfile;

    public function __toString(): string
    {
        return "inv-dashboard:{$this->getCountryCode()}";
    }

    public function getId(): int {
        return $this->id ?? 0;
    }

    public function getSpaProfile(): ?AccountProfileSpa
    {
        return $this->spaProfile;
    }

    public function setSpaProfile(?AccountProfileSpa $spaProfile): static
    {
        $this->spaProfile = $spaProfile;

        return $this;
    }

    /**
     * @param self $record
     * @return bool
     */
    public function matchesRecord($record): bool
    {
        if ($record->getCountryCode() !== $this->getCountryCode()) {
            return false;
        }
        if ($record->getSnapshotDate() !== $this->getSnapshotDate()) {
            return false;
        }
        if ($record->getSpaProfile()->getId() !== $this->getSpaProfile()->getId()) {
            return false;
        }
        return true;
    }

    public function updateFromObject(DataCrawlAscInvDashboardInventoryPerformance $record): void
    {
        $this->updatableUpdateFromObject($record);

        $this->updateCapacityMonitorsFromInventory($record);
    }

    public function updateCapacityMonitorsFromInventory(DataCrawlAscInvDashboardInventoryPerformance $record): void {
        $recordStorageTypes = [];
        foreach ($record->getCapacityMonitors() as $newCapacityMonitor) {
            $recordStorageTypes[] = $newCapacityMonitor->getStorageType();
            if ($this->alreadyContainsCapacityMonitor($newCapacityMonitor)) {
                $existingCapacityMonitor = $this->getExistingCapacityMonitor($newCapacityMonitor);
                $existingCapacityMonitor->updateFromNewInvDashboardCapacityMonitor($newCapacityMonitor);
            } else {
                $this->addCapacityMonitor($newCapacityMonitor);
            }
        }

        foreach ($this->getCapacityMonitors() as $existingCapacityMonitor) {
            if (!in_array($existingCapacityMonitor->getStorageType(), $recordStorageTypes)) {
                $this->removeCapacityMonitor($existingCapacityMonitor);
            }
        }
    }

    public function alreadyContainsCapacityMonitor(DataCrawlAscInvDashboardCapacityMonitor $newCapacityMonitor): bool
    {
        foreach ($this->getCapacityMonitors() as $existingCapacityMonitor) {
            if ($existingCapacityMonitor->getStorageType() === $newCapacityMonitor->getStorageType()) {
                return true;
            }
        }

        return false;
    }

    public function getExistingCapacityMonitor(DataCrawlAscInvDashboardCapacityMonitor $capacityMonitor): ?DataCrawlAscInvDashboardCapacityMonitor
    {
        foreach ($this->getCapacityMonitors() as $existingCapacityMonitor) {
            if ($existingCapacityMonitor->getStorageType() === $capacityMonitor->getStorageType()) {
                return $existingCapacityMonitor;
            }
        }

        return null;
    }

    /*************************
     *** Field definitions ***
     *************************/

    /**
     * @var Collection<integer, DataCrawlAscInvDashboardCapacityMonitor>
     */
    #[ORM\OneToMany(mappedBy: 'inventoryPerformance', targetEntity: DataCrawlAscInvDashboardCapacityMonitor::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    protected Collection $capacityMonitors;

    #[ORM\Column(type: Types::STRING, length: 2, nullable: true)]
    protected ?string $countryCode = null;

    /**********************************
     *** Storage Utilization Fields ***
     *********************************/

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $stdSizeUtilizationRatio = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $stdSizeAvgInventoryVolume = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $stdSizeAvgShippedVolume = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, length: 10, nullable: true)]
    protected ?\DateTime $snapshotDate = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $overSizeUtilizationRatio = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $overSizeAvgInventoryVolume = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $overSizeAvgShippedVolume = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, length: 10, nullable: true)]
    protected ?\DateTime $overSizeSnapShotDate = null;

    #[ORM\Column(type: Types::STRING, length: 30, nullable: true)]
    protected ?string $volumeUnit = null;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    protected ?string $sellerTier = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $sellerAgeInDay = null;

    /*****************************
     *** Plan Inventory Fields ***
     ****************************/

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $ipiWeekNum = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $ipiYear = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $ipiScore = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $ipiTrend = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $ipiThreshold = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $ipiHistory = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $excessInvRate = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $excessInvSkuCount = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $excessInvUnitCount = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $excessInvRatingName = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $excessInvRanges = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $excessInvEstStorageCostAmount = null;

    #[ORM\Column(type: Types::STRING, length: 3, nullable: true)]
    protected ?string $excessInvEstStorageCostCurrencyCode = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $inStockInvRate = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $inStockInvSkuCount = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $inStockInvRatingName = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $inStockInvRanges = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $inStockInvEstLostSalesAmount = null;

    #[ORM\Column(type: Types::STRING, length: 3, nullable: true)]
    protected ?string $inStockInvEstLostSalesCurrencyCode = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $sellThroughRate = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $sellThroughUnitCount = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $sellThroughRatingName = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $strandedInventoryRate = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $strandedInventoryUnitCount = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $strandedInventoryRatingName = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $sellThroughRanges = null;

    #[ORM\Column(type: Types::FLOAT, scale: 4, nullable: true)]
    protected ?float $strandedInvRate = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $strandedInvSkuCount = null;

    #[ORM\Column(type: Types::INTEGER, length: 8, nullable: true)]
    protected ?int $strandedInvUnitCount = null;

    #[ORM\Column(type: Types::STRING, length: 15, nullable: true)]
    protected ?string $strandedInvRatingName = null;

    /**
     * @var array<int, mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $strandedInvRanges = null;

    public function __construct()
    {
        $this->capacityMonitors = new ArrayCollection();
    }

    /*******************************
     *** Modified generated code ***
     ******************************/

    public function setCountryCode(?string $countryCode): static
    {
        if ($countryCode === null) {
            $this->countryCode = null;
        } else {
            if (array_key_exists($countryCode, AmazonSpaPool::MARKETPLACE)) {
                $this->countryCode = $countryCode;
            } else {
                $this->countryCode = AmazonSpaPool::convertMarketplaceIdToMarketplaceCode($countryCode);
            }
        }

        return $this;
    }

    public function setSnapshotDate(?\DateTime $snapshotDate): static
    {
        $this->snapshotDate = $snapshotDate;

        return $this;
    }

    public function setOverSizeSnapShotDate(?\DateTime $overSizeSnapShotDate): static
    {
        $this->overSizeSnapShotDate = $overSizeSnapShotDate;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getIpiHistory(): ?array
    {
        return $this->ipiHistory;
    }

    /**
     * @param array<int, mixed>|null $ipiHistory
     * @return $this
     */
    public function setIpiHistory(?array $ipiHistory): static
    {
        $this->ipiHistory = $ipiHistory;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getExcessInvRanges(): ?array
    {
        return $this->excessInvRanges;
    }

    /**
     * @param array<int, mixed>|null $excessInvRanges
     * @return $this
     */
    public function setExcessInvRanges(?array $excessInvRanges): static
    {
        $this->excessInvRanges = $excessInvRanges;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getInStockInvRanges(): ?array
    {
        return $this->inStockInvRanges;
    }

    /**
     * @param array<int, mixed>|null $inStockInvRanges
     * @return $this
     */
    public function setInStockInvRanges(?array $inStockInvRanges): static
    {
        $this->inStockInvRanges = $inStockInvRanges;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getSellThroughRanges(): ?array
    {
        return $this->sellThroughRanges;
    }

    /**
     * @param array<int, mixed>|null $sellThroughRanges
     * @return $this
     */
    public function setSellThroughRanges(?array $sellThroughRanges): static
    {
        $this->sellThroughRanges = $sellThroughRanges;

        return $this;
    }

    /**
     * @return array<int, mixed>|null
     */
    public function getStrandedInvRanges(): ?array
    {
        return $this->strandedInvRanges;
    }

    /**
     * @param array<int, mixed>|null $strandedInvRanges
     * @return $this
     */
    public function setStrandedInvRanges(?array $strandedInvRanges): static
    {
        $this->strandedInvRanges = $strandedInvRanges;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function getStdSizeUtilizationRatio(): ?float
    {
        return $this->stdSizeUtilizationRatio;
    }

    public function setStdSizeUtilizationRatio(?float $stdSizeUtilizationRatio): static
    {
        $this->stdSizeUtilizationRatio = $stdSizeUtilizationRatio;

        return $this;
    }

    public function getStdSizeAvgInventoryVolume(): ?float
    {
        return $this->stdSizeAvgInventoryVolume;
    }

    public function setStdSizeAvgInventoryVolume(?float $stdSizeAvgInventoryVolume): static
    {
        $this->stdSizeAvgInventoryVolume = $stdSizeAvgInventoryVolume;

        return $this;
    }

    public function getStdSizeAvgShippedVolume(): ?float
    {
        return $this->stdSizeAvgShippedVolume;
    }

    public function setStdSizeAvgShippedVolume(?float $stdSizeAvgShippedVolume): static
    {
        $this->stdSizeAvgShippedVolume = $stdSizeAvgShippedVolume;

        return $this;
    }

    public function getSnapshotDate(): ?\DateTimeInterface
    {
        return $this->snapshotDate;
    }

    public function getOverSizeUtilizationRatio(): ?float
    {
        return $this->overSizeUtilizationRatio;
    }

    public function setOverSizeUtilizationRatio(?float $overSizeUtilizationRatio): static
    {
        $this->overSizeUtilizationRatio = $overSizeUtilizationRatio;

        return $this;
    }

    public function getOverSizeAvgInventoryVolume(): ?float
    {
        return $this->overSizeAvgInventoryVolume;
    }

    public function setOverSizeAvgInventoryVolume(?float $overSizeAvgInventoryVolume): static
    {
        $this->overSizeAvgInventoryVolume = $overSizeAvgInventoryVolume;

        return $this;
    }

    public function getOverSizeAvgShippedVolume(): ?float
    {
        return $this->overSizeAvgShippedVolume;
    }

    public function setOverSizeAvgShippedVolume(?float $overSizeAvgShippedVolume): static
    {
        $this->overSizeAvgShippedVolume = $overSizeAvgShippedVolume;

        return $this;
    }

    public function getOverSizeSnapShotDate(): ?\DateTimeInterface
    {
        return $this->overSizeSnapShotDate;
    }

    public function getVolumeUnit(): ?string
    {
        return $this->volumeUnit;
    }

    public function setVolumeUnit(?string $volumeUnit): static
    {
        $this->volumeUnit = $volumeUnit;

        return $this;
    }

    public function getSellerTier(): ?string
    {
        return $this->sellerTier;
    }

    public function setSellerTier(?string $sellerTier): static
    {
        $this->sellerTier = $sellerTier;

        return $this;
    }

    public function getSellerAgeInDay(): ?int
    {
        return $this->sellerAgeInDay;
    }

    public function setSellerAgeInDay(?int $sellerAgeInDay): static
    {
        $this->sellerAgeInDay = $sellerAgeInDay;

        return $this;
    }

    public function getIpiWeekNum(): ?int
    {
        return $this->ipiWeekNum;
    }

    public function setIpiWeekNum(?int $ipiWeekNum): static
    {
        $this->ipiWeekNum = $ipiWeekNum;

        return $this;
    }

    public function getIpiYear(): ?int
    {
        return $this->ipiYear;
    }

    public function setIpiYear(?int $ipiYear): static
    {
        $this->ipiYear = $ipiYear;

        return $this;
    }

    public function getIpiScore(): ?int
    {
        return $this->ipiScore;
    }

    public function setIpiScore(?int $ipiScore): static
    {
        $this->ipiScore = $ipiScore;

        return $this;
    }

    public function getIpiTrend(): ?int
    {
        return $this->ipiTrend;
    }

    public function setIpiTrend(?int $ipiTrend): static
    {
        $this->ipiTrend = $ipiTrend;

        return $this;
    }

    public function getIpiThreshold(): ?int
    {
        return $this->ipiThreshold;
    }

    public function setIpiThreshold(?int $ipiThreshold): static
    {
        $this->ipiThreshold = $ipiThreshold;

        return $this;
    }

    public function getExcessInvRate(): ?float
    {
        return $this->excessInvRate;
    }

    public function setExcessInvRate(?float $excessInvRate): static
    {
        $this->excessInvRate = $excessInvRate;

        return $this;
    }

    public function getExcessInvSkuCount(): ?int
    {
        return $this->excessInvSkuCount;
    }

    public function setExcessInvSkuCount(?int $excessInvSkuCount): static
    {
        $this->excessInvSkuCount = $excessInvSkuCount;

        return $this;
    }

    public function getExcessInvUnitCount(): ?int
    {
        return $this->excessInvUnitCount;
    }

    public function setExcessInvUnitCount(?int $excessInvUnitCount): static
    {
        $this->excessInvUnitCount = $excessInvUnitCount;

        return $this;
    }

    public function getExcessInvRatingName(): ?string
    {
        return $this->excessInvRatingName;
    }

    public function setExcessInvRatingName(?string $excessInvRatingName): static
    {
        $this->excessInvRatingName = $excessInvRatingName;

        return $this;
    }

    public function getExcessInvEstStorageCostAmount(): ?float
    {
        return $this->excessInvEstStorageCostAmount;
    }

    public function setExcessInvEstStorageCostAmount(?float $excessInvEstStorageCostAmount): static
    {
        $this->excessInvEstStorageCostAmount = $excessInvEstStorageCostAmount;

        return $this;
    }

    public function getExcessInvEstStorageCostCurrencyCode(): ?string
    {
        return $this->excessInvEstStorageCostCurrencyCode;
    }

    public function setExcessInvEstStorageCostCurrencyCode(?string $excessInvEstStorageCostCurrencyCode): static
    {
        $this->excessInvEstStorageCostCurrencyCode = $excessInvEstStorageCostCurrencyCode;

        return $this;
    }

    public function getInStockInvRate(): ?float
    {
        return $this->inStockInvRate;
    }

    public function setInStockInvRate(?float $inStockInvRate): static
    {
        $this->inStockInvRate = $inStockInvRate;

        return $this;
    }

    public function getInStockInvSkuCount(): ?int
    {
        return $this->inStockInvSkuCount;
    }

    public function setInStockInvSkuCount(?int $inStockInvSkuCount): static
    {
        $this->inStockInvSkuCount = $inStockInvSkuCount;

        return $this;
    }

    public function getInStockInvRatingName(): ?string
    {
        return $this->inStockInvRatingName;
    }

    public function setInStockInvRatingName(?string $inStockInvRatingName): static
    {
        $this->inStockInvRatingName = $inStockInvRatingName;

        return $this;
    }

    public function getInStockInvEstLostSalesAmount(): ?float
    {
        return $this->inStockInvEstLostSalesAmount;
    }

    public function setInStockInvEstLostSalesAmount(?float $inStockInvEstLostSalesAmount): static
    {
        $this->inStockInvEstLostSalesAmount = $inStockInvEstLostSalesAmount;

        return $this;
    }

    public function getInStockInvEstLostSalesCurrencyCode(): ?string
    {
        return $this->inStockInvEstLostSalesCurrencyCode;
    }

    public function setInStockInvEstLostSalesCurrencyCode(?string $inStockInvEstLostSalesCurrencyCode): static
    {
        $this->inStockInvEstLostSalesCurrencyCode = $inStockInvEstLostSalesCurrencyCode;

        return $this;
    }

    public function getSellThroughRate(): ?int
    {
        return $this->sellThroughRate;
    }

    public function setSellThroughRate(?int $sellThroughRate): static
    {
        $this->sellThroughRate = $sellThroughRate;

        return $this;
    }

    public function getSellThroughUnitCount(): ?int
    {
        return $this->sellThroughUnitCount;
    }

    public function setSellThroughUnitCount(?int $sellThroughUnitCount): static
    {
        $this->sellThroughUnitCount = $sellThroughUnitCount;

        return $this;
    }

    public function getSellThroughRatingName(): ?string
    {
        return $this->sellThroughRatingName;
    }

    public function setSellThroughRatingName(?string $sellThroughRatingName): static
    {
        $this->sellThroughRatingName = $sellThroughRatingName;

        return $this;
    }

    public function getStrandedInventoryRate(): ?float
    {
        return $this->strandedInventoryRate;
    }

    public function setStrandedInventoryRate(?float $strandedInventoryRate): static
    {
        $this->strandedInventoryRate = $strandedInventoryRate;

        return $this;
    }

    public function getStrandedInventoryUnitCount(): ?int
    {
        return $this->strandedInventoryUnitCount;
    }

    public function setStrandedInventoryUnitCount(?int $strandedInventoryUnitCount): static
    {
        $this->strandedInventoryUnitCount = $strandedInventoryUnitCount;

        return $this;
    }

    public function getStrandedInventoryRatingName(): ?string
    {
        return $this->strandedInventoryRatingName;
    }

    public function setStrandedInventoryRatingName(?string $strandedInventoryRatingName): static
    {
        $this->strandedInventoryRatingName = $strandedInventoryRatingName;

        return $this;
    }

    public function getStrandedInvRate(): ?float
    {
        return $this->strandedInvRate;
    }

    public function setStrandedInvRate(?float $strandedInvRate): static
    {
        $this->strandedInvRate = $strandedInvRate;

        return $this;
    }

    public function getStrandedInvSkuCount(): ?int
    {
        return $this->strandedInvSkuCount;
    }

    public function setStrandedInvSkuCount(?int $strandedInvSkuCount): static
    {
        $this->strandedInvSkuCount = $strandedInvSkuCount;

        return $this;
    }

    public function getStrandedInvRatingName(): ?string
    {
        return $this->strandedInvRatingName;
    }

    public function setStrandedInvRatingName(?string $strandedInvRatingName): static
    {
        $this->strandedInvRatingName = $strandedInvRatingName;

        return $this;
    }

    /**
     * @return Collection<int, DataCrawlAscInvDashboardCapacityMonitor>
     */
    public function getCapacityMonitors(): Collection
    {
        return $this->capacityMonitors;
    }

    public function addCapacityMonitor(DataCrawlAscInvDashboardCapacityMonitor $capacityMonitor): static
    {
        if (!$this->capacityMonitors->contains($capacityMonitor)) {
            $this->capacityMonitors->add($capacityMonitor);
            $capacityMonitor->setInventoryPerformance($this);
        }

        return $this;
    }

    public function removeCapacityMonitor(DataCrawlAscInvDashboardCapacityMonitor $capacityMonitor): static
    {
        if ($this->capacityMonitors->removeElement($capacityMonitor)) {
            // set the owning side to null (unless already changed)
            if ($capacityMonitor->getInventoryPerformance() === $this) {
                $capacityMonitor->setInventoryPerformance(null);
            }
        }

        return $this;
    }

    public function getStrandedInvUnitCount(): ?int
    {
        return $this->strandedInvUnitCount;
    }

    public function setStrandedInvUnitCount(?int $strandedInvUnitCount): static
    {
        $this->strandedInvUnitCount = $strandedInvUnitCount;

        return $this;
    }
}
