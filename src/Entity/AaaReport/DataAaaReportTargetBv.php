<?php

namespace App\Entity\AaaReport;

use App\Repository\AaaReport\DataAaaReportTargetBvRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'data_aar_tar_bv')]
#[ORM\Index(columns: ['adGroupId'], name: 'IDX_ADGRP')]
#[ORM\Index(columns: ['campaignId'], name: 'IDX_CMPGN')]
#[ORM\Index(columns: ['targetId'], name: 'IDX_TARGT')]
#[ORM\Entity(repositoryClass: DataAaaReportTargetBvRepository::class)]
class DataAaaReportTargetBv extends DataAaaReportCommon
{
    public static function getUniquePerDayIdentifierFields(): array
    {
        return ['reportDate','targetId'];
    }

    // ############################################
    // #  Doctrine related code lives under here  #
    // ############################################
    #[ORM\Column(name: 'adGroupId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $adGroupId = null;

    #[ORM\Column(name: 'adGroupName', type: Types::STRING, nullable: true)]
    protected ?string $adGroupName = null;

    #[ORM\Column(name: 'attributedConversions14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions14d = null;

    #[ORM\Column(name: 'attributedConversions14dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions14dSameSKU = null;

    #[ORM\Column(name: 'attributedSales14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales14d = null;

    #[ORM\Column(name: 'attributedSales14dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales14dSameSKU = null;

    #[ORM\Column(name: 'campaignBudget', type: Types::INTEGER, nullable: true)]
    protected ?int $campaignBudget = null;

    #[ORM\Column(name: 'campaignBudgetType', type: Types::STRING, nullable: true)]
    protected ?string $campaignBudgetType = null;

    #[ORM\Column(name: 'campaignId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $campaignId = null;

    #[ORM\Column(name: 'campaignName', type: Types::STRING, nullable: true)]
    protected ?string $campaignName = null;

    #[ORM\Column(name: 'campaignStatus', type: Types::STRING, length: 64, nullable: true)]
    protected ?string $campaignStatus = null;

    #[ORM\Column(name: 'clicks', type: Types::INTEGER, nullable: true)]
    protected ?int $clicks = null;

    #[ORM\Column(name: 'cost', type: Types::INTEGER, nullable: true)]
    protected ?int $cost = null;

    #[ORM\Column(name: 'impressions', type: Types::INTEGER, nullable: true)]
    protected ?int $impressions = null;

    #[ORM\Column(name: 'targetId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $targetId = null;

    #[ORM\Column(name: 'targetingExpression', type: Types::STRING, nullable: true)]
    protected ?string $targetingExpression = null;

    #[ORM\Column(name: 'targetingText', type: Types::STRING, nullable: true)]
    protected ?string $targetingText = null;

    #[ORM\Column(name: 'targetingType', type: Types::STRING, nullable: true)]
    protected ?string $targetingType = null;

    #[ORM\Column(name: 'vctr', type: Types::FLOAT, nullable: true)]
    protected ?float $vctr = null;

    #[ORM\Column(name: 'video5SecondViewRate', type: Types::FLOAT, nullable: true)]
    protected ?float $video5SecondViewRate = null;

    #[ORM\Column(name: 'video5SecondViews', type: Types::INTEGER, nullable: true)]
    protected ?int $video5SecondViews = null;

    #[ORM\Column(name: 'videoCompleteViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoCompleteViews = null;

    #[ORM\Column(name: 'videoFirstQuartileViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoFirstQuartileViews = null;

    #[ORM\Column(name: 'videoMidpointViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoMidpointViews = null;

    #[ORM\Column(name: 'videoThirdQuartileViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoThirdQuartileViews = null;

    #[ORM\Column(name: 'videoUnmutes', type: Types::INTEGER, nullable: true)]
    protected ?int $videoUnmutes = null;

    #[ORM\Column(name: 'viewableImpressions', type: Types::INTEGER, nullable: true)]
    protected ?int $viewableImpressions = null;

    #[ORM\Column(name: 'vtr', type: Types::FLOAT, nullable: true)]
    protected ?float $vtr = null;

    #[ORM\Column(name: 'dpv14d', type: Types::INTEGER, nullable: true)]
    protected ?int $dpv14d = null;

    #[ORM\Column(name: 'attributedDetailPageViewsClicks14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedDetailPageViewsClicks14d = null;

    #[ORM\Column(name: 'attributedOrderRateNewToBrand14d', type: Types::FLOAT, nullable: true)]
    protected ?float $attributedOrderRateNewToBrand14d = null;

    #[ORM\Column(name: 'attributedOrdersNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedOrdersNewToBrand14d = null;

    #[ORM\Column(name: 'attributedOrdersNewToBrandPercentage14d', type: Types::FLOAT, nullable: true)]
    protected ?float $attributedOrdersNewToBrandPercentage14d = null;

    #[ORM\Column(name: 'attributedSalesNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSalesNewToBrand14d = null;

    #[ORM\Column(name: 'attributedSalesNewToBrandPercentage14d', type: Types::FLOAT, nullable: true)]
    protected ?float $attributedSalesNewToBrandPercentage14d = null;

    #[ORM\Column(name: 'attributedUnitsOrderedNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrderedNewToBrand14d = null;

    #[ORM\Column(name: 'attributedUnitsOrderedNewToBrandPercentage14d', type: Types::FLOAT, nullable: true)]
    protected ?float $attributedUnitsOrderedNewToBrandPercentage14d = null;

    #[ORM\Column(name: 'attributedBrandedSearches14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedBrandedSearches14d = null;

    #[ORM\Column(name: 'currency', type: Types::STRING, length: 3, nullable: true)]
    protected ?string $currency = null;

    #[ORM\Column(name: 'topOfSearchImpressionShare', type: Types::FLOAT, nullable: true)]
    protected ?float $topOfSearchImpressionShare = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getAdGroupId(): ?string
    {
        return $this->adGroupId;
    }

    public function setAdGroupId(?string $adGroupId): static
    {
        $this->adGroupId = $adGroupId;

        return $this;
    }

    public function getAdGroupName(): ?string
    {
        return $this->adGroupName;
    }

    public function setAdGroupName(?string $adGroupName): static
    {
        $this->adGroupName = $adGroupName;

        return $this;
    }

    public function getAttributedConversions14d(): ?int
    {
        return $this->attributedConversions14d;
    }

    public function setAttributedConversions14d(?int $attributedConversions14d): static
    {
        $this->attributedConversions14d = $attributedConversions14d;

        return $this;
    }

    public function getAttributedConversions14dSameSKU(): ?int
    {
        return $this->attributedConversions14dSameSKU;
    }

    public function setAttributedConversions14dSameSKU(?int $attributedConversions14dSameSKU): static
    {
        $this->attributedConversions14dSameSKU = $attributedConversions14dSameSKU;

        return $this;
    }

    public function getAttributedSales14d(): ?int
    {
        return $this->attributedSales14d;
    }

    public function setAttributedSales14d(?int $attributedSales14d): static
    {
        $this->attributedSales14d = $attributedSales14d;

        return $this;
    }

    public function getAttributedSales14dSameSKU(): ?int
    {
        return $this->attributedSales14dSameSKU;
    }

    public function setAttributedSales14dSameSKU(?int $attributedSales14dSameSKU): static
    {
        $this->attributedSales14dSameSKU = $attributedSales14dSameSKU;

        return $this;
    }

    public function getCampaignBudget(): ?int
    {
        return $this->campaignBudget;
    }

    public function setCampaignBudget(?int $campaignBudget): static
    {
        $this->campaignBudget = $campaignBudget;

        return $this;
    }

    public function getCampaignBudgetType(): ?string
    {
        return $this->campaignBudgetType;
    }

    public function setCampaignBudgetType(?string $campaignBudgetType): static
    {
        $this->campaignBudgetType = $campaignBudgetType;

        return $this;
    }

    public function getCampaignId(): ?string
    {
        return $this->campaignId;
    }

    public function setCampaignId(?string $campaignId): static
    {
        $this->campaignId = $campaignId;

        return $this;
    }

    public function getCampaignName(): ?string
    {
        return $this->campaignName;
    }

    public function setCampaignName(?string $campaignName): static
    {
        $this->campaignName = $campaignName;

        return $this;
    }

    public function getCampaignStatus(): ?string
    {
        return $this->campaignStatus;
    }

    public function setCampaignStatus(?string $campaignStatus): static
    {
        $this->campaignStatus = $campaignStatus;

        return $this;
    }

    public function getClicks(): ?int
    {
        return $this->clicks;
    }

    public function setClicks(?int $clicks): static
    {
        $this->clicks = $clicks;

        return $this;
    }

    public function getCost(): ?int
    {
        return $this->cost;
    }

    public function setCost(?int $cost): static
    {
        $this->cost = $cost;

        return $this;
    }

    public function getImpressions(): ?int
    {
        return $this->impressions;
    }

    public function setImpressions(?int $impressions): static
    {
        $this->impressions = $impressions;

        return $this;
    }

    public function getTargetId(): ?string
    {
        return $this->targetId;
    }

    public function setTargetId(?string $targetId): static
    {
        $this->targetId = $targetId;

        return $this;
    }

    public function getTargetingExpression(): ?string
    {
        return $this->targetingExpression;
    }

    public function setTargetingExpression(?string $targetingExpression): static
    {
        $this->targetingExpression = $targetingExpression;

        return $this;
    }

    public function getTargetingText(): ?string
    {
        return $this->targetingText;
    }

    public function setTargetingText(?string $targetingText): static
    {
        $this->targetingText = $targetingText;

        return $this;
    }

    public function getTargetingType(): ?string
    {
        return $this->targetingType;
    }

    public function setTargetingType(?string $targetingType): static
    {
        $this->targetingType = $targetingType;

        return $this;
    }

    public function getVctr(): ?float
    {
        return $this->vctr;
    }

    public function setVctr(?float $vctr): static
    {
        $this->vctr = $vctr;

        return $this;
    }

    public function getVideo5SecondViewRate(): ?float
    {
        return $this->video5SecondViewRate;
    }

    public function setVideo5SecondViewRate(?float $video5SecondViewRate): static
    {
        $this->video5SecondViewRate = $video5SecondViewRate;

        return $this;
    }

    public function getVideo5SecondViews(): ?int
    {
        return $this->video5SecondViews;
    }

    public function setVideo5SecondViews(?int $video5SecondViews): static
    {
        $this->video5SecondViews = $video5SecondViews;

        return $this;
    }

    public function getVideoCompleteViews(): ?int
    {
        return $this->videoCompleteViews;
    }

    public function setVideoCompleteViews(?int $videoCompleteViews): static
    {
        $this->videoCompleteViews = $videoCompleteViews;

        return $this;
    }

    public function getVideoFirstQuartileViews(): ?int
    {
        return $this->videoFirstQuartileViews;
    }

    public function setVideoFirstQuartileViews(?int $videoFirstQuartileViews): static
    {
        $this->videoFirstQuartileViews = $videoFirstQuartileViews;

        return $this;
    }

    public function getVideoMidpointViews(): ?int
    {
        return $this->videoMidpointViews;
    }

    public function setVideoMidpointViews(?int $videoMidpointViews): static
    {
        $this->videoMidpointViews = $videoMidpointViews;

        return $this;
    }

    public function getVideoThirdQuartileViews(): ?int
    {
        return $this->videoThirdQuartileViews;
    }

    public function setVideoThirdQuartileViews(?int $videoThirdQuartileViews): static
    {
        $this->videoThirdQuartileViews = $videoThirdQuartileViews;

        return $this;
    }

    public function getVideoUnmutes(): ?int
    {
        return $this->videoUnmutes;
    }

    public function setVideoUnmutes(?int $videoUnmutes): static
    {
        $this->videoUnmutes = $videoUnmutes;

        return $this;
    }

    public function getViewableImpressions(): ?int
    {
        return $this->viewableImpressions;
    }

    public function setViewableImpressions(?int $viewableImpressions): static
    {
        $this->viewableImpressions = $viewableImpressions;

        return $this;
    }

    public function getVtr(): ?float
    {
        return $this->vtr;
    }

    public function setVtr(?float $vtr): static
    {
        $this->vtr = $vtr;

        return $this;
    }

    public function getDpv14d(): ?int
    {
        return $this->dpv14d;
    }

    public function setDpv14d(?int $dpv14d): static
    {
        $this->dpv14d = $dpv14d;

        return $this;
    }

    public function getAttributedDetailPageViewsClicks14d(): ?int
    {
        return $this->attributedDetailPageViewsClicks14d;
    }

    public function setAttributedDetailPageViewsClicks14d(?int $attributedDetailPageViewsClicks14d): static
    {
        $this->attributedDetailPageViewsClicks14d = $attributedDetailPageViewsClicks14d;

        return $this;
    }

    public function getAttributedOrderRateNewToBrand14d(): ?float
    {
        return $this->attributedOrderRateNewToBrand14d;
    }

    public function setAttributedOrderRateNewToBrand14d(?float $attributedOrderRateNewToBrand14d): static
    {
        $this->attributedOrderRateNewToBrand14d = $attributedOrderRateNewToBrand14d;

        return $this;
    }

    public function getAttributedOrdersNewToBrand14d(): ?int
    {
        return $this->attributedOrdersNewToBrand14d;
    }

    public function setAttributedOrdersNewToBrand14d(?int $attributedOrdersNewToBrand14d): static
    {
        $this->attributedOrdersNewToBrand14d = $attributedOrdersNewToBrand14d;

        return $this;
    }

    public function getAttributedOrdersNewToBrandPercentage14d(): ?float
    {
        return $this->attributedOrdersNewToBrandPercentage14d;
    }

    public function setAttributedOrdersNewToBrandPercentage14d(?float $attributedOrdersNewToBrandPercentage14d): static
    {
        $this->attributedOrdersNewToBrandPercentage14d = $attributedOrdersNewToBrandPercentage14d;

        return $this;
    }

    public function getAttributedSalesNewToBrand14d(): ?int
    {
        return $this->attributedSalesNewToBrand14d;
    }

    public function setAttributedSalesNewToBrand14d(?int $attributedSalesNewToBrand14d): static
    {
        $this->attributedSalesNewToBrand14d = $attributedSalesNewToBrand14d;

        return $this;
    }

    public function getAttributedSalesNewToBrandPercentage14d(): ?float
    {
        return $this->attributedSalesNewToBrandPercentage14d;
    }

    public function setAttributedSalesNewToBrandPercentage14d(?float $attributedSalesNewToBrandPercentage14d): static
    {
        $this->attributedSalesNewToBrandPercentage14d = $attributedSalesNewToBrandPercentage14d;

        return $this;
    }

    public function getAttributedUnitsOrderedNewToBrand14d(): ?int
    {
        return $this->attributedUnitsOrderedNewToBrand14d;
    }

    public function setAttributedUnitsOrderedNewToBrand14d(?int $attributedUnitsOrderedNewToBrand14d): static
    {
        $this->attributedUnitsOrderedNewToBrand14d = $attributedUnitsOrderedNewToBrand14d;

        return $this;
    }

    public function getAttributedUnitsOrderedNewToBrandPercentage14d(): ?float
    {
        return $this->attributedUnitsOrderedNewToBrandPercentage14d;
    }

    public function setAttributedUnitsOrderedNewToBrandPercentage14d(?float $attributedUnitsOrderedNewToBrandPercentage14d): static
    {
        $this->attributedUnitsOrderedNewToBrandPercentage14d = $attributedUnitsOrderedNewToBrandPercentage14d;

        return $this;
    }

    public function getAttributedBrandedSearches14d(): ?int
    {
        return $this->attributedBrandedSearches14d;
    }

    public function setAttributedBrandedSearches14d(?int $attributedBrandedSearches14d): static
    {
        $this->attributedBrandedSearches14d = $attributedBrandedSearches14d;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getTopOfSearchImpressionShare(): ?float
    {
        return $this->topOfSearchImpressionShare;
    }

    public function setTopOfSearchImpressionShare(?float $topOfSearchImpressionShare): static
    {
        $this->topOfSearchImpressionShare = $topOfSearchImpressionShare;

        return $this;
    }
}
