<?php

namespace App\Entity\AaaReport;

use App\Repository\AaaReport\DataAaaReportAdGroupSdRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'data_aar_adg_sd')]
#[ORM\Index(columns: ['adGroupId'], name: 'IDX_ADGRP')]
#[ORM\Index(columns: ['campaignId'], name: 'IDX_CMPGN')]
#[ORM\Entity(repositoryClass: DataAaaReportAdGroupSdRepository::class)]
class DataAaaReportAdGroupSd extends DataAaaReportCommon
{
    public static function getUniquePerDayIdentifierFields(): array
    {
        return ['reportDate','adGroupId'];
    }

    // ############################################
    // #  Doctrine related code lives under here  #
    // ############################################
    #[ORM\Column(name: 'adGroupId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $adGroupId = null;

    #[ORM\Column(name: 'adGroupName', type: Types::STRING, nullable: true)]
    protected ?string $adGroupName = null;

    #[ORM\Column(name: 'attributedConversions14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions14d = null;

    #[ORM\Column(name: 'attributedConversions14dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions14dSameSKU = null;

    #[ORM\Column(name: 'attributedConversions1d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions1d = null;

    #[ORM\Column(name: 'attributedConversions1dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions1dSameSKU = null;

    #[ORM\Column(name: 'attributedConversions30d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions30d = null;

    #[ORM\Column(name: 'attributedConversions30dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions30dSameSKU = null;

    #[ORM\Column(name: 'attributedConversions7d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions7d = null;

    #[ORM\Column(name: 'attributedConversions7dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedConversions7dSameSKU = null;

    #[ORM\Column(name: 'attributedDetailPageView14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedDetailPageView14d = null;

    #[ORM\Column(name: 'attributedOrdersNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedOrdersNewToBrand14d = null;

    #[ORM\Column(name: 'attributedSales14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales14d = null;

    #[ORM\Column(name: 'attributedSales14dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales14dSameSKU = null;

    #[ORM\Column(name: 'attributedSales1d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales1d = null;

    #[ORM\Column(name: 'attributedSales1dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales1dSameSKU = null;

    #[ORM\Column(name: 'attributedSales30d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales30d = null;

    #[ORM\Column(name: 'attributedSales30dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales30dSameSKU = null;

    #[ORM\Column(name: 'attributedSales7d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales7d = null;

    #[ORM\Column(name: 'attributedSales7dSameSKU', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSales7dSameSKU = null;

    #[ORM\Column(name: 'attributedUnitsOrdered14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrdered14d = null;

    #[ORM\Column(name: 'attributedUnitsOrdered1d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrdered1d = null;

    #[ORM\Column(name: 'attributedUnitsOrdered30d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrdered30d = null;

    #[ORM\Column(name: 'attributedUnitsOrdered7d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrdered7d = null;

    #[ORM\Column(name: 'attributedUnitsOrderedNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedUnitsOrderedNewToBrand14d = null;

    #[ORM\Column(name: 'bidOptimization', type: Types::STRING, length: 18, nullable: true)]
    protected ?string $bidOptimization = null;

    #[ORM\Column(name: 'campaignId', type: Types::STRING, length: 20, nullable: true)]
    protected ?string $campaignId = null;

    #[ORM\Column(name: 'campaignName', type: Types::STRING, nullable: true)]
    protected ?string $campaignName = null;

    #[ORM\Column(name: 'clicks', type: Types::INTEGER, nullable: true)]
    protected ?int $clicks = null;

    #[ORM\Column(name: 'cost', type: Types::INTEGER, nullable: true)]
    protected ?int $cost = null;

    #[ORM\Column(name: 'currency', type: Types::STRING, length: 3, nullable: true)]
    protected ?string $currency = null;

    #[ORM\Column(name: 'impressions', type: Types::INTEGER, nullable: true)]
    protected ?int $impressions = null;

    #[ORM\Column(name: 'viewAttributedConversions14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedConversions14d = null;

    #[ORM\Column(name: 'viewAttributedDetailPageView14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedDetailPageView14d = null;

    #[ORM\Column(name: 'viewAttributedSales14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedSales14d = null;

    #[ORM\Column(name: 'viewAttributedUnitsOrdered14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedUnitsOrdered14d = null;

    #[ORM\Column(name: 'viewImpressions', type: Types::INTEGER, nullable: true)]
    protected ?int $viewImpressions = null;

    #[ORM\Column(name: 'viewAttributedOrdersNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedOrdersNewToBrand14d = null;

    #[ORM\Column(name: 'viewAttributedSalesNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedSalesNewToBrand14d = null;

    #[ORM\Column(name: 'viewAttributedUnitsOrderedNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedUnitsOrderedNewToBrand14d = null;

    #[ORM\Column(name: 'attributedBrandedSearches14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedBrandedSearches14d = null;

    #[ORM\Column(name: 'viewAttributedBrandedSearches14d', type: Types::INTEGER, nullable: true)]
    protected ?int $viewAttributedBrandedSearches14d = null;

    #[ORM\Column(name: 'videoCompleteViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoCompleteViews = null;

    #[ORM\Column(name: 'videoFirstQuartileViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoFirstQuartileViews = null;

    #[ORM\Column(name: 'videoMidpointViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoMidpointViews = null;

    #[ORM\Column(name: 'videoThirdQuartileViews', type: Types::INTEGER, nullable: true)]
    protected ?int $videoThirdQuartileViews = null;

    #[ORM\Column(name: 'videoUnmutes', type: Types::INTEGER, nullable: true)]
    protected ?int $videoUnmutes = null;

    #[ORM\Column(name: 'vtr', type: Types::FLOAT, nullable: true)]
    protected ?float $vtr = null;

    #[ORM\Column(name: 'vctr', type: Types::FLOAT, nullable: true)]
    protected ?float $vctr = null;

    #[ORM\Column(name: 'avgImpressionsFrequency', type: Types::FLOAT, nullable: true)]
    protected ?float $avgImpressionsFrequency = null;

    #[ORM\Column(name: 'cumulativeReach', type: Types::INTEGER, nullable: true)]
    protected ?int $cumulativeReach = null;

    #[ORM\Column(name: 'atcVoc', type: Types::INTEGER, nullable: true)]
    protected ?int $atcVoc = null;

    #[ORM\Column(name: 'atcCat', type: Types::INTEGER, nullable: true)]
    protected ?int $atcCat = null;

    #[ORM\Column(name: 'atcRate', type: Types::FLOAT, nullable: true)]
    protected ?float $atcRate = null;

    #[ORM\Column(name: 'addToCartViews', type: Types::INTEGER, nullable: true)]
    protected ?int $addToCartViews = null;

    #[ORM\Column(name: 'brandedSearchesVoc', type: Types::INTEGER, nullable: true)]
    protected ?int $brandedSearchesVoc = null;

    #[ORM\Column(name: 'brandedSearchRate', type: Types::FLOAT, nullable: true)]
    protected ?float $brandedSearchRate = null;

    #[ORM\Column(name: 'eCPATC', type: Types::INTEGER, nullable: true)]
    protected ?int $eCPATC = null;

    #[ORM\Column(name: 'eCPBrandSearch', type: Types::INTEGER, nullable: true)]
    protected ?int $eCPBrandSearch = null;

    #[ORM\Column(name: 'attributedSalesNewToBrand14d', type: Types::INTEGER, nullable: true)]
    protected ?int $attributedSalesNewToBrand14d = null;

    #[ORM\Column(name: 'newToBrandDetailPageViewClicks', type: Types::INTEGER, nullable: true)]
    protected ?int $newToBrandDetailPageViewClicks = null;

    #[ORM\Column(name: 'ntbDpViewsVoc', type: Types::INTEGER, nullable: true)]
    protected ?int $ntbDpViewsVoc = null;

    #[ORM\Column(name: 'newToBrandDetailPageViewViews', type: Types::INTEGER, nullable: true)]
    protected ?int $newToBrandDetailPageViewViews = null;

    #[ORM\Column(name: 'eCPNTBDPV', type: Types::INTEGER, nullable: true)]
    protected ?int $eCPNTBDPV = null;

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getAdGroupId(): ?string
    {
        return $this->adGroupId;
    }

    public function setAdGroupId(?string $adGroupId): static
    {
        $this->adGroupId = $adGroupId;

        return $this;
    }

    public function getAdGroupName(): ?string
    {
        return $this->adGroupName;
    }

    public function setAdGroupName(?string $adGroupName): static
    {
        $this->adGroupName = $adGroupName;

        return $this;
    }

    public function getAttributedConversions14d(): ?int
    {
        return $this->attributedConversions14d;
    }

    public function setAttributedConversions14d(?int $attributedConversions14d): static
    {
        $this->attributedConversions14d = $attributedConversions14d;

        return $this;
    }

    public function getAttributedConversions14dSameSKU(): ?int
    {
        return $this->attributedConversions14dSameSKU;
    }

    public function setAttributedConversions14dSameSKU(?int $attributedConversions14dSameSKU): static
    {
        $this->attributedConversions14dSameSKU = $attributedConversions14dSameSKU;

        return $this;
    }

    public function getAttributedConversions1d(): ?int
    {
        return $this->attributedConversions1d;
    }

    public function setAttributedConversions1d(?int $attributedConversions1d): static
    {
        $this->attributedConversions1d = $attributedConversions1d;

        return $this;
    }

    public function getAttributedConversions1dSameSKU(): ?int
    {
        return $this->attributedConversions1dSameSKU;
    }

    public function setAttributedConversions1dSameSKU(?int $attributedConversions1dSameSKU): static
    {
        $this->attributedConversions1dSameSKU = $attributedConversions1dSameSKU;

        return $this;
    }

    public function getAttributedConversions30d(): ?int
    {
        return $this->attributedConversions30d;
    }

    public function setAttributedConversions30d(?int $attributedConversions30d): static
    {
        $this->attributedConversions30d = $attributedConversions30d;

        return $this;
    }

    public function getAttributedConversions30dSameSKU(): ?int
    {
        return $this->attributedConversions30dSameSKU;
    }

    public function setAttributedConversions30dSameSKU(?int $attributedConversions30dSameSKU): static
    {
        $this->attributedConversions30dSameSKU = $attributedConversions30dSameSKU;

        return $this;
    }

    public function getAttributedConversions7d(): ?int
    {
        return $this->attributedConversions7d;
    }

    public function setAttributedConversions7d(?int $attributedConversions7d): static
    {
        $this->attributedConversions7d = $attributedConversions7d;

        return $this;
    }

    public function getAttributedConversions7dSameSKU(): ?int
    {
        return $this->attributedConversions7dSameSKU;
    }

    public function setAttributedConversions7dSameSKU(?int $attributedConversions7dSameSKU): static
    {
        $this->attributedConversions7dSameSKU = $attributedConversions7dSameSKU;

        return $this;
    }

    public function getAttributedDetailPageView14d(): ?int
    {
        return $this->attributedDetailPageView14d;
    }

    public function setAttributedDetailPageView14d(?int $attributedDetailPageView14d): static
    {
        $this->attributedDetailPageView14d = $attributedDetailPageView14d;

        return $this;
    }

    public function getAttributedOrdersNewToBrand14d(): ?int
    {
        return $this->attributedOrdersNewToBrand14d;
    }

    public function setAttributedOrdersNewToBrand14d(?int $attributedOrdersNewToBrand14d): static
    {
        $this->attributedOrdersNewToBrand14d = $attributedOrdersNewToBrand14d;

        return $this;
    }

    public function getAttributedSales14d(): ?int
    {
        return $this->attributedSales14d;
    }

    public function setAttributedSales14d(?int $attributedSales14d): static
    {
        $this->attributedSales14d = $attributedSales14d;

        return $this;
    }

    public function getAttributedSales14dSameSKU(): ?int
    {
        return $this->attributedSales14dSameSKU;
    }

    public function setAttributedSales14dSameSKU(?int $attributedSales14dSameSKU): static
    {
        $this->attributedSales14dSameSKU = $attributedSales14dSameSKU;

        return $this;
    }

    public function getAttributedSales1d(): ?int
    {
        return $this->attributedSales1d;
    }

    public function setAttributedSales1d(?int $attributedSales1d): static
    {
        $this->attributedSales1d = $attributedSales1d;

        return $this;
    }

    public function getAttributedSales1dSameSKU(): ?int
    {
        return $this->attributedSales1dSameSKU;
    }

    public function setAttributedSales1dSameSKU(?int $attributedSales1dSameSKU): static
    {
        $this->attributedSales1dSameSKU = $attributedSales1dSameSKU;

        return $this;
    }

    public function getAttributedSales30d(): ?int
    {
        return $this->attributedSales30d;
    }

    public function setAttributedSales30d(?int $attributedSales30d): static
    {
        $this->attributedSales30d = $attributedSales30d;

        return $this;
    }

    public function getAttributedSales30dSameSKU(): ?int
    {
        return $this->attributedSales30dSameSKU;
    }

    public function setAttributedSales30dSameSKU(?int $attributedSales30dSameSKU): static
    {
        $this->attributedSales30dSameSKU = $attributedSales30dSameSKU;

        return $this;
    }

    public function getAttributedSales7d(): ?int
    {
        return $this->attributedSales7d;
    }

    public function setAttributedSales7d(?int $attributedSales7d): static
    {
        $this->attributedSales7d = $attributedSales7d;

        return $this;
    }

    public function getAttributedSales7dSameSKU(): ?int
    {
        return $this->attributedSales7dSameSKU;
    }

    public function setAttributedSales7dSameSKU(?int $attributedSales7dSameSKU): static
    {
        $this->attributedSales7dSameSKU = $attributedSales7dSameSKU;

        return $this;
    }

    public function getAttributedUnitsOrdered14d(): ?int
    {
        return $this->attributedUnitsOrdered14d;
    }

    public function setAttributedUnitsOrdered14d(?int $attributedUnitsOrdered14d): static
    {
        $this->attributedUnitsOrdered14d = $attributedUnitsOrdered14d;

        return $this;
    }

    public function getAttributedUnitsOrdered1d(): ?int
    {
        return $this->attributedUnitsOrdered1d;
    }

    public function setAttributedUnitsOrdered1d(?int $attributedUnitsOrdered1d): static
    {
        $this->attributedUnitsOrdered1d = $attributedUnitsOrdered1d;

        return $this;
    }

    public function getAttributedUnitsOrdered30d(): ?int
    {
        return $this->attributedUnitsOrdered30d;
    }

    public function setAttributedUnitsOrdered30d(?int $attributedUnitsOrdered30d): static
    {
        $this->attributedUnitsOrdered30d = $attributedUnitsOrdered30d;

        return $this;
    }

    public function getAttributedUnitsOrdered7d(): ?int
    {
        return $this->attributedUnitsOrdered7d;
    }

    public function setAttributedUnitsOrdered7d(?int $attributedUnitsOrdered7d): static
    {
        $this->attributedUnitsOrdered7d = $attributedUnitsOrdered7d;

        return $this;
    }

    public function getAttributedUnitsOrderedNewToBrand14d(): ?int
    {
        return $this->attributedUnitsOrderedNewToBrand14d;
    }

    public function setAttributedUnitsOrderedNewToBrand14d(?int $attributedUnitsOrderedNewToBrand14d): static
    {
        $this->attributedUnitsOrderedNewToBrand14d = $attributedUnitsOrderedNewToBrand14d;

        return $this;
    }

    public function getBidOptimization(): ?string
    {
        return $this->bidOptimization;
    }

    public function setBidOptimization(?string $bidOptimization): static
    {
        $this->bidOptimization = $bidOptimization;

        return $this;
    }

    public function getCampaignId(): ?string
    {
        return $this->campaignId;
    }

    public function setCampaignId(?string $campaignId): static
    {
        $this->campaignId = $campaignId;

        return $this;
    }

    public function getCampaignName(): ?string
    {
        return $this->campaignName;
    }

    public function setCampaignName(?string $campaignName): static
    {
        $this->campaignName = $campaignName;

        return $this;
    }

    public function getClicks(): ?int
    {
        return $this->clicks;
    }

    public function setClicks(?int $clicks): static
    {
        $this->clicks = $clicks;

        return $this;
    }

    public function getCost(): ?int
    {
        return $this->cost;
    }

    public function setCost(?int $cost): static
    {
        $this->cost = $cost;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getImpressions(): ?int
    {
        return $this->impressions;
    }

    public function setImpressions(?int $impressions): static
    {
        $this->impressions = $impressions;

        return $this;
    }

    public function getViewAttributedConversions14d(): ?int
    {
        return $this->viewAttributedConversions14d;
    }

    public function setViewAttributedConversions14d(?int $viewAttributedConversions14d): static
    {
        $this->viewAttributedConversions14d = $viewAttributedConversions14d;

        return $this;
    }

    public function getViewAttributedDetailPageView14d(): ?int
    {
        return $this->viewAttributedDetailPageView14d;
    }

    public function setViewAttributedDetailPageView14d(?int $viewAttributedDetailPageView14d): static
    {
        $this->viewAttributedDetailPageView14d = $viewAttributedDetailPageView14d;

        return $this;
    }

    public function getViewAttributedSales14d(): ?int
    {
        return $this->viewAttributedSales14d;
    }

    public function setViewAttributedSales14d(?int $viewAttributedSales14d): static
    {
        $this->viewAttributedSales14d = $viewAttributedSales14d;

        return $this;
    }

    public function getViewAttributedUnitsOrdered14d(): ?int
    {
        return $this->viewAttributedUnitsOrdered14d;
    }

    public function setViewAttributedUnitsOrdered14d(?int $viewAttributedUnitsOrdered14d): static
    {
        $this->viewAttributedUnitsOrdered14d = $viewAttributedUnitsOrdered14d;

        return $this;
    }

    public function getViewImpressions(): ?int
    {
        return $this->viewImpressions;
    }

    public function setViewImpressions(?int $viewImpressions): static
    {
        $this->viewImpressions = $viewImpressions;

        return $this;
    }

    public function getViewAttributedOrdersNewToBrand14d(): ?int
    {
        return $this->viewAttributedOrdersNewToBrand14d;
    }

    public function setViewAttributedOrdersNewToBrand14d(?int $viewAttributedOrdersNewToBrand14d): static
    {
        $this->viewAttributedOrdersNewToBrand14d = $viewAttributedOrdersNewToBrand14d;

        return $this;
    }

    public function getViewAttributedSalesNewToBrand14d(): ?int
    {
        return $this->viewAttributedSalesNewToBrand14d;
    }

    public function setViewAttributedSalesNewToBrand14d(?int $viewAttributedSalesNewToBrand14d): static
    {
        $this->viewAttributedSalesNewToBrand14d = $viewAttributedSalesNewToBrand14d;

        return $this;
    }

    public function getViewAttributedUnitsOrderedNewToBrand14d(): ?int
    {
        return $this->viewAttributedUnitsOrderedNewToBrand14d;
    }

    public function setViewAttributedUnitsOrderedNewToBrand14d(?int $viewAttributedUnitsOrderedNewToBrand14d): static
    {
        $this->viewAttributedUnitsOrderedNewToBrand14d = $viewAttributedUnitsOrderedNewToBrand14d;

        return $this;
    }

    public function getAttributedBrandedSearches14d(): ?int
    {
        return $this->attributedBrandedSearches14d;
    }

    public function setAttributedBrandedSearches14d(?int $attributedBrandedSearches14d): static
    {
        $this->attributedBrandedSearches14d = $attributedBrandedSearches14d;

        return $this;
    }

    public function getViewAttributedBrandedSearches14d(): ?int
    {
        return $this->viewAttributedBrandedSearches14d;
    }

    public function setViewAttributedBrandedSearches14d(?int $viewAttributedBrandedSearches14d): static
    {
        $this->viewAttributedBrandedSearches14d = $viewAttributedBrandedSearches14d;

        return $this;
    }

    public function getVideoCompleteViews(): ?int
    {
        return $this->videoCompleteViews;
    }

    public function setVideoCompleteViews(?int $videoCompleteViews): static
    {
        $this->videoCompleteViews = $videoCompleteViews;

        return $this;
    }

    public function getVideoFirstQuartileViews(): ?int
    {
        return $this->videoFirstQuartileViews;
    }

    public function setVideoFirstQuartileViews(?int $videoFirstQuartileViews): static
    {
        $this->videoFirstQuartileViews = $videoFirstQuartileViews;

        return $this;
    }

    public function getVideoMidpointViews(): ?int
    {
        return $this->videoMidpointViews;
    }

    public function setVideoMidpointViews(?int $videoMidpointViews): static
    {
        $this->videoMidpointViews = $videoMidpointViews;

        return $this;
    }

    public function getVideoThirdQuartileViews(): ?int
    {
        return $this->videoThirdQuartileViews;
    }

    public function setVideoThirdQuartileViews(?int $videoThirdQuartileViews): static
    {
        $this->videoThirdQuartileViews = $videoThirdQuartileViews;

        return $this;
    }

    public function getVideoUnmutes(): ?int
    {
        return $this->videoUnmutes;
    }

    public function setVideoUnmutes(?int $videoUnmutes): static
    {
        $this->videoUnmutes = $videoUnmutes;

        return $this;
    }

    public function getVtr(): ?float
    {
        return $this->vtr;
    }

    public function setVtr(?float $vtr): static
    {
        $this->vtr = $vtr;

        return $this;
    }

    public function getVctr(): ?float
    {
        return $this->vctr;
    }

    public function setVctr(?float $vctr): static
    {
        $this->vctr = $vctr;

        return $this;
    }

    public function getAvgImpressionsFrequency(): ?float
    {
        return $this->avgImpressionsFrequency;
    }

    public function setAvgImpressionsFrequency(?float $avgImpressionsFrequency): static
    {
        $this->avgImpressionsFrequency = $avgImpressionsFrequency;

        return $this;
    }

    public function getCumulativeReach(): ?int
    {
        return $this->cumulativeReach;
    }

    public function setCumulativeReach(?int $cumulativeReach): static
    {
        $this->cumulativeReach = $cumulativeReach;

        return $this;
    }

    public function getAtcVoc(): ?int
    {
        return $this->atcVoc;
    }

    public function setAtcVoc(?int $atcVoc): static
    {
        $this->atcVoc = $atcVoc;

        return $this;
    }

    public function getAtcCat(): ?int
    {
        return $this->atcCat;
    }

    public function setAtcCat(?int $atcCat): static
    {
        $this->atcCat = $atcCat;

        return $this;
    }

    public function getAtcRate(): ?float
    {
        return $this->atcRate;
    }

    public function setAtcRate(?float $atcRate): static
    {
        $this->atcRate = $atcRate;

        return $this;
    }

    public function getAddToCartViews(): ?int
    {
        return $this->addToCartViews;
    }

    public function setAddToCartViews(?int $addToCartViews): static
    {
        $this->addToCartViews = $addToCartViews;

        return $this;
    }

    public function getBrandedSearchesVoc(): ?int
    {
        return $this->brandedSearchesVoc;
    }

    public function setBrandedSearchesVoc(?int $brandedSearchesVoc): static
    {
        $this->brandedSearchesVoc = $brandedSearchesVoc;

        return $this;
    }

    public function getBrandedSearchRate(): ?float
    {
        return $this->brandedSearchRate;
    }

    public function setBrandedSearchRate(?float $brandedSearchRate): static
    {
        $this->brandedSearchRate = $brandedSearchRate;

        return $this;
    }

    public function getECPATC(): ?int
    {
        return $this->eCPATC;
    }

    public function setECPATC(?int $eCPATC): static
    {
        $this->eCPATC = $eCPATC;

        return $this;
    }

    public function getECPBrandSearch(): ?int
    {
        return $this->eCPBrandSearch;
    }

    public function setECPBrandSearch(?int $eCPBrandSearch): static
    {
        $this->eCPBrandSearch = $eCPBrandSearch;

        return $this;
    }

    public function getAttributedSalesNewToBrand14d(): ?int
    {
        return $this->attributedSalesNewToBrand14d;
    }

    public function setAttributedSalesNewToBrand14d(?int $attributedSalesNewToBrand14d): static
    {
        $this->attributedSalesNewToBrand14d = $attributedSalesNewToBrand14d;

        return $this;
    }

    public function getNewToBrandDetailPageViewClicks(): ?int
    {
        return $this->newToBrandDetailPageViewClicks;
    }

    public function setNewToBrandDetailPageViewClicks(?int $newToBrandDetailPageViewClicks): static
    {
        $this->newToBrandDetailPageViewClicks = $newToBrandDetailPageViewClicks;

        return $this;
    }

    public function getNtbDpViewsVoc(): ?int
    {
        return $this->ntbDpViewsVoc;
    }

    public function setNtbDpViewsVoc(?int $ntbDpViewsVoc): static
    {
        $this->ntbDpViewsVoc = $ntbDpViewsVoc;

        return $this;
    }

    public function getNewToBrandDetailPageViewViews(): ?int
    {
        return $this->newToBrandDetailPageViewViews;
    }

    public function setNewToBrandDetailPageViewViews(?int $newToBrandDetailPageViewViews): static
    {
        $this->newToBrandDetailPageViewViews = $newToBrandDetailPageViewViews;

        return $this;
    }

    public function getECPNTBDPV(): ?int
    {
        return $this->eCPNTBDPV;
    }

    public function setECPNTBDPV(?int $eCPNTBDPV): static
    {
        $this->eCPNTBDPV = $eCPNTBDPV;

        return $this;
    }
}
