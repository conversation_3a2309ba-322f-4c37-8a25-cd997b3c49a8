<?php

namespace App\Entity\Tracker;

use App\Domain\AmazonSpaPool;
use App\Domain\Enum\SupplyChainCategory;
use App\Entity\Channel;
use App\Entity\GravitiqBaseEntity;
use App\Entity\Sonata\User;
use App\Repository\Tracker\SalesForecastGroupRepository;
use App\Service\Internal\SalesForecastManager;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'tk_fsr_group')]
#[ORM\Entity(repositoryClass: SalesForecastGroupRepository::class)]
#[ORM\UniqueConstraint(name: 'idx_fsr_group_unique', columns: ['blueprintId', 'sfRegion', 'forecastMadeInMonth', 'channelFamily'])]
#[ORM\Index(columns: ['sfRegion', 'forecastMadeInMonth'], name: 'idx_region_month')]
#[ORM\Index(columns: ['forecastMadeInMonth'], name: 'idx_month')]
class SalesForecastGroup extends GravitiqBaseEntity
{
    public const array LOCK_PERIOD = [
        'Unlocked'  => 'Unlocked',  // anyone can edit
        'Partial'   => 'Partial',   // only ROLE_ADMIN_SALES_FORECAST_GROUP_UNCONFIRM_EARLY can unlock then edit
        'Locked'    => 'Locked',    // only ROLE_ADMIN_SALES_FORECAST_GROUP_UNCONFIRM_ANY can unlock then edit
    ];

    public const int EXPECTED_SALES_FORECAST_DETAIL_COUNT = 12;

    public function __toString(): string
    {
        if (empty($this->id)) {
            return 'New Sales Forecast Group';
        } else {
            return "{$this->getForecastMadeInMonth()->format('M Y')} Sales Forecast for {$this->getIskuAndRegion()}";
        }
    }

    public function isMadeInCurrentMonth(): bool
    {
        $currentMonthString = (new \DateTimeImmutable())->format('Y-m-01');
        return ($this->getForecastMadeInMonth()->format('Y-m-01') === $currentMonthString);
    }

    protected function getCurrentDayOfMonth(): int
    {
        return (int)date('j');
    }

    public function isSupplyChainCategoryNew(): bool
    {
        return ($this->getSupplyChainCategory() === SupplyChainCategory::N_New);
    }

    /**
     * @phpstan-return value-of<self::LOCK_PERIOD>
     * @return string
     */
    public function getCurrentLockPeriod(): string
    {
        if ($this->isNotLockableBecauseItIsNew()) {
            return self::LOCK_PERIOD['Unlocked'];
        }

        $dayOfMonth = $this->getCurrentDayOfMonth();
        if (6 > $dayOfMonth) {
            return self::LOCK_PERIOD['Unlocked'];
        } elseif (10 > $dayOfMonth) {
            return self::LOCK_PERIOD['Partial'];
        } else {
            return self::LOCK_PERIOD['Locked'];
        }
    }

    public function isCurrentlyLockPeriodUnlocked(): bool
    {
        return ($this->getCurrentLockPeriod() === self::LOCK_PERIOD['Unlocked']);
    }
    public function isCurrentlyLockPeriodPartial(): bool
    {
        return ($this->getCurrentLockPeriod() === self::LOCK_PERIOD['Partial']);
    }
    public function isCurrentlyLockPeriodLocked(): bool
    {
        return ($this->getCurrentLockPeriod() === self::LOCK_PERIOD['Locked']);
    }

    public function isPossibleToUnconfirm(bool $hasPermissionToUnlockEarly, bool $hasPermissionToUnlockAny): bool
    {
        if ($this->isUnconfirmed()) return false;
        if ($this->isCurrentlyLockPeriodUnlocked()) return false;
        if ($this->isCurrentlyLockPeriodPartial() && $this->isConfirmed() && ($hasPermissionToUnlockEarly || $hasPermissionToUnlockAny)) return true;
        if ($this->isCurrentlyLockPeriodLocked() && $hasPermissionToUnlockAny) return true;
        return false;
    }

    public function isTooLateToEditConfirmedForecast(): bool
    {
        return (!$this->isCurrentlyLockPeriodUnlocked() && $this->isConfirmed());
    }

    public function isTooLateToEditAnyForecast(): bool
    {
        return ($this->isCurrentlyLockPeriodLocked() && !$this->isUnconfirmed());
    }

    public function canBeBatchConfirmed(): bool
    {
        if ($this->isConfirmed()) {
            return false;
        }
        if ($this->getSupplyChainCategory()?->isImportant()) {
            return false;
        }
        return true;
    }

    public function getIskuAndRegion(): string
    {
        return "{$this->getBlueprint()->getIsku()} in {$this->getSfRegion()}";
    }

    /**
     * @return array{sfg: SalesForecastGroup, forecast: list<?float>}
     */
    public function getSfDetailsWithGroupAsSfg(): array
    {
        $forecast = [];
        foreach ($this->getSfDetails() as $sfDetail) {
            $forecast[] = $sfDetail->getFsr();
        }

        return [
            'sfg' => $this,
            SalesForecastManager::DATA_KEY_FORECAST => $forecast,
        ];
    }

    /**
     * @return list<string>
     */
    public function getChannelIds(): array
    {
        $channelIds = [];
        if ($this->getSfRegion() === 'EUR') {
            $suffixes = AmazonSpaPool::POTENTIAL_PAN_EU_COUNTRIES;
            foreach ($suffixes as $suffix) {
                $channelIds[] = $this->getChannelFamily() . '_' . $suffix;
            }
        } elseif (2 === strlen($this->getSfRegion())) {
            $channelIds[] = $this->getChannelFamily() . '_' . $this->getSfRegion();
        } else {
            throw new \InvalidArgumentException('Unexpected region code: ' . $this->getSfRegion());
        }

        return $channelIds;
    }

    public function getBrandCode(): string
    {
        $isku = $this->getBlueprint()?->getIsku();
        if (empty($isku)) {
            return '';
        }
        return substr($isku, 0, 2);
    }
    public function supplyChainCategoryString(): string
    {
        return $this->getSupplyChainCategory()->value;
    }

    public function isConfirmed(): bool
    {
        return (!empty($this->getForecastConfirmationDate()) && !$this->isUnconfirmed());
    }

    public function getFsr1(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(1);
    }
    public function getFsr2(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(2);
    }
    public function getFsr3(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(3);
    }
    public function getFsr4(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(4);
    }
    public function getFsr5(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(5);
    }
    public function getFsr6(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(6);
    }
    public function getFsr7(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(7);
    }
    public function getFsr8(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(8);
    }
    public function getFsr9(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(9);
    }
    public function getFsr10(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(10);
    }
    public function getFsr11(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(11);
    }
    public function getFsr12(): int|float|null
    {
        return $this->getFsrForFutureMonthIndex(12);
    }

    public function getFsrForFutureMonthIndex(int $monthIndex): int|float|null
    {
        $baseMonth = $this->getForecastMadeInMonth();
        $targetMonth = $baseMonth->modify("+{$monthIndex} month");
        foreach ($this->getSfDetails() as $sfd) {
            if ($sfd->getForecastIsForMonthAsString() === $targetMonth->format('Y-m')) {
                return $sfd->getFsr();
            }
        }
        return null;
    }

    public function getComment1(): ?string
    {
        return $this->getCommentForFutureMonthIndex(1);
    }
    public function getComment2(): ?string
    {
        return $this->getCommentForFutureMonthIndex(2);
    }
    public function getComment3(): ?string
    {
        return $this->getCommentForFutureMonthIndex(3);
    }
    public function getComment4(): ?string
    {
        return $this->getCommentForFutureMonthIndex(4);
    }
    public function getComment5(): ?string
    {
        return $this->getCommentForFutureMonthIndex(5);
    }
    public function getComment6(): ?string
    {
        return $this->getCommentForFutureMonthIndex(6);
    }
    public function getComment7(): ?string
    {
        return $this->getCommentForFutureMonthIndex(7);
    }
    public function getComment8(): ?string
    {
        return $this->getCommentForFutureMonthIndex(8);
    }
    public function getComment9(): ?string
    {
        return $this->getCommentForFutureMonthIndex(9);
    }
    public function getComment10(): ?string
    {
        return $this->getCommentForFutureMonthIndex(10);
    }
    public function getComment11(): ?string
    {
        return $this->getCommentForFutureMonthIndex(11);
    }
    public function getComment12(): ?string
    {
        return $this->getCommentForFutureMonthIndex(12);
    }

    public function getCommentForFutureMonthIndex(int $monthIndex): ?string
    {
        $baseMonth = $this->getForecastMadeInMonth();
        $targetMonth = $baseMonth->modify("+{$monthIndex} month");
        foreach ($this->getSfDetails() as $sfd) {
            if ($sfd->getForecastIsForMonthAsString() === $targetMonth->format('Y-m')) {
                return $sfd->getNotes();
            }
        }
        return null;
    }

    public function getHsr0(): int|float|null
    {
        return $this->getHsrForMonthIndex(0);
    }
    public function getHsr1(): int|float|null
    {
        return $this->getHsrForMonthIndex(1);
    }
    public function getHsr2(): int|float|null
    {
        return $this->getHsrForMonthIndex(2);
    }
    public function getHsr3(): int|float|null
    {
        return $this->getHsrForMonthIndex(3);
    }
    public function getHsr4(): int|float|null
    {
        return $this->getHsrForMonthIndex(4);
    }
    public function getHsr5(): int|float|null
    {
        return $this->getHsrForMonthIndex(5);
    }
    public function getHsr6(): int|float|null
    {
        return $this->getHsrForMonthIndex(6);
    }
    public function getHsr7(): int|float|null
    {
        return $this->getHsrForMonthIndex(7);
    }
    public function getHsr8(): int|float|null
    {
        return $this->getHsrForMonthIndex(8);
    }
    public function getHsr9(): int|float|null
    {
        return $this->getHsrForMonthIndex(9);
    }
    public function getHsr10(): int|float|null
    {
        return $this->getHsrForMonthIndex(10);
    }
    public function getHsr11(): int|float|null
    {
        return $this->getHsrForMonthIndex(11);
    }
    public function getHsr12(): int|float|null
    {
        return $this->getHsrForMonthIndex(12);
    }
    public function getHsrForMonthIndex(int $monthIndex): int|float|null
    {
        if (is_null($this->sfManager)) {
            return -1;
        }
        $historicValues = $this->buildHistoricValues();
        $hsr = $historicValues[$monthIndex] ?? null;
        if (is_null($hsr)) {
            return null;
        }

        if (10 > $hsr) {
            return round($hsr, 1);
        } else {
            return round($hsr);
        }
    }

    public function isChannelFamilyAmazon(): bool
    {
        return (Channel::isFamilyAmazon($this->getChannelFamily()));
    }

    public function isNotLockableBecauseItIsNew(): bool
    {
        if ($this->hasFirstSaleDate() === false && $this->isSupplyChainCategoryNew()) {
            return true;
        }
        return false;
    }

/**********************************
     *** Unmapped field definitions ***
     **********************************/

    public bool $doConfirmation = false;

    protected ?string $asinsString = null;

    /**
     * @param list<string> $asins
     * @return $this
     */
    public function setAsinsStringFromArray(array $asins): self
    {
        $asins = array_unique($asins);
        $this->asinsString = implode(',', $asins);
        return $this;
    }
    public function getAsinsString(): string
    {
        if (is_null($this->asinsString) && !is_null($this->sfManager)) {
            $this->buildHistoricValues();
        }
        return $this->asinsString ?: '';
    }

    protected ?SalesForecastManager $sfManager = null;
    public function setSalesForecastManager(SalesForecastManager $sfManager): self
    {
        $this->sfManager = $sfManager;
        return $this;
    }

    /**
     * @var array<string|int, float|int>|null
     */
    protected ?array $historicValues = null;

    /**
     * @return array<string|int, float|int>
     */
    protected function buildHistoricValues(): array
    {
        if (!is_null($this->historicValues)) {
            return $this->historicValues;
        }

        if (is_null($this->sfManager)) {
            throw new \RuntimeException('SalesForecastManager not set in SalesForecastGroup');
        }

        $this->historicValues = $this->sfManager->retrieveSalesHistoryForSfg($this);

        return $this->historicValues;
    }

    public function setForecastConfirmationDateToNow(): static
    {
        return $this->setForecastConfirmationDate(new \DateTimeImmutable());
    }

    // less clunky alias for isHasFirstSaleDate()
    public function hasFirstSaleDate(): ?bool
    {
        return $this->isHasFirstSaleDate();
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(targetEntity: Blueprint::class, inversedBy: 'salesForecastGroups')]
    protected Blueprint $blueprint;

    /**
     * @var Collection<integer, SalesForecastDetail>
     */
    #[ORM\OneToMany( mappedBy: 'sfGroup', targetEntity: SalesForecastDetail::class, cascade: ['persist'], indexBy: 'id')]
    protected Collection $sfDetails;

    #[Assert\Length(exactly: 3)]
    #[ORM\Column(type: Types::STRING, length: 3, options: ['default' => 'AMZ'])]
    protected string $channelFamily = 'AMZ';

    #[ORM\Column(type: Types::STRING, length: 3)]
    protected ?string $sfRegion = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    protected User $createdByUser;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    protected ?User $confirmedByUser = null;

    #[ORM\Column(type: Types::DATE_IMMUTABLE)]
    protected \DateTimeImmutable $forecastMadeInMonth;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    protected ?\DateTimeImmutable $forecastConfirmationDate = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    protected bool $unconfirmed = false;

    #[ORM\Column(type: Types::STRING, length: 1, enumType: SupplyChainCategory::class, options: ['default' => 'N'])]
    protected ?SupplyChainCategory $supplyChainCategory = SupplyChainCategory::N_New;

    #[ORM\Column(type: Types::BOOLEAN, nullable:true)]
    protected ?bool $hasFirstSaleDate = null;

    #[ORM\Column(type: Types::DECIMAL, nullable: true, options: ['default' => 0.0, 'precision' => 10, 'scale' => 2])]
    protected ?float $caFourMonthsDelta = null;
//    #[ORM\Column(type: Types::DECIMAL, nullable: true, options: ['default' => 0.0, 'unsigned' => true, 'precision' => 6, 'scale' => 1])]
//    protected ?float $cachedThisMonthPrevFsr = null;
//
    public function __construct()
    {
        $this->sfDetails = new ArrayCollection();
    }

    /***************************************
     *** Over-ridden generated code here ***
     ***************************************/

    public function setForecastMadeInMonth(\DateTimeImmutable $forecastMadeInMonth): self
    {
        $checkDayAndTime = $forecastMadeInMonth->format('d H:i:s');
        if ($checkDayAndTime !== '01 00:00:00') {
            $forecastMadeInMonth = new \DateTimeImmutable($forecastMadeInMonth->format('Y-m-01 00:00:00'));
        }
        $this->forecastMadeInMonth = $forecastMadeInMonth;

        return $this;
    }

    public function getSupplyChainCategory(): ?SupplyChainCategory
    {
        return $this->supplyChainCategory;
    }

    public function setSupplyChainCategory(SupplyChainCategory $supplyChainCategory): static
    {
        $this->supplyChainCategory = $supplyChainCategory;

        return $this;
    }

//    public function getCachedThisMonthPrevFsr(): ?float
//    {
//        return (float)$this->cachedThisMonthPrevFsr;
//    }
//
//    public function setCachedThisMonthPrevFsr(int|float|string|null $cachedThisMonthPrevFsr): static
//    {
//        $this->cachedThisMonthPrevFsr = GravitiqTools::castNullToZeroFloat($cachedThisMonthPrevFsr);
//
//        return $this;
//    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getChannelFamily(): ?string
    {
        return $this->channelFamily;
    }

    public function setChannelFamily(string $channelFamily): static
    {
        $this->channelFamily = $channelFamily;

        return $this;
    }

    public function getSfRegion(): ?string
    {
        return $this->sfRegion;
    }

    public function setSfRegion(string $sfRegion): static
    {
        $this->sfRegion = $sfRegion;

        return $this;
    }

    public function getForecastMadeInMonth(): ?\DateTimeImmutable
    {
        return $this->forecastMadeInMonth;
    }

    public function getForecastConfirmationDate(): ?\DateTimeImmutable
    {
        return $this->forecastConfirmationDate;
    }

    public function setForecastConfirmationDate(?\DateTimeImmutable $forecastConfirmationDate): static
    {
        $this->forecastConfirmationDate = $forecastConfirmationDate;

        return $this;
    }

    public function isUnconfirmed(): ?bool
    {
        return $this->unconfirmed;
    }

    public function setUnconfirmed(bool $unconfirmed): static
    {
        $this->unconfirmed = $unconfirmed;

        return $this;
    }

    public function getBlueprint(): ?Blueprint
    {
        return $this->blueprint;
    }

    public function setBlueprint(?Blueprint $blueprint): static
    {
        $this->blueprint = $blueprint;

        return $this;
    }

    public function getCreatedByUser(): ?User
    {
        return $this->createdByUser;
    }

    public function setCreatedByUser(?User $createdByUser): static
    {
        $this->createdByUser = $createdByUser;

        return $this;
    }

    public function getConfirmedByUser(): ?User
    {
        return $this->confirmedByUser;
    }

    public function setConfirmedByUser(?User $confirmedByUser): static
    {
        $this->confirmedByUser = $confirmedByUser;

        return $this;
    }

    /**
     * @return Collection<int, SalesForecastDetail>
     */
    public function getSfDetails(): Collection
    {
        return $this->sfDetails;
    }

    public function addSfDetail(SalesForecastDetail $sfDetail): static
    {
        if (!$this->sfDetails->contains($sfDetail)) {
            $this->sfDetails->add($sfDetail);
            $sfDetail->setSfGroup($this);
        }

        return $this;
    }

    public function removeSfDetail(SalesForecastDetail $sfDetail): static
    {
        if ($this->sfDetails->removeElement($sfDetail)) {
            // set the owning side to null (unless already changed)
            if ($sfDetail->getSfGroup() === $this) {
                $sfDetail->setSfGroup(null);
            }
        }

        return $this;
    }

    public function isHasFirstSaleDate(): ?bool
    {
        return $this->hasFirstSaleDate;
    }

    public function setHasFirstSaleDate(?bool $hasFirstSaleDate): static
    {
        $this->hasFirstSaleDate = $hasFirstSaleDate;

        return $this;
    }

    public function getCaFourMonthsDelta(): ?float
    {
        return $this->caFourMonthsDelta;
    }

    public function setCaFourMonthsDelta(?float $caFourMonthsDelta): static
    {
        $this->caFourMonthsDelta = $caFourMonthsDelta;

        return $this;
    }
}