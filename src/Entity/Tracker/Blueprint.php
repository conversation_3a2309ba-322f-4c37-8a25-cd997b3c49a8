<?php /** @noinspection PhpUnused */

namespace App\Entity\Tracker;

use App\Domain\Enum\BarcodeType;
use App\Entity\Brand;
use App\Entity\ChannelSku;
use App\Entity\GravitiqBaseEntity;
use App\Entity\MerchantSku;
use App\Repository\Tracker\BlueprintRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Table(name: 'tk_blueprint')]
#[ORM\Index(columns: ['reportingGroup'], name: 'IDX_REPGROUP')]
#[ORM\Entity(repositoryClass: BlueprintRepository::class)]
class Blueprint extends GravitiqBaseEntity
{
    use SoftDeleteableEntity, TimestampableEntity;

    private const string DEFAULT_REPORTING_GROUP = '?';
    public const string ISKU_PATTERN = '/^[A-Z][A-Z]\-[A-Z0-9\-]+$/';

    public function __toString(): string
    {
        return $this->getIsku();
    }

    public function getIskuAndReportingGroup(): string
    {
        $label = $this->getIsku();
        if ($this->getReportingGroup() !== self::DEFAULT_REPORTING_GROUP) {
            $label .= ' (' . $this->getReportingGroup() . ')';
        }
        return $label;
    }

    /**
     * @return BoxPlan[]
     */
    public function getBoxPlans(): array
    {
        $boxPlans = [];
        foreach ($this->getContainerMetas() as $container) {
            if ($container->getBoxPlan()) {
                $boxPlans[] = $container->getBoxPlan();
            }
        }
        return $boxPlans;
    }

    /**
     * @return BoxPlan[]
     */
    public function getBoxPlansIndexedByDescription(): array
    {
        return array_reduce($this->getBoxPlans(), function ($carry, $boxPlan) {
            $carry[$boxPlan->getDescription()] = $boxPlan;
            return $carry;
        }, []);
    }

    /**
     * @param string|null $channelFamily - if null no filtering is done. If set, only ChannelSkus matching that Channel family are returned
     * @return string[]
     */
    public function getUids(?string $channelFamily = null): array
    {
        $uids = [];
        foreach ($this->getChannelSkus() as $channelSku) {
            if (!$channelSku->isMatchToChannelFamily($channelFamily)) {
                continue;
            }
            $uids[$channelSku->getUid()] = $channelSku->getUid();
        }
        return array_keys($uids);
    }

    /**
     * @param string|null $channelFamily - if null no filtering is done. If set, only ChannelSkus matching that Channel family are returned
     * @return string[][] - structured as [uid][channelId] => channelName
     */
    public function getUidsWithChannelNames(?string $channelFamily = null): array
    {
        $uids = [];
        foreach ($this->getChannelSkus() as $channelSku) {
            if (!$channelSku->isMatchToChannelFamily($channelFamily)) {
                continue;
            }
            if (empty($uids[$channelSku->getUid()])) {
                $uids[$channelSku->getUid()] = [];
            }
            $uids[$channelSku->getUid()][$channelSku->getChannel()->getId()] = $channelSku->getChannel()->getInternalName();
        }
        return $uids;
    }

    /**
     * @return string[][]   Structured as [uid => countries]
     */
    public function getUidsWithChannelCountries(?string $channelFamily = null): array
    {
        $uids = [];
        foreach ($this->getChannelSkus() as $channelSku) {
            if (!$channelSku->isMatchToChannelFamily($channelFamily)) {
                continue;
            }
            if (empty($uids[$channelSku->getUid()])) {
                $uids[$channelSku->getUid()] = [];
            }
            $uids[$channelSku->getUid()][$channelSku->getChannel()->getPrimaryCountry()] = $channelSku->getChannel()->getId();
        }

        $returnArray = [];
        foreach ($uids as $uid => $channelCountries) {
            $returnArray[$uid] = array_keys($channelCountries);
        }

        return $returnArray;
    }

    #[Assert\Callback]
    public function validateBarcodeSuitableForType(ExecutionContextInterface $context): void
    {
        if (empty($this->getBarcodeType())) {
            if (empty($this->getBarcode())) {
                return;
            }
            $context->buildViolation('Barcode type must be set if barcode is set')
                ->atPath('barcodeType')
                ->addViolation()
            ;
            return;
        }

        if (empty($this->getBarcode())) {
            $context->buildViolation('Barcode must be set if BarcodeType is set')
                ->atPath('barcode')
                ->addViolation()
            ;
            return;
        }

        if (BarcodeType::OTHER->value === $this->getBarcodeType()) {
            return; // No checks done
        }

        if (BarcodeType::ASIN->value === $this->getBarcodeType()) {
            $pattern = '/^(B[\dA-Z]{9}|\d{9}(X|\d))$/';
            if (preg_match($pattern, $this->getBarcode()) !== 1) {
                $context->buildViolation('Barcode does not look like a valid ASIN')
                    ->atPath('barcode')
                    ->addViolation()
                ;
                return;
            }
        }

        if (BarcodeType::FNSKU->value === $this->getBarcodeType()) {
            $pattern = '/^X0[\dA-Z]{8}$/';
            if (preg_match($pattern, $this->getBarcode()) !== 1) {
                $context->buildViolation('Barcode does not look like a valid FNSKU')
                    ->atPath('barcode')
                    ->addViolation()
                ;
                return;
            }
        }

        if (BarcodeType::EAN->value === $this->getBarcodeType() || BarcodeType::UPC->value === $this->getBarcodeType()) {
            if (BarcodeType::EAN->value === $this->getBarcodeType()) {
                $pattern = '/^\d{13}$/';
                $expectedLength = 13;
                $type = 'EAN-13';
            } else { // (BarcodeType::UPC->value === $this->getBarcodeType())
                $pattern = '/^\d{12}$/';
                $expectedLength = 12;
                $type = 'UPC-A';
            }
            if (preg_match($pattern, $this->getBarcode()) !== 1) {
                if (strlen($this->getBarcode()) !== $expectedLength) {
                    $context->buildViolation("Barcode does not look like a valid $type (should be $expectedLength digits)")
                        ->atPath('barcode')
                        ->addViolation()
                    ;
                } else {
                    $context->buildViolation("Barcode does not look like a valid $type (should be numeric)")
                        ->atPath('barcode')
                        ->addViolation()
                    ;
                }
                return;
            }
            if (!BarcodeType::hasValidUpcChecksum((string)$this->getBarcode())) {
                $context->buildViolation("Barcode does not look like a valid $type (check for typos)")
                    ->atPath('barcode')
                    ->addViolation()
                ;
                /** @noinspection PhpUnnecessaryStopStatementInspection - explicit return makes code more robust if more conditions are added later */
                return;
            }
        }
    }

    /**
     * @param int $maxMonths                        Defaults to current month only
     * @param string|list<string>|null $sfRegions   If set, only SalesForecastGroups in these regions will be considered
     * @return string|null
     */
    public function getHighestScCategory(int $maxMonths=0, string|array|null $sfRegions=null): ?string
    {
        $highestScCategory = null;
        if (empty($sfRegions)) {
            $sfRegions = [];
        } elseif (is_string($sfRegions)) {
            $sfRegions = explode(',', $sfRegions);
        }
        foreach ($this->getSalesForecastGroups() as $sfg) {
            if ($sfg->getSupplyChainCategory() === null) {
                continue;
            }
            try {
                $cutOffDate = new \DateTimeImmutable('first day of this month');
                if ($maxMonths > 0) {
                    $cutOffDate = $cutOffDate->sub(new \DateInterval('P' . $maxMonths . 'M'));
                }
                $cutOffDate = \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $cutOffDate->format('Y-m-01 00:00:00'));
            } catch (\Exception $e) {
                throw new \RuntimeException("Could not process dates in getHighestScCategory($maxMonths)", 0, $e);
            }
            if ($sfg->getForecastMadeInMonth() < $cutOffDate) {
                continue;
            }
            if (!empty($sfRegions) && !in_array($sfg->getSfRegion(), $sfRegions)) {
                continue;
            }
            if ($highestScCategory === null || $sfg->getSupplyChainCategory()->value < $highestScCategory) {
                $highestScCategory = $sfg->getSupplyChainCategory()->value;
            }
        }
        return $highestScCategory;
    }

    public function getBrandAlias(): ?string
    {
        $brandAlias = $this->getBrand()?->getBrandCode();
        if (empty($brandAlias)) {
            $brandAlias = substr($this->getIsku(), 0, 2);
        }
        return $brandAlias;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\ManyToOne(targetEntity: Brand::class, inversedBy: 'blueprints')]
    #[ORM\JoinColumn(nullable: true)]
    protected ?Brand $brand = null;

    /**
     * @var Collection<integer, Stock>
     */
    #[ORM\OneToMany( mappedBy: 'blueprint', targetEntity: Stock::class, indexBy: 'id')]
    protected Collection $stocks;

    /**
     * @var Collection<integer, SalesForecastGroup>
     */
    #[ORM\OneToMany( mappedBy: 'blueprint', targetEntity: SalesForecastGroup::class, indexBy: 'id')]
    protected Collection $salesForecastGroups;

    #[Assert\Regex(
        pattern: Blueprint::ISKU_PATTERN,
        message: 'Only capital letters, numbers and hyphens are allowed. Should start with 2-letter brand code and dash.'
    )]
    #[Assert\Length(max: 33)]
    #[ORM\Column(type: Types::STRING, length: 40, unique: true, nullable: false)]
    protected string $isku;

    #[Assert\Regex(
        pattern: '/^(([A-Z][A-Z]\-[A-Z0-9\-]+)|\?)$/',
        message: 'Only capital letters, numbers and hyphens are allowed. Should start with 2-letter brand code and dash.'
    )]
    #[Assert\Length(max: 40)]
    #[ORM\Column(type: Types::STRING, length: 40, nullable: false, options: ['default' => self::DEFAULT_REPORTING_GROUP])]
    protected string $reportingGroup = self::DEFAULT_REPORTING_GROUP;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: false, options: ['default' => 'CURRENT_TIMESTAMP'])]
    protected \DateTimeImmutable $createDate;

    /**
     * Old (unreadable) internal sku
     */
    #[ORM\Column(type: Types::STRING, length: 160, nullable: true)]
    protected ?string $legacySku = null;

    #[Assert\Regex(
        pattern: '/^[a-zA-Z0-9 :,\.&\'\-\/\(\)\[\]]*$/',
        message: 'Can contain letters, numbers, spaces and the following characters: , . : & \' - / ( ) [ ]'
    )]
    #[ORM\Column(type: Types::TEXT)]
    protected string $productName = '';

    /**
     * Dimension string, in cm, from Suppliers
     */
    #[Assert\Regex(
        pattern: '/^[0-9]+.?[0-9]? ?x ?[0-9]+.?[0-9]? ?x ?[0-9]+.?[0-9]?$/',
        message: 'Three dimensions in cm with an x between each measurement, e.g. 10x12.3x20'
    )]
    #[ORM\Column(type: Types::STRING, length: 30, nullable: true)]
    protected ?string $dimensions = null;

    /**
     * Weight in g, from Suppliers
     */
    #[Assert\Range(min: 0, max: 100000)]
    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $weight = null;

    /**
     * Dimension string, in cm, from Amazon
     */
    #[Assert\Regex(
        pattern: '/^[0-9]+.?[0-9]? ?x ?[0-9]+.?[0-9]? ?x ?[0-9]+.?[0-9]?$/',
        message: 'Three dimensions in cm with an x between each measurement, e.g. 20x12.3x10'
    )]
    #[ORM\Column(type: Types::STRING, length: 30, nullable: true)]
    protected ?string $dimensionsFromAmazon = null;

    /**
     * Weight in g, from Amazon
     */
    #[Assert\Range(min: 0, max: 100000)]
    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $weightFromAmazon = null;

    #[ORM\Column(type: Types::STRING, length: 30, nullable: true)]
    protected ?string $barcode = null;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: true)]
    protected ?string $barcodeType = null;

    #[ORM\Column(type: Types::STRING, length: 150, nullable: true)]
    protected ?string $dirtyBarcode = null;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: true)]
    protected ?string $dirtyBarcodeType = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: false)]
    protected bool $active = true;

    #[ORM\Column(type: Types::BOOLEAN, nullable: false)]
    protected bool $retail = false;

    #[ORM\Column(type: Types::BOOLEAN, nullable: false)]
    protected bool $wholesale = false;

    /**
     * @var Collection<integer, BlueprintContent>
     */
    #[ORM\OneToMany(mappedBy: 'container', targetEntity: BlueprintContent::class, indexBy: 'id')]
    protected Collection $bomChildBlueprints;

    /**
     * @var Collection<integer, BlueprintContent>
     */
    #[ORM\OneToMany(mappedBy: 'content', targetEntity: BlueprintContent::class, indexBy: 'id')]
    protected Collection $bomParentBlueprints;

    /**
     * @var Collection<integer, BoxPlanContents>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BoxPlanContents::class, indexBy: 'id')]
    protected Collection $containerMetas;

    /**
     * @var Collection<integer, SupplierBlueprint>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: SupplierBlueprint::class, indexBy: 'id')]
    protected Collection $supplierBlueprints;

    /**
     * @var Collection<integer, ChannelSku>
     */
    #[ORM\ManyToMany(targetEntity: ChannelSku::class, mappedBy: 'blueprints')]
    protected Collection $channelSkus;

    /**
     * @var Collection<integer, MerchantSku>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: MerchantSku::class, indexBy: 'id')]
    protected Collection $merchantSkus;

    /**
     * @var Collection<integer, BlueprintCogs>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintCogs::class)]
    protected Collection $cogs;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: true)]
    protected ?string $creativeFolder = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $productDescription = null;

    /**
     * @var Collection<integer, BlueprintDocPhotoImage>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocPhotoImage::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $photoImages;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $medicalNotes = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $complianceNotes = null;

    /**
     * @var Collection<integer, BlueprintDocComplianceImage>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocComplianceImage::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $complianceImages;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $requiredLanguageNotes = null;

    /**
     * @var Collection<integer, BlueprintDocRequiredLanguageImage>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocRequiredLanguageImage::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $requiredLanguageImages;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $instructions = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $ingredients = null;

    /**
     * @var Collection<integer, BlueprintDocDataSheetDoc>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocDataSheetDoc::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $dataSheetDocs;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $expiryNotes = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $variationNotes = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $packagingNotes = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $hasIfu = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    // Details of what should be in the IFU
    protected ?string $ifuNotes = null;

    /**
     * @var Collection<integer, BlueprintDocIfuDoc>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocIfuDoc::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $ifuDocs;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $hasInsertCard = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    // Details of what should be shown on the insert/thank you cards. QR codes? URLs? Messaging?
    protected ?string $insertCardNotes = null;

    /**
     * @var Collection<integer, BlueprintDocInsertCardDoc>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocInsertCardDoc::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $insertCardDocs;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    protected ?bool $hasLabels = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    // How many are there? What's on each one? Where are they attached?
    protected ?string $labelNotes = null;

    /**
     * @var Collection<integer, BlueprintDocLabelImage>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocLabelImage::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $labelImages;

    /**
     * @var Collection<integer, BlueprintDocBarcodeDoc>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocBarcodeDoc::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $barcodePdfDocs;

    #[ORM\Column(type: Types::STRING, length: 8, nullable: true)]
    protected ?string $competitor1Asin = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $competitor1Notes = null;

    /**
     * @var Collection<integer, BlueprintDocCompetitor1Image>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocCompetitor1Image::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $competitor1Images;

    #[ORM\Column(type: Types::STRING, length: 8, nullable: true)]
    protected ?string $competitor2Asin = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $competitor2Notes = null;

    /**
     * @var Collection<integer, BlueprintDocCompetitor2Image>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocCompetitor2Image::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $competitor2Images;

    #[ORM\Column(type: Types::STRING, length: 8, nullable: true)]
    protected ?string $competitor3Asin = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $competitor3Notes = null;

    /**
     * @var Collection<integer, BlueprintDocCompetitor3Image>
     */
    #[ORM\OneToMany(mappedBy: 'blueprint', targetEntity: BlueprintDocCompetitor3Image::class, cascade: ['persist', 'remove'], orphanRemoval: true, indexBy: 'id')]
    protected Collection $competitor3Images;

    #[ORM\Column(type: Types::INTEGER, options: ['default' => 0])]
    protected int $gravRank = 0;

    /*******************************************
     *** Modified generated code             ***
     *******************************************/

    public function __construct()
    {
        $this->setCreateDate();
        $this->stocks = new ArrayCollection();
        $this->salesForecastGroups = new ArrayCollection();
        $this->containerMetas = new ArrayCollection();
        $this->supplierBlueprints = new ArrayCollection();
        $this->channelSkus = new ArrayCollection();
        $this->merchantSkus = new ArrayCollection();
        $this->complianceImages = new ArrayCollection();
        $this->requiredLanguageImages = new ArrayCollection();
        $this->photoImages = new ArrayCollection();
        $this->dataSheetDocs = new ArrayCollection();
        $this->ifuDocs = new ArrayCollection();
        $this->insertCardDocs = new ArrayCollection();
        $this->labelImages = new ArrayCollection();
        $this->barcodePdfDocs = new ArrayCollection();
        $this->competitor1Images = new ArrayCollection();
        $this->competitor2Images = new ArrayCollection();
        $this->competitor3Images = new ArrayCollection();
        $this->bomChildBlueprints = new ArrayCollection();
        $this->bomParentBlueprints = new ArrayCollection();
    }

    public function removeStock(Stock $stock): static
    {
        if ($this->stocks->removeElement($stock)) {
            // set the owning side to null (unless already changed)
            if ($stock->getBlueprint() === $this) {
                throw new \InvalidArgumentException('Should not call Blueprint::removeStock() in normal situations because Stock.blueprint is not nullable');
                // $stock->setBlueprint(null);
            }
        }

        return $this;
    }

    public function getIsku(): string
    {
        return $this->isku;
    }

    public function setCreateDate(): self
    {
        $this->createDate = new \DateTimeImmutable();

        return $this;
    }

    public function getHasIfu(): ?bool
    {
        return $this->hasIfu;
    }
    public function getHasInsertCard(): ?bool
    {
        return $this->hasInsertCard;
    }
    public function getHasLabels(): ?bool
    {
        return $this->hasLabels;
    }

    public function setReportingGroup(string $reportingGroup): static
    {
        $reportingGroup = trim($reportingGroup);
        if (empty($reportingGroup)) {
            $reportingGroup = self::DEFAULT_REPORTING_GROUP;
        }

        $this->reportingGroup = $reportingGroup;

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function setIsku(string $isku): static
    {
        $this->isku = $isku;

        return $this;
    }

    public function getReportingGroup(): ?string
    {
        return $this->reportingGroup;
    }

    public function getCreateDate(): ?\DateTimeImmutable
    {
        return $this->createDate;
    }

    public function getLegacySku(): ?string
    {
        return $this->legacySku;
    }

    public function setLegacySku(?string $legacySku): static
    {
        $this->legacySku = $legacySku;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): static
    {
        $this->productName = $productName;

        return $this;
    }

    public function getDimensions(): ?string
    {
        return $this->dimensions;
    }

    public function setDimensions(?string $dimensions): static
    {
        $this->dimensions = $dimensions;

        return $this;
    }

    public function getWeight(): ?int
    {
        return $this->weight;
    }

    public function setWeight(?int $weight): static
    {
        $this->weight = $weight;

        return $this;
    }

    public function getDimensionsFromAmazon(): ?string
    {
        return $this->dimensionsFromAmazon;
    }

    public function setDimensionsFromAmazon(?string $dimensionsFromAmazon): static
    {
        $this->dimensionsFromAmazon = $dimensionsFromAmazon;

        return $this;
    }

    public function getWeightFromAmazon(): ?int
    {
        return $this->weightFromAmazon;
    }

    public function setWeightFromAmazon(?int $weightFromAmazon): static
    {
        $this->weightFromAmazon = $weightFromAmazon;

        return $this;
    }

    public function getBarcode(): ?string
    {
        return $this->barcode;
    }

    public function setBarcode(?string $barcode): static
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function getBarcodeType(): ?string
    {
        return $this->barcodeType;
    }

    public function setBarcodeType(?string $barcodeType): static
    {
        $this->barcodeType = $barcodeType;

        return $this;
    }

    public function getDirtyBarcode(): ?string
    {
        return $this->dirtyBarcode;
    }

    public function setDirtyBarcode(?string $dirtyBarcode): static
    {
        $this->dirtyBarcode = $dirtyBarcode;

        return $this;
    }

    public function getDirtyBarcodeType(): ?string
    {
        return $this->dirtyBarcodeType;
    }

    public function setDirtyBarcodeType(?string $dirtyBarcodeType): static
    {
        $this->dirtyBarcodeType = $dirtyBarcodeType;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): static
    {
        $this->active = $active;

        return $this;
    }

    public function isRetail(): ?bool
    {
        return $this->retail;
    }

    public function setRetail(bool $retail): static
    {
        $this->retail = $retail;

        return $this;
    }

    public function isWholesale(): ?bool
    {
        return $this->wholesale;
    }

    public function setWholesale(bool $wholesale): static
    {
        $this->wholesale = $wholesale;

        return $this;
    }

    public function getCreativeFolder(): ?string
    {
        return $this->creativeFolder;
    }

    public function setCreativeFolder(?string $creativeFolder): static
    {
        $this->creativeFolder = $creativeFolder;

        return $this;
    }

    public function getProductDescription(): ?string
    {
        return $this->productDescription;
    }

    public function setProductDescription(?string $productDescription): static
    {
        $this->productDescription = $productDescription;

        return $this;
    }

    public function getMedicalNotes(): ?string
    {
        return $this->medicalNotes;
    }

    public function setMedicalNotes(?string $medicalNotes): static
    {
        $this->medicalNotes = $medicalNotes;

        return $this;
    }

    public function getComplianceNotes(): ?string
    {
        return $this->complianceNotes;
    }

    public function setComplianceNotes(?string $complianceNotes): static
    {
        $this->complianceNotes = $complianceNotes;

        return $this;
    }

    public function getRequiredLanguageNotes(): ?string
    {
        return $this->requiredLanguageNotes;
    }

    public function setRequiredLanguageNotes(?string $requiredLanguageNotes): static
    {
        $this->requiredLanguageNotes = $requiredLanguageNotes;

        return $this;
    }

    public function getInstructions(): ?string
    {
        return $this->instructions;
    }

    public function setInstructions(?string $instructions): static
    {
        $this->instructions = $instructions;

        return $this;
    }

    public function getIngredients(): ?string
    {
        return $this->ingredients;
    }

    public function setIngredients(?string $ingredients): static
    {
        $this->ingredients = $ingredients;

        return $this;
    }

    public function getExpiryNotes(): ?string
    {
        return $this->expiryNotes;
    }

    public function setExpiryNotes(?string $expiryNotes): static
    {
        $this->expiryNotes = $expiryNotes;

        return $this;
    }

    public function getVariationNotes(): ?string
    {
        return $this->variationNotes;
    }

    public function setVariationNotes(?string $variationNotes): static
    {
        $this->variationNotes = $variationNotes;

        return $this;
    }

    public function getPackagingNotes(): ?string
    {
        return $this->packagingNotes;
    }

    public function setPackagingNotes(?string $packagingNotes): static
    {
        $this->packagingNotes = $packagingNotes;

        return $this;
    }

    public function isHasIfu(): ?bool
    {
        return $this->hasIfu;
    }

    public function setHasIfu(?bool $hasIfu): static
    {
        $this->hasIfu = $hasIfu;

        return $this;
    }

    public function getIfuNotes(): ?string
    {
        return $this->ifuNotes;
    }

    public function setIfuNotes(?string $ifuNotes): static
    {
        $this->ifuNotes = $ifuNotes;

        return $this;
    }

    public function isHasInsertCard(): ?bool
    {
        return $this->hasInsertCard;
    }

    public function setHasInsertCard(?bool $hasInsertCard): static
    {
        $this->hasInsertCard = $hasInsertCard;

        return $this;
    }

    public function getInsertCardNotes(): ?string
    {
        return $this->insertCardNotes;
    }

    public function setInsertCardNotes(?string $insertCardNotes): static
    {
        $this->insertCardNotes = $insertCardNotes;

        return $this;
    }

    public function isHasLabels(): ?bool
    {
        return $this->hasLabels;
    }

    public function setHasLabels(?bool $hasLabels): static
    {
        $this->hasLabels = $hasLabels;

        return $this;
    }

    public function getLabelNotes(): ?string
    {
        return $this->labelNotes;
    }

    public function setLabelNotes(?string $labelNotes): static
    {
        $this->labelNotes = $labelNotes;

        return $this;
    }

    public function getCompetitor1Asin(): ?string
    {
        return $this->competitor1Asin;
    }

    public function setCompetitor1Asin(?string $competitor1Asin): static
    {
        $this->competitor1Asin = $competitor1Asin;

        return $this;
    }

    public function getCompetitor1Notes(): ?string
    {
        return $this->competitor1Notes;
    }

    public function setCompetitor1Notes(?string $competitor1Notes): static
    {
        $this->competitor1Notes = $competitor1Notes;

        return $this;
    }

    public function getCompetitor2Asin(): ?string
    {
        return $this->competitor2Asin;
    }

    public function setCompetitor2Asin(?string $competitor2Asin): static
    {
        $this->competitor2Asin = $competitor2Asin;

        return $this;
    }

    public function getCompetitor2Notes(): ?string
    {
        return $this->competitor2Notes;
    }

    public function setCompetitor2Notes(?string $competitor2Notes): static
    {
        $this->competitor2Notes = $competitor2Notes;

        return $this;
    }

    public function getCompetitor3Asin(): ?string
    {
        return $this->competitor3Asin;
    }

    public function setCompetitor3Asin(?string $competitor3Asin): static
    {
        $this->competitor3Asin = $competitor3Asin;

        return $this;
    }

    public function getCompetitor3Notes(): ?string
    {
        return $this->competitor3Notes;
    }

    public function setCompetitor3Notes(?string $competitor3Notes): static
    {
        $this->competitor3Notes = $competitor3Notes;

        return $this;
    }

    public function getBrand(): ?Brand
    {
        return $this->brand;
    }

    public function setBrand(?Brand $brand): static
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return Collection<int, Stock>
     */
    public function getStocks(): Collection
    {
        return $this->stocks;
    }

    public function addStock(Stock $stock): static
    {
        if (!$this->stocks->contains($stock)) {
            $this->stocks->add($stock);
            $stock->setBlueprint($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, SalesForecastGroup>
     */
    public function getSalesForecastGroups(): Collection
    {
        return $this->salesForecastGroups;
    }

    public function addSalesForecastGroup(SalesForecastGroup $salesForecastGroup): static
    {
        if (!$this->salesForecastGroups->contains($salesForecastGroup)) {
            $this->salesForecastGroups->add($salesForecastGroup);
            $salesForecastGroup->setBlueprint($this);
        }

        return $this;
    }

    public function removeSalesForecastGroup(SalesForecastGroup $salesForecastGroup): static
    {
        if ($this->salesForecastGroups->removeElement($salesForecastGroup)) {
            // set the owning side to null (unless already changed)
            if ($salesForecastGroup->getBlueprint() === $this) {
                $salesForecastGroup->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintContent>
     */
    public function getBomChildBlueprints(): Collection
    {
        return $this->bomChildBlueprints;
    }

    public function addBomChildBlueprint(BlueprintContent $bomChildBlueprint): static
    {
        if (!$this->bomChildBlueprints->contains($bomChildBlueprint)) {
            $this->bomChildBlueprints->add($bomChildBlueprint);
            $bomChildBlueprint->setContainer($this);
        }

        return $this;
    }

    public function removeBomChildBlueprint(BlueprintContent $bomChildBlueprint): static
    {
        if ($this->bomChildBlueprints->removeElement($bomChildBlueprint)) {
            // set the owning side to null (unless already changed)
            if ($bomChildBlueprint->getContainer() === $this) {
                $bomChildBlueprint->setContainer(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintContent>
     */
    public function getBomParentBlueprints(): Collection
    {
        return $this->bomParentBlueprints;
    }

    public function addBomParentBlueprint(BlueprintContent $bomParentBlueprint): static
    {
        if (!$this->bomParentBlueprints->contains($bomParentBlueprint)) {
            $this->bomParentBlueprints->add($bomParentBlueprint);
            $bomParentBlueprint->setContent($this);
        }

        return $this;
    }

    public function removeBomParentBlueprint(BlueprintContent $bomParentBlueprint): static
    {
        if ($this->bomParentBlueprints->removeElement($bomParentBlueprint)) {
            // set the owning side to null (unless already changed)
            if ($bomParentBlueprint->getContent() === $this) {
                $bomParentBlueprint->setContent(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BoxPlanContents>
     */
    public function getContainerMetas(): Collection
    {
        return $this->containerMetas;
    }

    public function addContainerMeta(BoxPlanContents $containerMeta): static
    {
        if (!$this->containerMetas->contains($containerMeta)) {
            $this->containerMetas->add($containerMeta);
            $containerMeta->setBlueprint($this);
        }

        return $this;
    }

    public function removeContainerMeta(BoxPlanContents $containerMeta): static
    {
        if ($this->containerMetas->removeElement($containerMeta)) {
            // set the owning side to null (unless already changed)
            if ($containerMeta->getBlueprint() === $this) {
                $containerMeta->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, SupplierBlueprint>
     */
    public function getSupplierBlueprints(): Collection
    {
        return $this->supplierBlueprints;
    }

    public function addSupplierBlueprint(SupplierBlueprint $supplierBlueprint): static
    {
        if (!$this->supplierBlueprints->contains($supplierBlueprint)) {
            $this->supplierBlueprints->add($supplierBlueprint);
            $supplierBlueprint->setBlueprint($this);
        }

        return $this;
    }

    public function removeSupplierBlueprint(SupplierBlueprint $supplierBlueprint): static
    {
        if ($this->supplierBlueprints->removeElement($supplierBlueprint)) {
            // set the owning side to null (unless already changed)
            if ($supplierBlueprint->getBlueprint() === $this) {
                $supplierBlueprint->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ChannelSku>
     */
    public function getChannelSkus(): Collection
    {
        return $this->channelSkus;
    }

    public function addChannelSku(ChannelSku $channelSku): static
    {
        if (!$this->channelSkus->contains($channelSku)) {
            $this->channelSkus->add($channelSku);
            $channelSku->addBlueprint($this);
        }

        return $this;
    }

    public function removeChannelSku(ChannelSku $channelSku): static
    {
        if ($this->channelSkus->removeElement($channelSku)) {
            $channelSku->removeBlueprint($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, MerchantSku>
     */
    public function getMerchantSkus(): Collection
    {
        return $this->merchantSkus;
    }

    public function addMerchantSku(MerchantSku $merchantSku): static
    {
        if (!$this->merchantSkus->contains($merchantSku)) {
            $this->merchantSkus->add($merchantSku);
            $merchantSku->setBlueprint($this);
        }

        return $this;
    }

    public function removeMerchantSku(MerchantSku $merchantSku): static
    {
        if ($this->merchantSkus->removeElement($merchantSku)) {
            // set the owning side to null (unless already changed)
            if ($merchantSku->getBlueprint() === $this) {
                $merchantSku->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocPhotoImage>
     */
    public function getPhotoImages(): Collection
    {
        return $this->photoImages;
    }

    public function addPhotoImage(BlueprintDocPhotoImage $photoImage): static
    {
        if (!$this->photoImages->contains($photoImage)) {
            $this->photoImages->add($photoImage);
            $photoImage->setBlueprint($this);
        }

        return $this;
    }

    public function removePhotoImage(BlueprintDocPhotoImage $photoImage): static
    {
        if ($this->photoImages->removeElement($photoImage)) {
            // set the owning side to null (unless already changed)
            if ($photoImage->getBlueprint() === $this) {
                $photoImage->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocComplianceImage>
     */
    public function getComplianceImages(): Collection
    {
        return $this->complianceImages;
    }

    public function addComplianceImage(BlueprintDocComplianceImage $complianceImage): static
    {
        if (!$this->complianceImages->contains($complianceImage)) {
            $this->complianceImages->add($complianceImage);
            $complianceImage->setBlueprint($this);
        }

        return $this;
    }

    public function removeComplianceImage(BlueprintDocComplianceImage $complianceImage): static
    {
        if ($this->complianceImages->removeElement($complianceImage)) {
            // set the owning side to null (unless already changed)
            if ($complianceImage->getBlueprint() === $this) {
                $complianceImage->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocRequiredLanguageImage>
     */
    public function getRequiredLanguageImages(): Collection
    {
        return $this->requiredLanguageImages;
    }

    public function addRequiredLanguageImage(BlueprintDocRequiredLanguageImage $requiredLanguageImage): static
    {
        if (!$this->requiredLanguageImages->contains($requiredLanguageImage)) {
            $this->requiredLanguageImages->add($requiredLanguageImage);
            $requiredLanguageImage->setBlueprint($this);
        }

        return $this;
    }

    public function removeRequiredLanguageImage(BlueprintDocRequiredLanguageImage $requiredLanguageImage): static
    {
        if ($this->requiredLanguageImages->removeElement($requiredLanguageImage)) {
            // set the owning side to null (unless already changed)
            if ($requiredLanguageImage->getBlueprint() === $this) {
                $requiredLanguageImage->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocDataSheetDoc>
     */
    public function getDataSheetDocs(): Collection
    {
        return $this->dataSheetDocs;
    }

    public function addDataSheetDoc(BlueprintDocDataSheetDoc $dataSheetDoc): static
    {
        if (!$this->dataSheetDocs->contains($dataSheetDoc)) {
            $this->dataSheetDocs->add($dataSheetDoc);
            $dataSheetDoc->setBlueprint($this);
        }

        return $this;
    }

    public function removeDataSheetDoc(BlueprintDocDataSheetDoc $dataSheetDoc): static
    {
        if ($this->dataSheetDocs->removeElement($dataSheetDoc)) {
            // set the owning side to null (unless already changed)
            if ($dataSheetDoc->getBlueprint() === $this) {
                $dataSheetDoc->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocIfuDoc>
     */
    public function getIfuDocs(): Collection
    {
        return $this->ifuDocs;
    }

    public function addIfuDoc(BlueprintDocIfuDoc $ifuDoc): static
    {
        if (!$this->ifuDocs->contains($ifuDoc)) {
            $this->ifuDocs->add($ifuDoc);
            $ifuDoc->setBlueprint($this);
        }

        return $this;
    }

    public function removeIfuDoc(BlueprintDocIfuDoc $ifuDoc): static
    {
        if ($this->ifuDocs->removeElement($ifuDoc)) {
            // set the owning side to null (unless already changed)
            if ($ifuDoc->getBlueprint() === $this) {
                $ifuDoc->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocInsertCardDoc>
     */
    public function getInsertCardDocs(): Collection
    {
        return $this->insertCardDocs;
    }

    public function addInsertCardDoc(BlueprintDocInsertCardDoc $insertCardDoc): static
    {
        if (!$this->insertCardDocs->contains($insertCardDoc)) {
            $this->insertCardDocs->add($insertCardDoc);
            $insertCardDoc->setBlueprint($this);
        }

        return $this;
    }

    public function removeInsertCardDoc(BlueprintDocInsertCardDoc $insertCardDoc): static
    {
        if ($this->insertCardDocs->removeElement($insertCardDoc)) {
            // set the owning side to null (unless already changed)
            if ($insertCardDoc->getBlueprint() === $this) {
                $insertCardDoc->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocLabelImage>
     */
    public function getLabelImages(): Collection
    {
        return $this->labelImages;
    }

    public function addLabelImage(BlueprintDocLabelImage $labelImage): static
    {
        if (!$this->labelImages->contains($labelImage)) {
            $this->labelImages->add($labelImage);
            $labelImage->setBlueprint($this);
        }

        return $this;
    }

    public function removeLabelImage(BlueprintDocLabelImage $labelImage): static
    {
        if ($this->labelImages->removeElement($labelImage)) {
            // set the owning side to null (unless already changed)
            if ($labelImage->getBlueprint() === $this) {
                $labelImage->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocBarcodeDoc>
     */
    public function getBarcodePdfDocs(): Collection
    {
        return $this->barcodePdfDocs;
    }

    public function addBarcodePdfDoc(BlueprintDocBarcodeDoc $barcodePdfDoc): static
    {
        if (!$this->barcodePdfDocs->contains($barcodePdfDoc)) {
            $this->barcodePdfDocs->add($barcodePdfDoc);
            $barcodePdfDoc->setBlueprint($this);
        }

        return $this;
    }

    public function removeBarcodePdfDoc(BlueprintDocBarcodeDoc $barcodePdfDoc): static
    {
        if ($this->barcodePdfDocs->removeElement($barcodePdfDoc)) {
            // set the owning side to null (unless already changed)
            if ($barcodePdfDoc->getBlueprint() === $this) {
                $barcodePdfDoc->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocCompetitor1Image>
     */
    public function getCompetitor1Images(): Collection
    {
        return $this->competitor1Images;
    }

    public function addCompetitor1Image(BlueprintDocCompetitor1Image $competitor1Image): static
    {
        if (!$this->competitor1Images->contains($competitor1Image)) {
            $this->competitor1Images->add($competitor1Image);
            $competitor1Image->setBlueprint($this);
        }

        return $this;
    }

    public function removeCompetitor1Image(BlueprintDocCompetitor1Image $competitor1Image): static
    {
        if ($this->competitor1Images->removeElement($competitor1Image)) {
            // set the owning side to null (unless already changed)
            if ($competitor1Image->getBlueprint() === $this) {
                $competitor1Image->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocCompetitor2Image>
     */
    public function getCompetitor2Images(): Collection
    {
        return $this->competitor2Images;
    }

    public function addCompetitor2Image(BlueprintDocCompetitor2Image $competitor2Image): static
    {
        if (!$this->competitor2Images->contains($competitor2Image)) {
            $this->competitor2Images->add($competitor2Image);
            $competitor2Image->setBlueprint($this);
        }

        return $this;
    }

    public function removeCompetitor2Image(BlueprintDocCompetitor2Image $competitor2Image): static
    {
        if ($this->competitor2Images->removeElement($competitor2Image)) {
            // set the owning side to null (unless already changed)
            if ($competitor2Image->getBlueprint() === $this) {
                $competitor2Image->setBlueprint(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, BlueprintDocCompetitor3Image>
     */
    public function getCompetitor3Images(): Collection
    {
        return $this->competitor3Images;
    }

    public function addCompetitor3Image(BlueprintDocCompetitor3Image $competitor3Image): static
    {
        if (!$this->competitor3Images->contains($competitor3Image)) {
            $this->competitor3Images->add($competitor3Image);
            $competitor3Image->setBlueprint($this);
        }

        return $this;
    }

    public function removeCompetitor3Image(BlueprintDocCompetitor3Image $competitor3Image): static
    {
        if ($this->competitor3Images->removeElement($competitor3Image)) {
            // set the owning side to null (unless already changed)
            if ($competitor3Image->getBlueprint() === $this) {
                $competitor3Image->setBlueprint(null);
            }
        }

        return $this;
    }

    public function getGravRank(): int
    {
        return $this->gravRank;
    }

    public function setGravRank(int $gravRank): static
    {
        $this->gravRank = $gravRank;

        return $this;
    }
}