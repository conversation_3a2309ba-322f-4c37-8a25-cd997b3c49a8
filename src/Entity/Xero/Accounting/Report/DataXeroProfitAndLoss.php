<?php

namespace App\Entity\Xero\Accounting\Report;

use App\Entity\MagicMutatorTrait;
use App\Repository\Xero\Accounting\Report\DataXeroProfitAndLossRepository;
use App\Tools\GravitiqTools;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * @method self setValueAtReportDateFromDecimal(string|float|int|null $value) - via MagicMutatorTrait
 */
#[ORM\Table(name: 'data_xero_acc_pl')]
#[ORM\Entity(repositoryClass: DataXeroProfitAndLossRepository::class)]
class DataXeroProfitAndLoss extends DataXeroBaseEntity
{
    use MagicMutatorTrait;
    public const int CURRENT_PERIOD_COLUMN = 1;

    public function getValueAtReportDateAsInt(): ?int
    {
        if (is_null($this->valueAtReportDate)) {
            return null;
        }
        return (int) $this->valueAtReportDate;
    }

    /*************************
     *** Field definitions ***
     *************************/

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    protected ?string $valueAtReportDate = null;

    /*******************************************
     *** Modified generated code             ***
     *******************************************/

    public function setValueAtReportDate(null|int|string $valueAtReportDate): static
    {
        $this->valueAtReportDate = GravitiqTools::castToNullableString($valueAtReportDate);

        return $this;
    }

    /*******************************************
     *** Generated code only below this line ***
     *******************************************/

    public function getValueAtReportDate(): ?string
    {
        return $this->valueAtReportDate;
    }
}
