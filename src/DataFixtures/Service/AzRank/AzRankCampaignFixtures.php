<?php

namespace App\DataFixtures\Service\AzRank;

use App\DataFixtures\GravitiqFixture;
use App\Entity\AzRank\ApiRequestAzRank;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

class AzRankCampaignFixtures extends GravitiqFixture
{
    public const int REQUEST_ID_1 = 101;
    public const int REQUEST_ID_2 = 102;
    public const int EXPECTED_RECORD_COUNT = 1;

    public static function getRequestId1(): int
    {
        return self::REQUEST_ID_1;
    }

    public static function getRequestId2(): int
    {
        return self::REQUEST_ID_2;
    }

    public static function getExpectedRecordCount(): int
    {
        return self::EXPECTED_RECORD_COUNT;
    }

    /**
     * @return array<int, mixed>
     */
    public static function getExpectedRecords(): array
    {
        return [
            self::getRequestId1() => [
                'brand'         => 'Test Brand',
                'country'       => 'US',
                'brandManager'  => 'Test Manager 1',
                'asin'          => 'SAMPLE1234',
                'product'       => 'test-product',
                'spreadsheetId' => '1-TeSt11',
                'projects' => [
                    'spreadsheetId' => '1-TeSt11',
                    'gid' => '342291192',
                ]
            ],
            self::getRequestId2() => [
                'brand'         => 'Test Brand',
                'country'       => 'US',
                'brandManager'  => 'Test Manager 2',
                'asin'          => 'SAMPLE1234',
                'product'       => 'test-product',
                'spreadsheetId' => '1-TeSt11',
                'projects' => [
                    'spreadsheetId' => '1-TeSt11',
                    'gid' => '342291192',
                ]
            ],
        ];
    }

    /**
     * @param EntityManager $manager
     */
    public function load(ObjectManager $manager): void
    {
        $requestId1 = self::getRequestId1();
        $requestId2 = self::getRequestId2();
        $requestType = ApiRequestAzRank::REQUEST_TYPE_AZ_RANK_CAMPAIGN;

        $sqlStatement = <<<SQL
            INSERT INTO `api_request_azr` 
                (`id`, `campaignId`, `spreadsheetId`, `gid`, `onDashboard`, `requestType`, `requestDate`, `status`, `recordCount`, `savedFileSize`, `filename`, `fileSystemName`) 
            VALUES 
                ({$requestId1}, NULL, '1-TeSt11', NULL, NULL, '{$requestType}', '2024-07-22 09:07:32', 'DONE', NULL, 4545, 'fixtures/azRank-1-azrCampaign-2024-07-22.json', 'azRankLocal'),
                ({$requestId2}, NULL, '1-TeSt11', NULL, NULL, '{$requestType}', '2024-07-23 09:07:32', 'DONE', NULL, 4545, 'fixtures/azRank-2-azrCampaign-2024-07-23.json', 'azRankLocal')
            ;
        SQL;

        self::runSql($sqlStatement, $manager);

    }
}