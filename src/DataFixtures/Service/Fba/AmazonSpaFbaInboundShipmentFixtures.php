<?php

namespace App\DataFixtures\Service\Fba;

use App\DataFixtures\GravitiqFixture;
use App\Entity\Spa\Fba\SpaFbaInboundShipment;
use App\Entity\SpaApiRequest\ApiRequestSpaFbaInbound;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

class AmazonSpaFbaInboundShipmentFixtures extends GravitiqFixture
{
    public const int REQUEST_ID_INITIAL = 200;
    public const int REQUEST_ID_UPDATED = 201;
    public const int REQUEST_ID_EMPTY = 202;
    public const int REQUEST_ID_FAIL = 203;

    public static function getRequestClass(): string
    {
        return ApiRequestSpaFbaInbound::class;
    }

    /**
     * @return array<int, array{filename: string, requestDate: string}>
     */
    public static function getInboundShipmentsFile(): array
    {
        return [
            self::REQUEST_ID_INITIAL => [
                'filename'   => 'fixtures/inbound_shipments_initial.json',
                'requestDate' => '2024-01-01',
            ],
            self::REQUEST_ID_UPDATED   => [
                'filename'   => 'fixtures/inbound_shipments_updated.json',
                'requestDate' => '2024-03-01',
            ],
            self::REQUEST_ID_EMPTY   => [
                'filename'   => 'fixtures/inbound_shipments_empty.json',
                'requestDate' => '2024-03-01',
            ],
            self::REQUEST_ID_FAIL   => [
                'filename'   => 'fixtures/inbound_shipments_fail.json',
                'requestDate' => '2024-03-01',
            ],
        ];
    }

    /**
     * @param int $requestId
     * @return array<string, array{
     *     shipmentId: string,
     *     shipmentName: string,
     *     shipmentFromName: string,
     *     shipmentFromAddressLine1: string,
     *     shipmentFromAddressLine2: string|null,
     *     shipmentFromDistrictOrCounty: string,
     *     shipmentFromCity: string,
     *     shipmentFromProvince: string,
     *     shipmentFromCountryCode: string,
     *     shipmentFromPostalCode: string,
     *     destinationFulfillmentCenterId: string,
     *     shipmentStatus: string,
     *     labelPrepType: string,
     *     boxContentsSource: string,
     *     areCasesRequired: bool|null,
     *     dateWorking: string|null,
     *     dateReadyToShip: string|null,
     *     dateShipped: string|null,
     *     dateReceiving: string|null,
     *     dateCancelled: string|null,
     *     dateDeleted: string|null,
     *     dateClosed: string|null,
     *     dateError: string|null,
     *     dateInTransit: string|null,
     *     dateDelivered: string|null,
     *     dateCheckedIn: string|null,
     * }>
     */
    public static function getExpectedValuesForRequest(int $requestId): array
    {
        $expectedValues = [];

        if ($requestId === self::REQUEST_ID_INITIAL) {
            $expectedValues = [
                'FBA15JQL4QW7' => [
                    'azFbaShipmentId'                => 'FBA15JQL4QW7',
                    'shipmentName'                   => 'DA_Rain_Air(14/11/2024 03:41)-BHX8',
                    'shipmentFromName'               => 'ZHENGZHOU WECARE GLOVE COMPANY LTD',
                    'shipmentFromAddressLine1'       => 'NO. 28 DONGFENG ROAD,ZHENGZHOU,CHINA',
                    'shipmentFromAddressLine2'       => null, // Not provided in the data
                    'shipmentFromDistrictOrCounty'   => 'China',
                    'shipmentFromCity'               => 'Henan',
                    'shipmentFromProvince'           => 'Henan',
                    'shipmentFromCountryCode'        => 'CN',
                    'shipmentFromPostalCode'         => '450000',
                    'destinationFulfillmentCenterId' => 'LBA4',
                    'shipmentStatus'                 => 'RECEIVING',
                    'labelPrepType'                  => 'SELLER_LABEL',
                    'boxContentsSource'              => 'INTERACTIVE',
                    'areCasesRequired'               => null, // Not provided in the data
                    'dateWorking'                    => null,
                    'dateReadyToShip'                => null,
                    'dateShipped'                    => null,
                    'dateReceiving'                  => '2024-01-01 00:00:00',
                    'dateCancelled'                  => null,
                    'dateDeleted'                    => null,
                    'dateClosed'                     => null,
                    'dateError'                      => null,
                    'dateInTransit'                  => null,
                    'dateDelivered'                  => null,
                    'dateCheckedIn'                  => null,
                ],
            ];
        }

        if ($requestId === self::REQUEST_ID_UPDATED) {
            $expectedValues = [
                'FBA15JQL4QW7' => [
                    'azFbaShipmentId'                => 'FBA15JQL4QW7',
                    'shipmentName'                   => 'DA_Rain_Air(14/11/2024 03:41)-BHX8',
                    'shipmentFromName'               => 'Suzhou Gazelle Sporting Goods Co. Ltd',
                    'shipmentFromAddressLine1'       => 'Chengbei #2, Haiguan Road, Yangshe Town',
                    'shipmentFromAddressLine2'       => 'Zhangjiagang, Suzhou City, Jiangsu Prov. China',
                    'shipmentFromDistrictOrCounty'   => 'China',
                    'shipmentFromCity'               => 'Jiangsu',
                    'shipmentFromProvince'           => 'Jiangsu',
                    'shipmentFromCountryCode'        => 'CN',
                    'shipmentFromPostalCode'         => '215600',
                    'destinationFulfillmentCenterId' => 'BHX4',
                    'shipmentStatus'                 => 'CANCELLED',
                    'labelPrepType'                  => 'SELLER_LABEL',
                    'boxContentsSource'              => 'INTERACTIVE',
                    'areCasesRequired'               => null, // Not provided in the data
                    'dateWorking'                    => null,
                    'dateReadyToShip'                => null,
                    'dateShipped'                    => null,
                    'dateReceiving'                  => '2024-01-01 00:00:00',
                    'dateCancelled'                  => '2024-03-01 00:00:00', // shipmentStatus has changed, so date should be set to match the requestDate
                    'dateDeleted'                    => null,
                    'dateClosed'                     => null,
                    'dateError'                      => null,
                    'dateInTransit'                  => null,
                    'dateDelivered'                  => null,
                    'dateCheckedIn'                  => null,
                ],
            ];
        }

        if ($requestId === self::REQUEST_ID_FAIL) {
            // Has data, but just to make sure it's parsing correctly the expected shipments
            $expectedValues = [
                'FBA15J4B7KL4' => [],
                'FBA15J7NYLHC' => [],
                'FBA15JJQQFCJ' => [],
                'FBA15JJRK1RB' => [],
                'FBA15JK5N2GQ' => [],
                'FBA15JK5RBBT' => [],
                'FBA15JL4PLNM' => [],
                'FBA15JL4QWP8' => [],
                'FBA15JLSGD7T' => [],
                'FBA15JN05PHY' => [],
                'FBA15JP4Y20G' => [],
                'FBA15JQP9B53' => [],
                'FBA15JQPS120' => [],
                'FBA15JQPXBCX' => [],
                'FBA15JQT6FZG' => [],
                'FBA15JQTNDXR' => [],
                'FBA15JQVGNCW' => [],
                'FBA15JR1WKWT' => [],
                'FBA15JRJ3V39' => [],
                'FBA15JS6HYTC' => [],
                'FBA15JSC95S7' => [],
                'FBA15JT05G7Y' => [],
                'FBA15JT5CY9P' => [],
                'FBA15JTVSMRF' => [],
            ];
        }

        return $expectedValues;
    }

    /**
     * @param int $currentRequestId
     * @return int|null
     */
    public static function getPreviousRequestId(int $currentRequestId): ?int
    {
        return match ($currentRequestId) {
            self::REQUEST_ID_UPDATED => self::REQUEST_ID_INITIAL,
            default => null,
        };
    }

    /**
     * @param int $requestId
     * @return array<class-string, int>
     */
    public static function getExpectedEntityCountIncrease(int $requestId): array
    {
        $expectedValues = self::getExpectedValuesForRequest($requestId);
        $previousRequestId = self::getPreviousRequestId($requestId);

        if ($previousRequestId !== null) {
            $previousAsins = array_keys(self::getExpectedValuesForRequest($previousRequestId));
            $currentAsins = array_keys($expectedValues);
            $newAsins = array_diff($currentAsins, $previousAsins);
            $increase = count($newAsins);
        } else {
            $increase = count($expectedValues);
        }

        return [
            SpaFbaInboundShipment::class => $increase,
        ];
    }

    /**
     * @param EntityManager $manager
     */
    public function load(ObjectManager $manager): void
    {
        $requestFiles = self::getInboundShipmentsFile();

        $sqlStatements = [];
        foreach ($requestFiles as $requestId => $fileData) {
            $sqlStatements[] = <<<SQL
            INSERT INTO api_request_spa_fba_inbound (`id`, `accountProfileId`, `accountAmazonId`, `requestType`, `requestDate`, `perPage`, `pageNumber`, `parseDate`, `status`, `fileSystemName`, `filename`) 
            VALUES 
                ({$requestId}, 2, 2, 'inboundShipments', '{$fileData['requestDate']}', 100, 1, NULL, 'OK', 'amazonFbaInboundLocal', '{$fileData['filename']}');
            SQL;
        }

        self::runSql($sqlStatements, $manager);
    }
}