<?php

namespace App\DataFixtures\Service\ReportParser;

use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

class AmazonSpaReportsFbaSnsFixtures extends BaseSpaReportFixtures
{
    const int REPORT_ID_PERFORMANCE_1 = 101;
    const int REPORT_ID_PERFORMANCE_2 = 102;
    const int REPORT_ID_PERFORMANCE_MULTI = 104;
    const int REPORT_ID_FORECAST = 103;

    public static function getReportIdPerformance1(): int
    {
        return self::REPORT_ID_PERFORMANCE_1;
    }

    public static function getReportIdPerformance2(): int
    {
        return self::REPORT_ID_PERFORMANCE_2;
    }

    public static function getReportIdPerformanceMulti(): int
    {
        return self::REPORT_ID_PERFORMANCE_MULTI;
    }

    public static function getReportIdForecast(): int
    {
        return self::REPORT_ID_FORECAST;
    }

    /**
     * @return array<int, mixed>
     */
    public static function getExpectedDataForForecastReportAssertion(): array
    {
        return [
            [
                'offerState'                  => 'Enabled',
                'snapshotDate'                => '2024-03-18T17:04:45+00:00',
                'sku'                         => 'GS-BATH-BOMBS-1PK-2305',
                'asin'                        => 'B0C5NS3W9H',
                'estAvgSnsDiscountNext8Weeks' => null,
                'country'                     => 'CA',
                'activeSubscriptions'         => 0,
                'week1StartDate'              => '2024-03-17T00:00:00+00:00',
                'scheduledSnsUnitsWeek1'      => 0,
                'scheduledSnsUnitsWeek2'      => 0,
                'scheduledSnsUnitsWeek3'      => 0,
                'scheduledSnsUnitsWeek4'      => 0,
                'scheduledSnsUnitsWeek5'      => 0,
                'scheduledSnsUnitsWeek6'      => 0,
                'scheduledSnsUnitsWeek7'      => 0,
                'scheduledSnsUnitsWeek8'      => 0,
            ],
            [
                'offerState'                  => 'Enabled',
                'snapshotDate'                => '2024-03-18T17:04:45+00:00',
                'sku'                         => 'GS-BATH-BOMBS-1PK-2305',
                'asin'                        => 'B0C5NS3W9H',
                'estAvgSnsDiscountNext8Weeks' => 10.00,
                'country'                     => 'US',
                'activeSubscriptions'         => 6,
                'week1StartDate'              => '2024-03-17T00:00:00+00:00',
                'scheduledSnsUnitsWeek1'      => 0,
                'scheduledSnsUnitsWeek2'      => 0,
                'scheduledSnsUnitsWeek3'      => 0,
                'scheduledSnsUnitsWeek4'      => 0,
                'scheduledSnsUnitsWeek5'      => 4,
                'scheduledSnsUnitsWeek6'      => 0,
                'scheduledSnsUnitsWeek7'      => 0,
                'scheduledSnsUnitsWeek8'      => 1,
            ],
        ];
    }

    /**
     * @return array<int, mixed>
     */
    public static function getExpectedDataForPerformanceReportAssertion(): array
    {
        return [
            [
                'offerState'      => 'Enabled',
                'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                'asin'            => 'B0C4G1YZY4',
                'country'         => 'US',
                'weekStartDate'   => '2024-03-10T00:00:00+00:00',
                'snsUnitsShipped' => 2,
                'oosRate'         => 0,
                'snsSalePrice'    => 1369,
                'snsDiscount'     => 15.00,
            ],
            [
                'offerState'      => 'Enabled',
                'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                'asin'            => 'B0C4G1YZY4',
                'country'         => 'US',
                'weekStartDate'   => '2024-03-03T00:00:00+00:00',
                'snsUnitsShipped' => 7,
                'oosRate'         => 12,
                'snsSalePrice'    => 1375,
                'snsDiscount'     => 13.57,
            ],
            [
                'offerState'      => 'Enabled',
                'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                'asin'            => 'B0C4G1YZY4',
                'country'         => 'US',
                'weekStartDate'   => '2024-02-25T00:00:00+00:00',
                'snsUnitsShipped' => 14,
                'oosRate'         => 7,
                'snsSalePrice'    => 1362,
                'snsDiscount'     => 11.79,
            ],
            [
                'offerState'      => 'Enabled',
                'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                'asin'            => 'B0C4G1YZY4',
                'country'         => 'US',
                'weekStartDate'   => '2024-02-18T00:00:00+00:00',
                'snsUnitsShipped' => 7,
                'oosRate'         => 12,
                'snsSalePrice'    => 1384,
                'snsDiscount'     => 12.14,
            ],
        ];
    }

    /**
     * @return array{created: array<int, mixed>, updated: array<int, mixed>}
     */
    public static function getExpectedDataForParseAndUpdatePerformanceReport(): array
    {
        return [
            'created' => [
                [
                    'offerState'      => 'Enabled',
                    'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                    'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                    'asin'            => 'B0C4G1YZY4',
                    'country'         => 'US',
                    'weekStartDate'   => '2024-03-24T00:00:00+00:00',
                    'snsUnitsShipped' => 4,
                    'oosRate'         => 5,
                    'snsSalePrice'    => 2456,
                    'snsDiscount'     => 10.00,
                ],
                [
                    'offerState'      => 'Enabled',
                    'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                    'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                    'asin'            => 'B0C4G1YZY4',
                    'country'         => 'US',
                    'weekStartDate'   => '2024-03-17T00:00:00+00:00',
                    'snsUnitsShipped' => 10,
                    'oosRate'         => 13,
                    'snsSalePrice'    => 1111,
                    'snsDiscount'     => 5.67,
                ],
            ],
            'updated' => [
                [
                    'offerState'      => 'Enabled',
                    'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                    'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                    'asin'            => 'B0C4G1YZY4',
                    'country'         => 'US',
                    'weekStartDate'   => '2024-03-10T00:00:00+00:00',
                    'snsUnitsShipped' => 21,
                    'oosRate'         => 14,
                    'snsSalePrice'    => 5012,
                    'snsDiscount'     => 19.91,
                ],
                [
                    'offerState'      => 'Enabled',
                    'snapshotDate'    => '2024-03-18T16:59:04+00:00',
                    'sku'             => 'GS-MASK-UNDEREYE-GLD-6PR',
                    'asin'            => 'B0C4G1YZY4',
                    'country'         => 'US',
                    'weekStartDate'   => '2024-03-03T00:00:00+00:00',
                    'snsUnitsShipped' => 6,
                    'oosRate'         => 11,
                    'snsSalePrice'    => 1284,
                    'snsDiscount'     => 11.14,
                ],
            ]
        ];
    }

    /**
     * @param EntityManager $manager
     */
    public static function loadAmazonReportSpa(ObjectManager $manager): void
    {
        $reportIdPerformance1 = self::getReportIdPerformance1();
        $reportIdPerformance2 = self::getReportIdPerformance2();
        $reportIdForecast = self::getReportIdForecast();

        $sqlStatements = [
            <<<EOF
    INSERT INTO `amazon_report_spa` 
           (`id`,`accountprofileid`,`marketplace`,`reporttype`,`status`,`savedfilesize`,`scheduled`,`requestdate`,`startDate`,`endDate`,`parsedate`,`azreportid`,`azreportdocumentid`,`filesystemname`,`filename`)
    VALUES ('{$reportIdPerformance1}','2','US','GET_FBA_SNS_PERFORMANCE_DATA','DONE','0','0','2024-01-01 00:00:00','2024-01-01 00:00:00','2024-01-01 00:00:00',NULL,'TEST','TEST','amazonReportsLocal','fixtures/GS_US-730365-GET_FBA_SNS_PERFORMANCE_DATA-S230101.tsv'),
           ('{$reportIdPerformance2}','2','US','GET_FBA_SNS_PERFORMANCE_DATA','DONE','0','0','2024-01-01 00:00:00','2024-01-01 00:00:00','2024-01-01 00:00:00',NULL,'TEST','TEST','amazonReportsLocal','fixtures/GS_US-730366-GET_FBA_SNS_PERFORMANCE_DATA-S230101.tsv'),
           ('{$reportIdForecast}','2','US','GET_FBA_SNS_FORECAST_DATA','DONE','0','0','2024-01-01 00:00:00','2024-01-01 00:00:00','2024-01-01 00:00:00',NULL,'TEST','TEST','amazonReportsLocal','fixtures/GS_US-730368-GET_FBA_SNS_FORECAST_DATA-S231201.tsv')
        ;
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    /**
     * @param EntityManager $manager
     */
    public static function loadAmazonReportWithMultipleEntries(ObjectManager $manager): void
    {
        $reportId1 = self::getReportIdPerformanceMulti();

        $sqlStatements = [
            <<<EOF
                INSERT INTO `amazon_report_spa` 
                       (`id`,`accountProfileId`,`marketplace`,`reportType`,`status`,`requestdate`,`startDate`,`endDate`,`azreportid`,`fileSystemName`,`filename`)
                VALUES ('{$reportId1}','2','US','GET_FBA_SNS_PERFORMANCE_DATA','DONE','2024-06-04','2024-05-01 00:00:00','2024-06-01 00:00:00','TEST','amazonReportsLocal','fixtures/XX-104-GET_FBA_SNS_PERFORMANCE_DATA-multiple-entries.tsv')
                    ;
            EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}