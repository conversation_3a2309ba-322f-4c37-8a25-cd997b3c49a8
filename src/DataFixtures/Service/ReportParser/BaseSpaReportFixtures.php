<?php

namespace App\DataFixtures\Service\ReportParser;

use App\DataFixtures\GravitiqFixture;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

abstract class BaseSpaReportFixtures extends GravitiqFixture
{
    const int REPORT_ID_UK = 101;
    const int REPORT_ID_US = 102;
    const int REPORT_ID_NL = 103;
    const int REPORT_ID_PL = 104;
    const int REPORT_ID_SE = 105;
    const int REPORT_ID_MX = 106;
    const int REPORT_ID_FR = 107;
    const int REPORT_ID_IT = 108;
    const int REPORT_ID_DE = 109;
    const int REPORT_ID_ES = 110;
    const int REPORT_ID_CA = 111;
    const int REPORT_ID_TR = 112;
    const int REPORT_ID_JP = 113;

    /**
     * @throws \Exception
     */
    public function load(ObjectManager $manager): void
    {
    }

    public static function getReportIdUK(): int
    {
        return self::REPORT_ID_UK;
    }

    public static function getReportIdUS(): int
    {
        return self::REPORT_ID_US;
    }

    public static function getReportIdSE(): int
    {
        return self::REPORT_ID_SE;
    }

    public static function getReportIdPL(): int
    {
        return self::REPORT_ID_PL;
    }

    public static function getReportIdNL(): int
    {
        return self::REPORT_ID_NL;
    }

    public static function getReportIdMX(): int
    {
        return self::REPORT_ID_MX;
    }

    public static function getReportIdIT(): int
    {
        return self::REPORT_ID_IT;
    }

    public static function getReportIdFR(): int
    {
        return self::REPORT_ID_FR;
    }

    public static function getReportIdES(): int
    {
        return self::REPORT_ID_ES;
    }

    public static function getReportIdDE(): int
    {
        return self::REPORT_ID_DE;
    }

    public static function getReportIdCA(): int
    {
        return self::REPORT_ID_CA;
    }

    public static function getReportIdTR(): int
    {
        return self::REPORT_ID_TR;
    }

    public static function getReportIdJP(): int
    {
        return self::REPORT_ID_JP;
    }

    public static function loadAccountAmazon(EntityManager $manager): void
    {
        $sqlStatements = [
            <<<EOF
    INSERT INTO account_amazon 
        (id, companyId, brandCode, region, accountName, accountNameInSellerCentral, roleArn, awsAccessKeyId, awsSecretAccessKey, lwaClientId, lwaClientSecret, lwaRefreshToken, aaaRefreshToken, sqsNotificationQueueRoot, crawlAccountUsername, crawlAccountPassword, crawlAccountOtpSeed, crawlCaptcha)
    VALUES 
        (3, 1, 'ZZ', 'APA', 'Test', 'Test Limited', 'test', 'test', 'test', 'test', 'test', 'test', 'test', null, null, null, null, null);
EOF,
        ];
        self::runSql($sqlStatements, $manager);
    }

    public static function loadAccountProfileSpa(EntityManager $manager): void
    {
        $sqlStatements = [
            <<<EOF
    INSERT INTO account_profile_spa 
        (id, accountAmazonId, alias, active, countryCode, currencyCode, accountName, timezone)
    VALUES
        (101, 1, 'ZZ_NL', 1, 'NL', 'EUR', 'Test', 'Europe/Amsterdam'),
        (102, 1, 'ZZ_PL', 1, 'PL', 'PLN', 'Test', 'Europe/Warsaw'),
        (103, 1, 'ZZ_SE', 1, 'SE', 'SEK', 'Test', 'Europe/Stockholm'),
        (104, 2, 'ZZ_MX', 1, 'MX', 'MXN', 'Test', 'America/Los_Angeles'),
        (108, 1, 'ZZ_ES', 1, 'ES', 'EUR', 'Test', 'Europe/Paris'),
        (109, 1, 'ZZ_TR', 1, 'TR', 'TRY', 'Test', 'Europe/Istanbul'),
        (110, 3, 'ZZ_JP', 1, 'JP', 'JPY', 'Test', 'Asia/Tokyo')
    ;
EOF,
        ];
        self::runSql($sqlStatements, $manager);
    }


    abstract public static function loadAmazonReportSpa(ObjectManager $manager): void;
}