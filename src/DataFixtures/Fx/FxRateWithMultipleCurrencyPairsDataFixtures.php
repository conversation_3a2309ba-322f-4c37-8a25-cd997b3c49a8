<?php

declare(strict_types=1);

namespace App\DataFixtures\Fx;

use App\Entity\Fx\Currency;
use App\Entity\Fx\FxRate;
use App\Tests\Mother\ParsedResponseMother;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class FxRateWithMultipleCurrencyPairsDataFixtures extends Fixture
{
    /**
     * @throws \Exception
     */
    public function load(ObjectManager $manager): void
    {
        $currencyPairs = [
            ['sourceCurrency' => Currency::USD, 'targetCurrency' => Currency::CAD],
            ['sourceCurrency' => Currency::USD, 'targetCurrency' => Currency::EUR],
            ['sourceCurrency' => Currency::USD, 'targetCurrency' => Currency::GBP],
        ];

        foreach ($currencyPairs as $currencyPair) {
            foreach (ParsedResponseMother::getThreeMonthDataParsed() as $fxRate) {
                $fxRate = new FxRate(
                    null,
                    $currencyPair['sourceCurrency'],
                    $currencyPair['targetCurrency'],
                    $fxRate['validFromDate'],
                    $fxRate['validBeforeDate'],
                    $fxRate['fetchDate'],
                    $fxRate['fxRate'],
                    $fxRate['inverseFxRate'],
                    $fxRate['period']
                );

                $manager->persist($fxRate);
            }
        }

        $manager->flush();
    }
}
