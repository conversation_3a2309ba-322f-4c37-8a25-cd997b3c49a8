<?php

namespace App\DataFixtures\Aaa;

use App\DataFixtures\GravitiqFixture;
use App\Entity\Aaa\ApiRequestAaa;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

class AaaListSbAdsFixtures extends GravitiqFixture
{
    const int REQUEST_ID_MOON1 = 101;
    const int REQUEST_ID_MOON2 = 102;
    const int REQUEST_ID_SUN1 = 103;
    const int REQUEST_ID_SUN2 = 104;
    const int REQUEST_ID_STAR = 105;

    public static function getRequestIdMoon1(): int
    {
        return self::REQUEST_ID_MOON1;
    }

    public static function getRequestIdMoon2(): int
    {
        return self::REQUEST_ID_MOON2;
    }

    public static function getRequestIdSun1(): int
    {
        return self::REQUEST_ID_SUN1;
    }

    public static function getRequestIdSun2(): int
    {
        return self::REQUEST_ID_SUN2;
    }

    public static function getRequestIdStar(): int
    {
        return self::REQUEST_ID_STAR;
    }

    /**
     * @return list<int>
     */
    public static function getSbAdsRequestIdsForBasicParsing(): array
    {
        return [
            self::getRequestIdStar(),
        ];
    }

    /**
     * @return array<int, mixed>
     */
    public static function getExpectedCollectionForBasicParsing(): array
    {
        return [
            [
                'adGroupId' => '194652826359733',
                'adId' => '150918487899761',
                'data' => [
                    'campaignId' => '12602547138169',
                    'landingPageAsins' => null,
                    'landingPageType' => 'DETAIL_PAGE',
                    'landingPageUrl' => 'https://www.amazon.com/dp/B01DZ7C502',
                    'name' => "Ad - 8/25/2022 10:59:46",
                    'state' => 'ENABLED',
                ]
            ],
            [
                'adGroupId' => '144248743679277775',
                'adId' => null,
                'data' => [
                    'campaignId' => '144373165255706434',
                    'landingPageAsins' => null,
                    'landingPageType' => 'STORE',
                    'landingPageUrl' => 'https://www.amazon.com/stores/page/BF706C7B-D815-4DF8-934D-ED87B2A2885A',
                    'name' => null,
                    'state' => 'ENABLED',
                ]
            ]
        ];
    }

    /**
     * @return array<int, array{createdFromRequest: int, initialName: string, updatedFromRequest: int, updatedName: string}>
     */
    public static function getSbAdsRequests(): array
    {
        return [
            [
                'createdFromRequest' => self::getRequestIdMoon1(),
                'initialName'        => 'Ad Sb 1',
                'updatedFromRequest' => self::getRequestIdMoon2(),
                'updatedName'        => 'Updated - Ad Sb 1',
            ],
            [
                'createdFromRequest' => self::getRequestIdSun1(),
                'initialName'        => 'Ad Sb - 2',
                'updatedFromRequest' => self::getRequestIdSun2(),
                'updatedName'        => 'Updated - Ad Sb - 2',
            ]
        ];
    }

    /**
     * @param EntityManager $manager
     */
    public function load(ObjectManager $manager): void
    {
        $requestIdMoon1 = self::getRequestIdMoon1();
        $requestIdMoon2 = self::getRequestIdMoon2();
        $requestIdSun1 = self::getRequestIdSun1();
        $requestIdSun2 = self::getRequestIdSun2();
        $requestIdStar = self::getRequestIdStar();
        $requestType = ApiRequestAaa::REQUEST_TYPE_SB_ADS_LIST;

        $sqlStatements = [
            <<<EOF
    INSERT INTO api_request_aaa (`id`, `accountProfileId`, `accountAmazonId`, `status`, `requestType`, `marketplace`, `requestDate`, `perPage`, `pageNumber`, `parseDate`, `recordCount`, `savedFileSize`, `filename`, `fileSystemName`,`filterType`, `filter`)
    VALUES 
        ({$requestIdMoon1}, 1, 1, 'DONE', '{$requestType}', 'US', '2023-10-10 16:16:46', 100, 1, NULL, NULL, NULL, 'fixtures/GS_US_sell-200-sb-ads-R240313.json', 'amazonRequestsLocal', 'None', 'None'),
        ({$requestIdMoon2}, 1, 1, 'DONE', '{$requestType}', 'US', '2023-10-10 16:16:46', 100, 1, NULL, NULL, NULL, 'fixtures/GS_US_sell-201-sb-ads-R240313.json', 'amazonRequestsLocal', 'None', 'None'),
        ({$requestIdSun1}, 1, 1, 'DONE', '{$requestType}', 'US', '2023-10-10 16:16:46', 100, 1, NULL, NULL, NULL, 'fixtures/GS_US_sell-202-sb-ads-R240313.json', 'amazonRequestsLocal', 'None', 'None'),
        ({$requestIdSun2}, 1, 1, 'DONE', '{$requestType}', 'US', '2023-10-10 16:16:46', 100, 1, NULL, NULL, NULL, 'fixtures/GS_US_sell-203-sb-ads-R240313.json', 'amazonRequestsLocal', 'None', 'None'),
        ({$requestIdStar}, 1, 1, 'DONE', '{$requestType}', 'US', '2023-10-10 16:16:46', 100, 1, NULL, NULL, NULL, 'fixtures/GS_US_sell-184-sb-ads-R240328.json', 'amazonRequestsLocal', 'None', 'None')
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}