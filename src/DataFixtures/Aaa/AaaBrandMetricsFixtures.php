<?php

namespace App\DataFixtures\Aaa;

use App\DataFixtures\GravitiqFixture;
use Doctrine\Persistence\ObjectManager;

class AaaBrandMetricsFixtures extends GravitiqFixture
{
    const int REPORT_ID_SUN1 = 101;
    const int REPORT_ID_MOON1 = 102;
    const int REPORT_ID_STAR1 = 103;
    const int REPORT_ID_SUN2 = 104;
    const int REPORT_ID_MOON2 = 105;
    const int REPORT_ID_STAR2 = 106;

    public static function getReportIdSun1(): int
    {
        return self::REPORT_ID_SUN1;
    }

    public static function getReportIdMoon1(): int
    {
        return self::REPORT_ID_MOON1;
    }

    public static function getReportIdStar1(): int
    {
        return self::REPORT_ID_STAR1;
    }

    public static function getReportIdSun2(): int
    {
        return self::REPORT_ID_SUN2;
    }

    public static function getReportIdMoon2(): int
    {
        return self::REPORT_ID_MOON2;
    }

    public static function getReportIdStar2(): int
    {
        return self::REPORT_ID_STAR2;
    }

    /**
     * @return array<int, array{
     *     categoryNodeName: string,
     *     createdFromReport: int,
     *     initialBrandedSearchesOnly: int,
     *     updatedFromReport: int,
     *     lastReport: int,
     *     updatedBrandedSearchesOnly: int,
     *     updateParserOutput: array{int, int, int, int}
     * }>
     */
    public static function getBrandMetricsReports(): array
    {
        return [
            [
                'categoryNodeName'           => 'Camping & Hiking',
                'createdFromReport'          => self::getReportIdSun1(),
                'initialBrandedSearchesOnly' => 261,
                'updatedFromReport'          => self::getReportIdSun2(),
                'updatedBrandedSearchesOnly' => 261,
                'lastReport'                 => self::getReportIdSun1(),
                'updateParserOutput'         => [1, 0, 0, 1],
            ],
            [
                'categoryNodeName'           => 'Strength Training Devices',
                'createdFromReport'          => self::getReportIdMoon1(),
                'initialBrandedSearchesOnly' => 196,
                'updatedFromReport'          => self::getReportIdMoon2(),
                'updatedBrandedSearchesOnly' => 196,
                'lastReport'                 => self::getReportIdMoon1(),
                'updateParserOutput'         => [1, 0, 0, 1],
            ],
            [
                'categoryNodeName'           => 'Thigh & Hip Strengtheners',
                'createdFromReport'          => self::getReportIdStar1(),
                'initialBrandedSearchesOnly' => 240,
                'updatedFromReport'          => self::getReportIdStar2(),
                'updatedBrandedSearchesOnly' => 100,
                'lastReport'                 => self::getReportIdStar2(),
                'updateParserOutput'         => [1, 0, 1, 0],
            ]
        ];
    }

    public function load(ObjectManager $manager): void
    {
        $reportIdSun1 = self::getReportIdSun1();
        $reportIdMoon1 = self::getReportIdMoon1();
        $reportIdStar1 = self::getReportIdStar1();
        $reportIdSun2 = self::getReportIdSun2();
        $reportIdMoon2 = self::getReportIdMoon2();
        $reportIdStar2 = self::getReportIdStar2();

        $sqlStatements = [
            <<<EOF
    INSERT INTO amazon_report_aaa (`id`, `accountProfileId`, `marketplace`, `reportType`, `azReportType`, `status`, `scheduled`, `requestDate`, `parseDate`, `reportDate`, `startDate`, `endDate`, `azReportId`, `fileSystemName`, `filename`, `metricGroup`)
    VALUES 
        ({$reportIdSun1}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '1', 'amazonReportsLocal', 'fixtures/DA_US_sell-001-BrandMetrics-S231001-E231001.json', NULL),
        ({$reportIdMoon1}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '2', 'amazonReportsLocal', 'fixtures/DA_US_sell-002-BrandMetrics-S231001-E231001.json', NULL),
        ({$reportIdStar1}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '3', 'amazonReportsLocal', 'fixtures/DA_US_sell-003-BrandMetrics-S231001-E231001.json', NULL),
        ({$reportIdSun2}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '1', 'amazonReportsLocal', 'fixtures/DA_US_sell-004-BrandMetrics-S231001-E231001.json', NULL),
        ({$reportIdMoon2}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '2', 'amazonReportsLocal', 'fixtures/DA_US_sell-005-BrandMetrics-S231001-E231001.json', NULL),
        ({$reportIdStar2}, 1, 'US', 'BrandMetrics', 'brandMetrics', 'SUCCESS', 0, '2023-11-22 00:00:00', NULL, NULL, NULL, NULL, '3', 'amazonReportsLocal', 'fixtures/DA_US_sell-006-BrandMetrics-S231001-E231001.json', NULL)
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}