<?php

namespace App\DataFixtures\Repository\Rainforest;

use App\DataFixtures\GravitiqFixture;
use App\Entity\Brand;
use App\Entity\Rainforest\ApiRequestRainforest;
use App\Entity\Rainforest\ScheduleProduct;
use Doctrine\Persistence\ObjectManager;

class ScheduleProductFixtures extends GravitiqFixture
{
    public const string ASIN1 = 'B012345678';
    /**
     * @throws \Exception
     */
    public function load(ObjectManager $manager): void
    {
    }

    public static function loadScheduleProduct(ObjectManager $manager): void
    {
        $schedule1 = new ScheduleProduct();
        $schedule1->setCountryCode('US');
        $schedule1->setHoursBetweenRuns(24);
        $schedule1->setAsin(self::ASIN1);
        $manager->persist($schedule1);

        $schedule5 = new ScheduleProduct();
        $schedule5->setCountryCode('UK');
        $schedule5->setHoursBetweenRuns(24);
        $schedule5->setAsin(self::ASIN1);
        $manager->persist($schedule5);

        // Save the ScheduleProduct objects to the database
        $manager->flush();

    }
}