<?php

namespace App\DataFixtures\Repository;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class AmazonReportAaaRepositoryFixtures extends Fixture
{
    const int PROFILE_ID_TO_TEST = 1;

    const string REPORT_ID_READY_TO_PARSE = 'ready to parse';
    const string REPORT_ID_UNEXPECTED_ZERO = 'unexpected zero';
    const string REPORT_ID_PARSED_OK = 'parsed ok';

    /**
     * @throws \Exception
     */
    public function load(ObjectManager $manager): void
    {
        $aaaProfileId = self::PROFILE_ID_TO_TEST;
        $reportIds = [
            self::REPORT_ID_READY_TO_PARSE,
            self::REPORT_ID_UNEXPECTED_ZERO,
            self::REPORT_ID_PARSED_OK,
        ];

        $sqlStatements = [
            <<<EOF
INSERT INTO amazon_report_aaa (id, accountProfileId, marketplace, reportType, `status`, parseDate, filename, fileSize, recordCount, azReportId)
VALUES (1, {$aaaProfileId}, 'UK', 'CampaignSp', 'SUCCESS', NULL,         'file1', 100, 0, '{$reportIds[0]}') 
     , (2, {$aaaProfileId}, 'UK', 'CampaignSp', 'SUCCESS', '2023-01-01', 'file1', 100, 0, '{$reportIds[1]}') 
     , (3, {$aaaProfileId}, 'UK', 'CampaignSp', 'SUCCESS', '2023-01-01', 'file1', 100, 5, '{$reportIds[2]}') 
;
EOF,
        ];

        $connection = $manager->getConnection();
        foreach ($sqlStatements as $sql) {
            $connection->executeQuery($sql);
        }

        $manager->flush();
    }
}