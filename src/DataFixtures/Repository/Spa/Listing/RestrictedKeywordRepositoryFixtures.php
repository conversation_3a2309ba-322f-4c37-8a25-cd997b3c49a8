<?php

namespace App\DataFixtures\Repository\Spa\Listing;

use App\DataFixtures\GravitiqFixture;
use Doctrine\Persistence\ObjectManager;

class RestrictedKeywordRepositoryFixtures extends GravitiqFixture
{
    const int RK_ID_ONE = 1;
    const int RK_ID_TWO = 2;
    const int RK_ID_THREE = 3;
    const int RK_ID_FOUR = 4;

    /**
     * @throws \Exception
     */
    public function load(ObjectManager $manager): void
    {
    }

    public static function loadRestrictedKeywords(ObjectManager $manager): void
    {
        $rk1Id = self::RK_ID_ONE;
        $rk2Id = self::RK_ID_TWO;
        $rk3Id = self::RK_ID_THREE;
        $rk4Id = self::RK_ID_FOUR;

        $sqlStatements = [
            <<<EOF
INSERT INTO spa_listing_restricted_kw (id, newtonEntry, marketplace, amzVideoAdsOnly)
VALUES ({$rk1Id}, 'cat', 'ALL', 0), 
       ({$rk2Id}, 'catVideo', 'ALL', 1), 
       ({$rk3Id}, 'chat', 'FR', 0), 
       ({$rk4Id}, 'chatVideo', 'FR', 1);
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}