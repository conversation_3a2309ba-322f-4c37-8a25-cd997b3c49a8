<?php

namespace App\DataFixtures\Command\Monday;

use App\DataFixtures\GravitiqFixture;
use Doctrine\Persistence\ObjectManager;

class MondayCheckCreativeTaskCommentsCommandFixtures extends GravitiqFixture
{
    public const int MONDAY_CREATIVE_TASK_ID = 1;
    public const int MONDAY_TEMPLATE_ID = 100;

    public static function getIdForMondayTemplate(): int
    {
        return self::MONDAY_TEMPLATE_ID;
    }

    public static function getIdForMondayCreativeTask(): int
    {
        return self::MONDAY_CREATIVE_TASK_ID;
    }

    public function load(ObjectManager $manager): void
    {
    }

    public static function loadAllRecords(ObjectManager $manager): void
    {
        self::loadMondayCreativeTemplate($manager);
        self::loadMondayCreativeTaskRecord($manager);
        self::loadMondayCreativeTaskBlueprintRecord($manager);
        self::loadMondayCreativeTaskMerchantSkuRecord($manager);
        self::loadMondayCreativeTaskChannelSkuRecord($manager);
    }

    public static function loadMondayCreativeTemplate(ObjectManager $manager): void
    {
        $mondayTemplateId = self::getIdForMondayTemplate();

        $sqlStatements = [
            <<<EOF
INSERT INTO monday_template (id, boardId, itemId, dateCreated, itemName) 
VALUES ('{$mondayTemplateId}', '1234567890', '1234567890', '2024-01-01 01:23:45', 'test item')
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function loadMondayCreativeTaskRecord(ObjectManager $manager): void
    {
        $mondayTaskId = self::getIdForMondayCreativeTask();
        $mondayTemplateId = self::getIdForMondayTemplate();

        $sqlStatements = [
            <<<EOF
INSERT INTO mon_creative_task (id, mondayId, marketplace, reportingGroup, status, dateCreated, mondayUrl, amazonCompliance, newListing, confirmedPostedOnMonday, productUrl, itemName, comment, commentOnTiming, templateId, brandId, authorId, googleDoc, draft) 
VALUES ('{$mondayTaskId}', '5949116303', 'US', null, 'DONE', '2024-01-29 11:28:14', 'https://gravitiq.monday.com/boards/5555908593/pulses/5949116303', 0, 0, 0, 'https://www.google.com/', '⚠️ BL-OLD-GEL-CELL-5PR,BL-OLD-BOF-CLR-2PR [Other Creative Task] - only use as a last resort, Compliance Message Test 11', 'Creative Request Details - Comment Box', 'Turnaround Time', $mondayTemplateId, 1, 1, null, 0)
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function loadMondayCreativeTaskBlueprintRecord(ObjectManager $manager): void
    {
        $mondayTaskId = self::getIdForMondayCreativeTask();

        $sqlStatements = [
            <<<EOF
INSERT INTO mon_join_creative_blueprint (mondayCreativeTaskId, blueprintId) 
VALUES ('{$mondayTaskId}', 1),('{$mondayTaskId}', 2)
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function loadMondayCreativeTaskMerchantSkuRecord(ObjectManager $manager): void
    {
        $mondayTaskId = self::getIdForMondayCreativeTask();

        $sqlStatements = [
            <<<EOF
INSERT INTO mon_join_creative_ms (mondayCreativeTaskId, msId) 
VALUES ('{$mondayTaskId}', 1),('{$mondayTaskId}', 2)
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function loadMondayCreativeTaskChannelSkuRecord(ObjectManager $manager): void
    {
        $mondayTaskId = self::getIdForMondayCreativeTask();

        $sqlStatements = [
            <<<EOF
INSERT INTO mon_join_creative_cs (mondayCreativeTaskId, csId) 
VALUES ('{$mondayTaskId}', 1),('{$mondayTaskId}', 2)
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function updateMondayCreativeTaskRecord(ObjectManager $manager): void
    {
        $mondayTaskId = self::getIdForMondayCreativeTask();

        $sqlStatements = [
            <<<EOF
UPDATE mon_creative_task
SET amazonCompliance = 1, newListing = 1, googleDoc = 'https://www.google.com/'
WHERE id = '{$mondayTaskId}'
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}