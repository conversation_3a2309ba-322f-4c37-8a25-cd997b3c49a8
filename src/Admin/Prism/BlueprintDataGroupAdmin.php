<?php

namespace App\Admin\Prism;

use App\Entity\Prism\BlueprintDataGroup;
use App\Entity\Prism\DataGroupDefinition;
use App\Entity\Tracker\Blueprint;

/**
 * @phpstan-extends BaseDataGroupAdmin<BlueprintDataGroup>
 */
class BlueprintDataGroupAdmin extends BaseDataGroupAdmin
{
    protected function getConnectionType(): string
    {
        return DataGroupDefinition::CONNECTION_LEVEL_PRODUCT;
    }

    protected function getConnectionLevelLabel(): string
    {
        return 'blueprint';
    }

    protected function getConnectionEntityFormFields(): array
    {
        return [
            'blueprint' => [
                'property' => 'isku',
                'btn_add' => false,
                'required' => true,
                'minimum_input_length' => 3,
                'to_string_callback' => fn(Blueprint $entity) => $entity->getIsku(),
            ],
            'inheritFromBlueprint' => [
                'property' => 'isku',
                'btn_add' => false,
                'required' => false,
                'minimum_input_length' => 3,
                'to_string_callback' => fn(Blueprint $entity) => $entity->getIsku(),
            ],
        ];
    }
}