<?php

namespace App\Admin\Raptor;

use App\Admin\GravitiqBaseAdmin;
use Sonata\AdminBundle\Route\RouteCollectionInterface;

/**
 * @phpstan-ignore-next-line - this is a pseudo-Admin, so valid that there's no type defined
 */
class RaptorAdmin extends GravitiqBaseAdmin
{
    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'raptor';
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->clear();
        $collection->add('daily', 'daily/{reportDate}', ['reportDate' => 'yesterday']);
        $collection->add('asinStock', 'asinStock/{profileAlias}');
        $collection->add('aaaAsinQueryReport', 'aaaAsinQuery');
        $collection->add('dsrHeatmapReport', 'dsrHeatmap');
        $collection->add('b2bSalesReport', 'b2bSales');
        $collection->add('dsrHeatmapBrandCountry', 'dsrHeatmap/{brand}/{country}/{daysBefore}', ['daysBefore' => 14]);
        $collection->add('profitCalculator', 'profitCalculator');
        $collection->add('retrieveProfitCalculatorDynamicFieldValues', 'retrieveProfitCalculatorDynamicFieldValues/{channelSku}/{profileAlias}');
    }

    protected function configureDashboardActions(array $actions): array
    {
        $actions['daily'] = [
            'label' => 'Daily reports',
            'url' => $this->generateUrl('daily'),
            'icon' => 'fa fa-chart-line',
        ];

        $actions['dsrHeatmapReport'] = [
            'label' => 'DSR Heatmap',
            'url' => $this->generateUrl('dsrHeatmapReport'),
            'icon' => 'fa fa-fire',
        ];

        $actions['asinStock'] = [
            'label' => 'ASIN stock',
            'url' => $this->generateUrl('asinStock', ['profileAlias' => 'DA_UK']),
            'icon' => 'fa fa-box',
        ];

        $actions['asinQuery'] = [
            'label' => 'Search Term Report',
            'url' => $this->generateUrl('aaaAsinQueryReport'),
            'icon' => 'fa fa-search',
        ];

        $actions['profitCalculator'] = [
            'label' => 'Profitability Calculator',
            'url' => $this->generateUrl('profitCalculator'),
            'icon' => 'fa fa-calculator',
        ];

        $actions['b2bSalesReport'] = [
            'label' => 'B2B Sales Report',
            'url' => $this->generateUrl('b2bSalesReport'),
            'icon' => 'fa fa-briefcase',
        ];

        return $actions;
    }
}