<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240305224943 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add mon_creative_task.typeOfWork column';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE mon_creative_task ADD typeOfWork VARCHAR(50) DEFAULT \'\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE mon_creative_task DROP typeOfWork');
    }
}
