<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240814153807 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename table evergreen_coupon to spa_evergreen_coupon';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE spa_evergreen_coupon (id INT AUTO_INCREMENT NOT NULL, linkedCouponId INT DEFAULT NULL, description VARCHAR(255) NOT NULL, isExpired TINYINT(1) NOT NULL, plannedEndDate DATE NOT NULL, UNIQUE INDEX UNIQ_382B3BFC489BD34E (linkedCouponId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE spa_evergreen_coupon ADD CONSTRAINT FK_382B3BFC489BD34E FOREIGN KEY (linkedCouponId) REFERENCES data_crawl_asc_coupon (id)');
        $this->addSql('INSERT INTO spa_evergreen_coupon SELECT * FROM evergreen_coupon');
        $this->addSql('ALTER TABLE evergreen_coupon DROP FOREIGN KEY FK_47ECED0D489BD34E');
        $this->addSql('DROP TABLE evergreen_coupon');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE evergreen_coupon (id INT AUTO_INCREMENT NOT NULL, linkedCouponId INT DEFAULT NULL, description VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, isExpired TINYINT(1) NOT NULL, plannedEndDate DATE NOT NULL, UNIQUE INDEX UNIQ_47ECED0D489BD34E (linkedCouponId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE evergreen_coupon ADD CONSTRAINT FK_47ECED0D489BD34E FOREIGN KEY (linkedCouponId) REFERENCES data_crawl_asc_coupon (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('INSERT INTO evergreen_coupon SELECT * FROM spa_evergreen_coupon');
        $this->addSql('ALTER TABLE spa_evergreen_coupon DROP FOREIGN KEY FK_382B3BFC489BD34E');
        $this->addSql('DROP TABLE spa_evergreen_coupon');
    }
}
