<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240423094329 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add scheduleProduct in rf_product table, add foreign key reference in rf_schedule_product';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE rf_product ADD scheduleProduct INT DEFAULT NULL');
        $this->addSql('ALTER TABLE rf_product ADD CONSTRAINT FK_FC6FFF4487E59EAD FOREIGN KEY (scheduleProduct) REFERENCES rf_schedule_product (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_FC6FFF4487E59EAD ON rf_product (scheduleProduct)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE rf_product DROP FOREIGN KEY FK_FC6FFF4487E59EAD');
        $this->addSql('DROP INDEX UNIQ_FC6FFF4487E59EAD ON rf_product');
        $this->addSql('ALTER TABLE rf_product DROP scheduleProduct');
    }
}
