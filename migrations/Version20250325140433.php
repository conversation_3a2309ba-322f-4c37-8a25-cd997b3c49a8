<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250325140433 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add requestUpdateId to xero_accounting_report';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE xero_accounting_report ADD requestUpdateId INT DEFAULT NULL AFTER requestCreateId');
        $this->addSql('ALTER TABLE xero_accounting_report ADD CONSTRAINT FK_DD8659253CF21D76 FOREIGN KEY (requestUpdateId) REFERENCES api_request_xero (id)');
        $this->addSql('CREATE INDEX IDX_DD8659253CF21D76 ON xero_accounting_report (requestUpdateId)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE xero_accounting_report DROP FOREIGN KEY FK_DD8659253CF21D76');
        $this->addSql('DROP INDEX IDX_DD8659253CF21D76 ON xero_accounting_report');
        $this->addSql('ALTER TABLE xero_accounting_report DROP requestUpdateId');
    }
}
