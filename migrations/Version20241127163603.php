<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241127163603 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add data_spa_fin_shipment_event.numZeros column';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_spa_fin_shipment_event ADD numZeros INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_spa_fin_shipment_event DROP numZeros');
    }
}
