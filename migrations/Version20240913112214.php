<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240913112214 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add data_azr_customer_order table and importLinkDataLastFetchedDate column to data_azr_project table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE data_azr_customer_order (id INT AUTO_INCREMENT NOT NULL, azRankProjectId INT NOT NULL, requestId INT NOT NULL, updateRequestId INT DEFAULT NULL,  spreadsheetId VARCHAR(100) NOT NULL, gid VARCHAR(10) NOT NULL, azrOrderId VARCHAR(20) NOT NULL, orderTimestamp DATETIME DEFAULT NULL, orderDate DATE DEFAULT NULL, orderStatus VARCHAR(20) DEFAULT NULL,  firstName VARCHAR(50) DEFAULT NULL, lastName VARCHAR(50) DEFAULT NULL, fbName VARCHAR(32) DEFAULT NULL, customerEmail VARCHAR(32) DEFAULT NULL, productOrdered VARCHAR(255) DEFAULT NULL, amountPaid INT DEFAULT NULL, searchKeyword VARCHAR(255) DEFAULT NULL, qUseDifferentKeyword LONGTEXT DEFAULT NULL, qUseFiltersForSimilarProduct TINYINT(1) DEFAULT NULL, qLikeAboutProductDescription LONGTEXT DEFAULT NULL, qIsMainPhotoEyeCatching TINYINT(1) DEFAULT NULL, qIsThereMoreAppealingPhoto VARCHAR(20) DEFAULT NULL, qAskSellerAnythingAboutTheProduct LONGTEXT DEFAULT NULL, qIfYouWereInstructedToUseCoupon VARCHAR(15) DEFAULT NULL, qHowDidYouOrderThisToday VARCHAR(30) DEFAULT NULL, qNotesForAdmin LONGTEXT DEFAULT NULL, customerComment LONGTEXT DEFAULT NULL, refundComment LONGTEXT DEFAULT NULL, refund LONGTEXT DEFAULT NULL, INDEX IDX_7BD9F058A1637001 (requestId), INDEX IDX_7BD9F058C9744C63 (updateRequestId), INDEX IDX_7BD9F0582E784F20 (azRankProjectId), UNIQUE INDEX azr_order_id (azrOrderId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE data_azr_customer_order ADD CONSTRAINT FK_7BD9F058A1637001 FOREIGN KEY (requestId) REFERENCES api_request_azr (id)');
        $this->addSql('ALTER TABLE data_azr_customer_order ADD CONSTRAINT FK_7BD9F058C9744C63 FOREIGN KEY (updateRequestId) REFERENCES api_request_azr (id)');
        $this->addSql('ALTER TABLE data_azr_customer_order ADD CONSTRAINT FK_7BD9F0582E784F20 FOREIGN KEY (azRankProjectId) REFERENCES data_azr_project (id)');
        $this->addSql('ALTER TABLE data_azr_project ADD importLinkDataLastFetchedDate DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE api_request_azr ADD azRankProjectId INT DEFAULT NULL AFTER azRankCampaignId');
        $this->addSql('ALTER TABLE api_request_azr ADD CONSTRAINT FK_7AD232ABEDA1E22C FOREIGN KEY (azRankProjectId) REFERENCES data_azr_project (id)');
        $this->addSql('CREATE INDEX IDX_7AD232ABEDA1E22C ON api_request_azr (azRankProjectId)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_azr_customer_order DROP FOREIGN KEY FK_7BD9F058A1637001');
        $this->addSql('ALTER TABLE data_azr_customer_order DROP FOREIGN KEY FK_7BD9F058C9744C63');
        $this->addSql('ALTER TABLE data_azr_customer_order DROP FOREIGN KEY FK_7BD9F0582E784F20');
        $this->addSql('DROP TABLE data_azr_customer_order');
        $this->addSql('ALTER TABLE data_azr_project DROP importLinkDataLastFetchedDate');
        $this->addSql('ALTER TABLE api_request_azr DROP FOREIGN KEY FK_7AD232ABEDA1E22C');
        $this->addSql('DROP INDEX IDX_7AD232ABEDA1E22C ON api_request_azr');
        $this->addSql('ALTER TABLE api_request_azr DROP azRankProjectId');
    }
}
