<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240716165925 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add table for storing Az Rank API requests and Az Rank Campaign.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE api_request_az_rank (id INT AUTO_INCREMENT NOT NULL, requestType VARCHAR(20) NOT NULL, requestDate DATETIME DEFAULT NULL, parseDate DATETIME DEFAULT NULL, genHasParseDate TINYINT(1) GENERATED ALWAYS AS (parseDate IS NOT NULL) STORED, status VARCHAR(16) DEFAULT NULL, recordCount INT DEFAULT NULL, savedFileSize INT DEFAULT NULL, filename VA<PERSON>HAR(128) DEFAULT NULL, fileSystemName VARCHAR(40) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE azr_campaign (id INT AUTO_INCREMENT NOT NULL, requestId INT NOT NULL, updateRequestId INT DEFAULT NULL, spreadsheetId VARCHAR(100) NOT NULL, startDate DATE DEFAULT NULL, endDate DATE DEFAULT NULL, lastFetchedDate DATE DEFAULT NULL, brandName VARCHAR(100) NOT NULL, country VARCHAR(3) NOT NULL, brandManager VARCHAR(50) NOT NULL, type VARCHAR(10) NOT NULL, asinList LONGTEXT NOT NULL, productNameList LONGTEXT NOT NULL, INDEX IDX_F1CFFAC9A1637001 (requestId), INDEX IDX_F1CFFAC9C9744C63 (updateRequestId), UNIQUE INDEX spreadsheet_id (spreadsheetId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE azr_campaign ADD CONSTRAINT FK_F1CFFAC9A1637001 FOREIGN KEY (requestId) REFERENCES api_request_az_rank (id)');
        $this->addSql('ALTER TABLE azr_campaign ADD CONSTRAINT FK_F1CFFAC9C9744C63 FOREIGN KEY (updateRequestId) REFERENCES api_request_az_rank (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE api_request_az_rank');
        $this->addSql('ALTER TABLE azr_campaign DROP FOREIGN KEY FK_F1CFFAC9A1637001');
        $this->addSql('ALTER TABLE azr_campaign DROP FOREIGN KEY FK_F1CFFAC9C9744C63');
        $this->addSql('DROP TABLE azr_campaign');
    }
}
