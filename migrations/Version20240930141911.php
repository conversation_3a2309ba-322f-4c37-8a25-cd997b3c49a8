<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240930141911 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add brandAlias to azr_project table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE azr_project ADD brandAlias VARCHAR(5) DEFAULT NULL AFTER brand');
        $this->addSql(<<<SQL
            UPDATE azr_project p
              LEFT JOIN top_brand b on (
                       b.internalName = p.brand 
                    OR b.internalName = REPLACE(p.brand,' Store','') 
                    OR b.aliases like concat('%~',REPLACE(p.brand,' Store',''),'~%')
                   )
               SET p.brandAlias = b.brandCode
             WHERE p.brandAlias IS NULL
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE azr_project DROP brandAlias');
    }
}
