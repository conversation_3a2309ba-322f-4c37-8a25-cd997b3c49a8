<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240604114331 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add experimentCreationSource to data_crawl_asc_er_experiment';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_er_experiment ADD experimentCreationSource VARCHAR(10) DEFAULT NULL AFTER experimentDescription');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_er_experiment DROP experimentCreationSource');
    }
}
