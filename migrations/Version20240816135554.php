<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240816135554 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add slackMessageSent and dateSlackMessageSent column to tk_shipment_charge to track if a slack message has been sent for a shipment charge and when it was sent';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_shipment_charge ADD slackMessageSent TINYINT(1) DEFAULT 0 NOT NULL AFTER warnings, ADD dateSlackMessageSent DATETIME DEFAULT NULL AFTER slackMessageSent');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_shipment_charge DROP slackMessageSent, DROP dateSlackMessageSent');
    }
}
