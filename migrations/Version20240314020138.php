<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240314020138 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add tk_country_ship_time.transportMode';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_country_ship_time ADD transportMode VARCHAR(1) DEFAULT "S" NOT NULL AFTER countryTo');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_country_ship_time DROP transportMode');
    }
}
