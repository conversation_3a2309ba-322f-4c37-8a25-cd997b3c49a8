<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240428142552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add tk_shipment_charge.paymentDate field';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_shipment_charge ADD paymentDate DATE DEFAULT NULL AFTER chargeDate');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_shipment_charge DROP paymentDate');
    }
}
