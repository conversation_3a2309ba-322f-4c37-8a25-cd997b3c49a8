<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240130233204 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Split personal data from Order into OrderPersonal';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE spf_order_personal (id VARCHAR(20) NOT NULL, orderMainId VARCHAR(20) DEFAULT NULL, browserIp VARCHAR(20) DEFAULT NULL, contactEmail VARCHAR(50) DEFAULT NULL, email VARCHAR(100) DEFAULT NULL, billingFirstName VARCHAR(100) DEFAULT NULL, billingLastName VARCHAR(100) DEFAULT NULL, billingName VARCHAR(100) DEFAULT NULL, billingAddress1 VARCHAR(255) DEFAULT NULL, billingPhone VARCHAR(100) DEFAULT NULL, shippingFirstName VARCHAR(100) DEFAULT NULL, shippingLastName VARCHAR(100) DEFAULT NULL, shippingName VARCHAR(100) DEFAULT NULL, shippingAddress1 VARCHAR(255) DEFAULT NULL, shippingPhone VARCHAR(100) DEFAULT NULL, shippingLatitude DOUBLE PRECISION DEFAULT NULL, shippingLongitude DOUBLE PRECISION DEFAULT NULL, phone VARCHAR(100) DEFAULT NULL, clientDetails JSON DEFAULT NULL, UNIQUE INDEX UNIQ_51497D2AC0F37123 (orderMainId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE spf_order_personal ADD CONSTRAINT FK_51497D2AC0F37123 FOREIGN KEY (orderMainId) REFERENCES spf_order (id)');
        $this->addSql('ALTER TABLE spf_order DROP browserIp, DROP contactEmail, DROP email, DROP billingFirstName, DROP billingAddress1, DROP billingPhone, DROP billingLastName, DROP billingName, DROP shippingFirstName, DROP shippingAddress1, DROP shippingPhone, DROP shippingLastName, DROP shippingLatitude, DROP shippingLongitude, DROP shippingName, DROP phone, DROP clientDetails, CHANGE deviceId deviceId VARCHAR(20) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE spf_order_personal DROP FOREIGN KEY FK_51497D2AC0F37123');
        $this->addSql('DROP TABLE spf_order_personal');
        $this->addSql('ALTER TABLE spf_order ADD browserIp VARCHAR(20) DEFAULT NULL, ADD contactEmail VARCHAR(50) DEFAULT NULL, ADD email VARCHAR(100) DEFAULT NULL, ADD billingFirstName VARCHAR(100) DEFAULT NULL, ADD billingAddress1 VARCHAR(255) DEFAULT NULL, ADD billingPhone VARCHAR(100) DEFAULT NULL, ADD billingLastName VARCHAR(100) DEFAULT NULL, ADD billingName VARCHAR(100) DEFAULT NULL, ADD shippingFirstName VARCHAR(100) DEFAULT NULL, ADD shippingAddress1 VARCHAR(255) DEFAULT NULL, ADD shippingPhone VARCHAR(100) DEFAULT NULL, ADD shippingLastName VARCHAR(100) DEFAULT NULL, ADD shippingLatitude DOUBLE PRECISION DEFAULT NULL, ADD shippingLongitude DOUBLE PRECISION DEFAULT NULL, ADD shippingName VARCHAR(100) DEFAULT NULL, ADD phone VARCHAR(100) DEFAULT NULL, ADD clientDetails JSON DEFAULT NULL, CHANGE deviceId deviceId INT DEFAULT NULL');
    }
}
