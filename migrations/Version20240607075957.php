<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240607075957 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add startDate, endDate, and marketplace to api_request_levanta';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE api_request_levanta ADD startDate DATETIME DEFAULT NULL AFTER requestParams, ADD endDate DATETIME DEFAULT NULL AFTER startDate, ADD marketplace VARCHAR(12) DEFAULT NULL AFTER endDate');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE api_request_levanta DROP startDate, DROP endDate, DROP marketplace');
    }
}
