<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240127154115 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename and optimise data types POE columns';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche 
            CHANGE searchVolumeT90 searchVolT90 INT UNSIGNED DEFAULT NULL, 
            CHANGE searchVolumeT360 searchVolT360 INT UNSIGNED DEFAULT NULL, 
            CHANGE searchVolumeGrowthT90 searchVolGrowthT90Permil INT DEFAULT NULL, 
            CHANGE searchVolumeGrowthT180 searchVolGrowthT180Permil INT DEFAULT NULL, 
            CHANGE searchVolumeGrowthT360 searchVolGrowthT360Permil INT DEFAULT NULL,
            CHANGE minimumUnitsSoldT90 minUnitsSoldT90 INT UNSIGNED DEFAULT NULL,
            CHANGE maximumUnitsSoldT90 maxUnitsSoldT90 INT UNSIGNED DEFAULT NULL,
            CHANGE minimumUnitsSoldT360 minUnitsSoldT360 INT UNSIGNED DEFAULT NULL,
            CHANGE maximumUnitsSoldT360 maxUnitsSoldT360 INT UNSIGNED DEFAULT NULL,
            CHANGE minimumAverageUnitsSoldT360 minAvgUnitsSoldT360 INT UNSIGNED DEFAULT NULL,
            CHANGE maximumAverageUnitsSoldT360 maxAvgUnitsSoldT360 INT UNSIGNED DEFAULT NULL,
            CHANGE minimumPrice minPrice INT UNSIGNED DEFAULT NULL,
            CHANGE maximumPrice maxPrice INT UNSIGNED DEFAULT NULL,
            CHANGE productCount productCount INT UNSIGNED DEFAULT NULL,
            CHANGE avgPrice avgPrice INT UNSIGNED DEFAULT NULL,
            CHANGE avgPriceT360 avgPriceT360 INT UNSIGNED DEFAULT NULL,
            CHANGE returnRateT360 returnRateT360BasisPoints INT UNSIGNED DEFAULT NULL, 
            CHANGE demand demand INT UNSIGNED DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_product 
            CHANGE clickShareT90 clickShareT90Permil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE customerRating customerRating50 SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE clickShareT360 clickShareT360Permil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE avgPrice avgPrice INT UNSIGNED DEFAULT NULL, 
            CHANGE avgPriceT360 avgPriceT360 INT UNSIGNED DEFAULT NULL, 
            CHANGE clickCount clickCountT90 INT DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_search_term_metric 
            CHANGE searchVolumeT90 searchVolT90 INT DEFAULT NULL, 
            CHANGE searchVolumeT360 searchVolT360 INT DEFAULT NULL,
            CHANGE clickShare clickSharePermil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE clickShareT360 clickShareT360Permil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE searchConversionRate searchCvrPermil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE searchConversionRateT360 searchCvrT360Permil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE searchVolumeGrowthT180 searchVolGrowthT180Permil INT DEFAULT NULL, 
            CHANGE searchVolumeGrowthT360Yoy searchVolGrowthT360Permil INT DEFAULT NULL, 
            CHANGE searchVolumeQoq searchVolQoqPermil INT DEFAULT NULL, 
            CHANGE searchVolumeYoy searchVolYoyPermil INT DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_trends_metric 
            CHANGE brandCount brandCount SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE productCount productCount SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE searchVolumeT7 searchVolT7 INT UNSIGNED DEFAULT NULL, 
            CHANGE searchConversionRateT7 searchCvrT7Permil SMALLINT UNSIGNED DEFAULT NULL, 
            CHANGE averagePriceT7 avgPriceT7 INT UNSIGNED DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche 
            CHANGE searchVolT90 searchVolumeT90 INT DEFAULT NULL, 
            CHANGE searchVolT360 searchVolumeT360 INT DEFAULT NULL, 
            CHANGE searchVolGrowthT90Permil searchVolumeGrowthT90 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolGrowthT180Permil searchVolumeGrowthT180 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolGrowthT360Permil searchVolumeGrowthT360 DOUBLE PRECISION DEFAULT NULL,
            CHANGE minUnitsSoldT90 minimumUnitsSoldT90 INT DEFAULT NULL,
            CHANGE maxUnitsSoldT90 maximumUnitsSoldT90 INT DEFAULT NULL,
            CHANGE minUnitsSoldT360 minimumUnitsSoldT360 INT DEFAULT NULL,
            CHANGE maxUnitsSoldT360 maximumUnitsSoldT360 INT DEFAULT NULL,
            CHANGE minAvgUnitsSoldT360 minimumAverageUnitsSoldT360 DOUBLE PRECISION DEFAULT NULL,
            CHANGE maxAvgUnitsSoldT360 maximumAverageUnitsSoldT360 DOUBLE PRECISION DEFAULT NULL,
            CHANGE minPrice minimumPrice DOUBLE PRECISION DEFAULT NULL,
            CHANGE maxPrice maximumPrice DOUBLE PRECISION DEFAULT NULL,
            CHANGE productCount productCount INT DEFAULT NULL,
            CHANGE avgPrice avgPrice DOUBLE PRECISION DEFAULT NULL,
            CHANGE avgPriceT360 avgPriceT360 DOUBLE PRECISION DEFAULT NULL,
            CHANGE returnRateT360BasisPoints returnRateT360 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE demand demand DOUBLE PRECISION DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_product 
            CHANGE clickShareT90Permil clickShareT90 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE customerRating50 customerRating DOUBLE PRECISION DEFAULT NULL, 
            CHANGE clickShareT360Permil clickShareT360 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE avgPrice avgPrice DOUBLE PRECISION DEFAULT NULL, 
            CHANGE avgPriceT360 avgPriceT360 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE clickCountT90 clickCount INT DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_search_term_metric 
            CHANGE searchVolT90 searchVolumeT90 INT DEFAULT NULL, 
            CHANGE searchVolT360 searchVolumeT360 INT DEFAULT NULL,
            CHANGE clickSharePermil clickShare DOUBLE PRECISION DEFAULT NULL, 
            CHANGE clickShareT360Permil clickShareT360 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchCvrPermil searchConversionRate DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchCvrT360Permil searchConversionRateT360 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolGrowthT180Permil searchVolumeGrowthT180 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolGrowthT360Permil searchVolumeGrowthT360Yoy DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolQoqPermil searchVolumeQoq DOUBLE PRECISION DEFAULT NULL, 
            CHANGE searchVolYoyPermil searchVolumeYoy INT DEFAULT NULL');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_trends_metric 
            CHANGE brandCount brandCount INT DEFAULT NULL, 
            CHANGE productCount productCount INT DEFAULT NULL, 
            CHANGE searchVolT7 searchVolumeT7 INT DEFAULT NULL, 
            CHANGE searchCvrT7Permil searchConversionRateT7 DOUBLE PRECISION DEFAULT NULL, 
            CHANGE avgPriceT7 averagePriceT7 DOUBLE PRECISION DEFAULT NULL');
    }
}
