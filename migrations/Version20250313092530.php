<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250313092530 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add RFQ document support to ShipmentCharge';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE tk_shipment_rfq_doc (id INT AUTO_INCREMENT NOT NULL, uploadedById INT DEFAULT NULL, uploadedAt DATE DEFAULT NULL, rfqNumber VARCHAR(255) NOT NULL, hash VARCHAR(8) NOT NULL, mimeType VARCHAR(255) DEFAULT \'\' NOT NULL, originalFilename VARCHAR(255) DEFAULT \'\' NOT NULL, savedFileSize INT DEFAULT NULL, filename VARCHAR(128) DEFAULT NULL, fileSystemName VARCHAR(40) DEFAULT NULL, createdAt DATETIME NOT NULL, updatedAt DATETIME NOT NULL, INDEX IDX_CCDE5CEF2775DD20 (uploadedById), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tk_join_shipment_rfq_doc (rfqDocId INT NOT NULL, shipmentId INT NOT NULL, INDEX IDX_84378EB44C8CD397 (rfqDocId), INDEX IDX_84378EB43C5EEBE2 (shipmentId), PRIMARY KEY(rfqDocId, shipmentId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE tk_shipment_rfq_doc ADD CONSTRAINT FK_CCDE5CEF2775DD20 FOREIGN KEY (uploadedById) REFERENCES sonata_user (id)');
        $this->addSql('ALTER TABLE tk_join_shipment_rfq_doc ADD CONSTRAINT FK_84378EB44C8CD397 FOREIGN KEY (rfqDocId) REFERENCES tk_shipment_rfq_doc (id)');
        $this->addSql('ALTER TABLE tk_join_shipment_rfq_doc ADD CONSTRAINT FK_84378EB43C5EEBE2 FOREIGN KEY (shipmentId) REFERENCES tk_shipment (id)');
        $this->addSql('ALTER TABLE tk_shipment ADD estimatedNumberOfDays INT DEFAULT NULL');
        $this->addSql('ALTER TABLE tk_shipment_charge ADD shipmentRfqDocId INT DEFAULT NULL');
        $this->addSql('ALTER TABLE tk_shipment_charge ADD CONSTRAINT FK_18F5D5F2A1A5619 FOREIGN KEY (shipmentRfqDocId) REFERENCES tk_shipment_rfq_doc (id)');
        $this->addSql('CREATE INDEX IDX_18F5D5F2A1A5619 ON tk_shipment_charge (shipmentRfqDocId)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_shipment_charge DROP FOREIGN KEY FK_18F5D5F2A1A5619');
        $this->addSql('ALTER TABLE tk_shipment_rfq_doc DROP FOREIGN KEY FK_CCDE5CEF2775DD20');
        $this->addSql('ALTER TABLE tk_join_shipment_rfq_doc DROP FOREIGN KEY FK_84378EB44C8CD397');
        $this->addSql('ALTER TABLE tk_join_shipment_rfq_doc DROP FOREIGN KEY FK_84378EB43C5EEBE2');
        $this->addSql('DROP TABLE tk_join_shipment_rfq_doc');
        $this->addSql('DROP TABLE tk_shipment_rfq_doc');
        $this->addSql('ALTER TABLE tk_shipment DROP estimatedNumberOfDays');
        $this->addSql('DROP INDEX IDX_18F5D5F2A1A5619 ON tk_shipment_charge');
        $this->addSql('ALTER TABLE tk_shipment_charge DROP shipmentRfqDocId');
    }
}
