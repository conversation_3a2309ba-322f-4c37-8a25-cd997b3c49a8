<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227171022 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add reportDate and v3 fields to data_aar_cam_sb table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_aar_cam_sb CHANGE campaignId campaignId VARCHAR(20) DEFAULT NULL, ADD reportDate DATETIME DEFAULT NULL AFTER lastReportId');
        $this->addSql('ALTER TABLE data_aar_cam_sb ADD currency VARCHAR(3) DEFAULT NULL AFTER campaignBudgetType, CHANGE campaignBudgetType campaignBudgetType VARCHAR(20) DEFAULT NULL, ADD ordersCat INT DEFAULT NULL AFTER attributedConversions14d, ADD dpViewsVoc14d INT DEFAULT NULL AFTER attributedConversions14dSameSKU, ADD ntbOrdersCat INT DEFAULT NULL AFTER attributedOrdersNewToBrand14d, ADD salesCat INT DEFAULT NULL AFTER attributedSales14d, ADD ntbSalesCat INT DEFAULT NULL AFTER attributedSalesNewToBrand14d, ADD ntbUnitsCat INT DEFAULT NULL AFTER attributedUnitsOrderedNewToBrand14d, ADD unitsSoldCat INT DEFAULT NULL AFTER unitsSold14d, ADD viewableImpressions INT DEFAULT NULL, ADD video5SecondViewRate DOUBLE PRECISION DEFAULT NULL, ADD video5SecondViews INT DEFAULT NULL, ADD videoFirstQuartileViews INT DEFAULT NULL, ADD videoMidpointViews INT DEFAULT NULL, ADD videoThirdQuartileViews INT DEFAULT NULL, ADD videoCompleteViews INT DEFAULT NULL, ADD videoUnmutes INT DEFAULT NULL, ADD vtr DOUBLE PRECISION DEFAULT NULL, ADD vctr DOUBLE PRECISION DEFAULT NULL, ADD atcVoc INT DEFAULT NULL, ADD atcCat INT DEFAULT NULL, ADD atcRate DOUBLE PRECISION DEFAULT NULL, ADD eCPNTBDPV INT DEFAULT NULL, ADD eCPATC INT DEFAULT NULL, ADD brandedSearchesVoc INT DEFAULT NULL, ADD costType VARCHAR(4) DEFAULT NULL, ADD ntbDpViewRate DOUBLE PRECISION DEFAULT NULL, ADD ntbDpViewsVoc INT DEFAULT NULL, ADD ntbDpViewsCat INT DEFAULT NULL, CHANGE campaignId campaignId VARCHAR(20) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_aar_cam_sb DROP currency, CHANGE campaignBudgetType campaignBudgetType VARCHAR(255) DEFAULT NULL, DROP ordersCat, DROP dpViewsVoc14d, DROP ntbOrdersCat, DROP salesCat, DROP ntbSalesCat, DROP ntbUnitsCat, DROP unitsSoldCat, DROP viewableImpressions, DROP video5SecondViewRate, DROP video5SecondViews, DROP videoFirstQuartileViews, DROP videoMidpointViews, DROP videoThirdQuartileViews, DROP videoCompleteViews, DROP videoUnmutes, DROP vtr, DROP vctr, DROP atcVoc, DROP atcCat, DROP atcRate, DROP eCPNTBDPV, DROP eCPATC, DROP brandedSearchesVoc, DROP costType, DROP ntbDpViewRate, DROP ntbDpViewsVoc, DROP ntbDpViewsCat, CHANGE campaignId campaignId BIGINT DEFAULT NULL');
        $this->addSql('ALTER TABLE data_aar_cam_sb CHANGE campaignId campaignId BIGINT DEFAULT NULL, DROP reportDate');
    }
}
