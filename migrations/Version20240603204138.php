<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240603204138 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'rename dd_niche.nl_id index to dd_niche_id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE dd_niche RENAME INDEX nl_id TO dd_niche_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE dd_niche RENAME INDEX dd_niche_id TO nl_id');
    }
}
