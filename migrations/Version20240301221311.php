<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240301221311 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add performance index on data_spa_rep_sra_sat_asin.startDate';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IDX_DATE ON data_spa_rep_sra_sat_asin (startDate)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_DATE ON data_spa_rep_sra_sat_asin');
    }
}
