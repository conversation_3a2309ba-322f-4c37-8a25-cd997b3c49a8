<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240903094327 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Change data_azr_project currency|float fields to INT from DOUBLE PRECISION';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_azr_project CHANGE priceOnAMZ priceOnAMZ INT DEFAULT NULL, CHANGE priceOfProduct priceOfProduct INT DEFAULT NULL, CHANGE azRankFeePerUnit azRankFeePerUnit INT DEFAULT NULL, CHANGE totalCostOfProducts totalCostOfProducts INT DEFAULT NULL, <PERSON>AN<PERSON> azRankFeeTotal azRankFeeTotal INT DEFAULT NULL, <PERSON>AN<PERSON> totalToPayBySeller totalToPayBySeller INT DEFAULT NULL, CHANGE moneyBackFromAMZ moneyBackFromAMZ INT DEFAULT NULL, CHANGE actualExpenses actualExpenses INT DEFAULT NULL, CHANGE costPerUnit costPerUnit INT DEFAULT NULL, CHANGE price price INT DEFAULT NULL, CHANGE amzFbaFees amzFbaFees INT DEFAULT NULL, CHANGE amzReferalFee amzReferalFee INT DEFAULT NULL, CHANGE incomeFromAMZPerUnit incomeFromAMZPerUnit INT DEFAULT NULL, CHANGE realExpensePerUnit realExpensePerUnit INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_azr_project CHANGE priceOnAMZ priceOnAMZ DOUBLE PRECISION DEFAULT NULL, CHANGE priceOfProduct priceOfProduct DOUBLE PRECISION DEFAULT NULL, CHANGE azRankFeePerUnit azRankFeePerUnit DOUBLE PRECISION DEFAULT NULL, CHANGE totalCostOfProducts totalCostOfProducts DOUBLE PRECISION DEFAULT NULL, CHANGE azRankFeeTotal azRankFeeTotal DOUBLE PRECISION DEFAULT NULL, CHANGE totalToPayBySeller totalToPayBySeller DOUBLE PRECISION DEFAULT NULL, CHANGE moneyBackFromAMZ moneyBackFromAMZ DOUBLE PRECISION DEFAULT NULL, CHANGE actualExpenses actualExpenses DOUBLE PRECISION DEFAULT NULL, CHANGE costPerUnit costPerUnit DOUBLE PRECISION DEFAULT NULL, CHANGE price price DOUBLE PRECISION DEFAULT NULL, CHANGE amzFbaFees amzFbaFees DOUBLE PRECISION DEFAULT NULL, CHANGE amzReferalFee amzReferalFee DOUBLE PRECISION DEFAULT NULL, CHANGE incomeFromAMZPerUnit incomeFromAMZPerUnit DOUBLE PRECISION DEFAULT NULL, CHANGE realExpensePerUnit realExpensePerUnit DOUBLE PRECISION DEFAULT NULL');
    }
}
