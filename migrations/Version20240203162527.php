<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240203162527 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add performance index to api_request_spa_order';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX acct_azid ON api_request_spa_order (accountAmazonId, orderId)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX acct_azid ON api_request_spa_order');
    }
}
