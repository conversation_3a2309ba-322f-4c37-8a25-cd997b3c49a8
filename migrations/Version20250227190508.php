<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250227190508 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add performance indexes on COIF to speed up ASIN and SKU searches';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX idx_asin_date ON data_spa_cached_ord_item_fee (oiAsin, ooLocalDate)');
        $this->addSql('CREATE INDEX idx_sku_date ON data_spa_cached_ord_item_fee (oiSku, ooLocalDate)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX idx_asin_date ON data_spa_cached_ord_item_fee');
        $this->addSql('DROP INDEX idx_sku_date ON data_spa_cached_ord_item_fee');
    }
}
