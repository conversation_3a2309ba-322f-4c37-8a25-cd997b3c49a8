<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240220135713 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create tables for Creative Task Management System';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE tm_task_creative (id INT AUTO_INCREMENT NOT NULL, assignedUserId INT DEFAULT NULL, mondayCreativeTaskId INT DEFAULT NULL, designTaskId INT DEFAULT NULL, allocationStatus VARCHAR(7) DEFAULT \'Draft\' NOT NULL, taskNature VARCHAR(15) DEFAULT \'\' NOT NULL, enOrFlTask VARCHAR(2) DEFAULT \'\' NOT NULL, caWeek DATE DEFAULT NULL, typeOfWork VARCHAR(100) DEFAULT \'\' NOT NULL, taskName LONGTEXT NOT NULL, brand VARCHAR(20) DEFAULT \'\' NOT NULL, taskHours DOUBLE PRECISION DEFAULT \'11\' NOT NULL, taskDays SMALLINT DEFAULT 1 NOT NULL, taskProgress SMALLINT DEFAULT 0 NOT NULL, taskStartDate DATE DEFAULT NULL, taskEndDate DATE DEFAULT NULL, revisionHours DOUBLE PRECISION DEFAULT \'0\' NOT NULL, revisionDays SMALLINT DEFAULT 1 NOT NULL, revisionProgress SMALLINT DEFAULT 0 NOT NULL, revisionStartDate DATE DEFAULT NULL, revisionEndDate DATE DEFAULT NULL, caTotalHours DOUBLE PRECISION DEFAULT \'0\' NOT NULL, requestedDate DATE DEFAULT NULL, caCompletionDate DATE DEFAULT NULL, remarks LONGTEXT NOT NULL, INDEX IDX_3CCBA5652DAA9624 (assignedUserId), INDEX IDX_3CCBA565B8B6D4ED (mondayCreativeTaskId), UNIQUE INDEX UNIQ_3CCBA565F83A52BE (designTaskId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tm_task_day_creative (id INT AUTO_INCREMENT NOT NULL, taskId INT DEFAULT NULL, workDate DATE NOT NULL, taskHours DOUBLE PRECISION NOT NULL, workType VARCHAR(1) NOT NULL, INDEX IDX_E900AEF7D34FCA37 (taskId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tm_task_user_day_creative (id INT AUTO_INCREMENT NOT NULL, userId INT DEFAULT NULL, workDate DATE NOT NULL, maxHours DOUBLE PRECISION NOT NULL, caTaskHours DOUBLE PRECISION NOT NULL, INDEX IDX_489E72E564B64DCC (userId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE tm_task_creative ADD CONSTRAINT FK_3CCBA5652DAA9624 FOREIGN KEY (assignedUserId) REFERENCES sonata_user (id)');
        $this->addSql('ALTER TABLE tm_task_creative ADD CONSTRAINT FK_3CCBA565B8B6D4ED FOREIGN KEY (mondayCreativeTaskId) REFERENCES mon_creative_task (id)');
        $this->addSql('ALTER TABLE tm_task_creative ADD CONSTRAINT FK_3CCBA565F83A52BE FOREIGN KEY (designTaskId) REFERENCES tm_task_creative (id)');
        $this->addSql('ALTER TABLE tm_task_day_creative ADD CONSTRAINT FK_E900AEF7D34FCA37 FOREIGN KEY (taskId) REFERENCES tm_task_creative (id)');
        $this->addSql('ALTER TABLE tm_task_user_day_creative ADD CONSTRAINT FK_489E72E564B64DCC FOREIGN KEY (userId) REFERENCES sonata_user (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tm_task_creative DROP FOREIGN KEY FK_3CCBA5652DAA9624');
        $this->addSql('ALTER TABLE tm_task_creative DROP FOREIGN KEY FK_3CCBA565B8B6D4ED');
        $this->addSql('ALTER TABLE tm_task_creative DROP FOREIGN KEY FK_3CCBA565F83A52BE');
        $this->addSql('ALTER TABLE tm_task_day_creative DROP FOREIGN KEY FK_E900AEF7D34FCA37');
        $this->addSql('ALTER TABLE tm_task_user_day_creative DROP FOREIGN KEY FK_489E72E564B64DCC');
        $this->addSql('DROP TABLE tm_task_creative');
        $this->addSql('DROP TABLE tm_task_day_creative');
        $this->addSql('DROP TABLE tm_task_user_day_creative');
    }
}
