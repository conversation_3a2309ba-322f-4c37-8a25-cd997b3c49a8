<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231026130749 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create tables for lightning deals and items';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE data_crawl_asc_lightning_deal (id INT AUTO_INCREMENT NOT NULL, requestId INT NOT NULL, updateRequestId INT DEFAULT NULL, spaProfileId INT NOT NULL, campaignId VARCHAR(36) NOT NULL, campaignKey VARCHAR(100) NOT NULL, startDate DATETIME DEFAULT NULL, endDate DATETIME DEFAULT NULL, internalDescription VARCHAR(255) NOT NULL, countryCode VARCHAR(2) NOT NULL, type VARCHAR(20) NOT NULL, imageUrl VARCHAR(255) DEFAULT NULL, nudgeId VARCHAR(100) DEFAULT NULL, eventId VARCHAR(100) DEFAULT NULL, eventName VARCHAR(255) DEFAULT NULL, minCurrencyAmount INT DEFAULT NULL, maxCurrencyAmount INT DEFAULT NULL, chargedFee INT DEFAULT NULL, scheduledFee INT DEFAULT NULL, isDealScheduleSurfaced TINYINT(1) DEFAULT NULL, isAdmin TINYINT(1) DEFAULT NULL, isPrimeExclusive TINYINT(1) DEFAULT NULL, sales INT DEFAULT NULL, unitsSold INT DEFAULT NULL, currencyCode VARCHAR(3) DEFAULT NULL, ingressUrl VARCHAR(255) DEFAULT NULL, skuCount INT DEFAULT NULL, asinCount INT DEFAULT NULL, externalId VARCHAR(15) DEFAULT NULL, glanceViews INT DEFAULT NULL, imageAsin VARCHAR(15) DEFAULT NULL, committedQuantity INT DEFAULT NULL, canAddItems TINYINT(1) DEFAULT NULL, canRemoveItems TINYINT(1) DEFAULT NULL, multiParentItemList JSON DEFAULT NULL, campaignStatus VARCHAR(15) DEFAULT NULL, egressUrl VARCHAR(255) DEFAULT NULL, featuredAsin VARCHAR(15) DEFAULT NULL, scheduleOptions JSON DEFAULT NULL, dealValidationErrors JSON DEFAULT NULL, dealValidationErrorViews JSON DEFAULT NULL, pendingEdits TINYINT(1) DEFAULT NULL, recommendedAsin VARCHAR(100) DEFAULT NULL, enforceRecommendedAsin TINYINT(1) DEFAULT NULL, title LONGTEXT DEFAULT NULL, displayName LONGTEXT DEFAULT NULL, INDEX IDX_CA6308C7A1637001 (requestId), INDEX IDX_CA6308C7C9744C63 (updateRequestId), INDEX IDX_CA6308C717D8AA47 (spaProfileId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE data_crawl_asc_lightning_deal_item (id INT AUTO_INCREMENT NOT NULL, lightningDealId INT NOT NULL, asin VARCHAR(10) NOT NULL, sku VARCHAR(50) NOT NULL, dealPrice INT DEFAULT NULL, dealQuantity INT DEFAULT NULL, dealItemQuantitySold INT DEFAULT NULL, dealItemQuantityWaitListed INT DEFAULT NULL, dealItemRevenue INT DEFAULT NULL, glanceViews INT DEFAULT NULL, itemData JSON DEFAULT NULL, INDEX IDX_A73B039C3900F8BE (lightningDealId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal ADD CONSTRAINT FK_CA6308C7A1637001 FOREIGN KEY (requestId) REFERENCES api_request_crawl_asc (id)');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal ADD CONSTRAINT FK_CA6308C7C9744C63 FOREIGN KEY (updateRequestId) REFERENCES api_request_crawl_asc (id)');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal ADD CONSTRAINT FK_CA6308C717D8AA47 FOREIGN KEY (spaProfileId) REFERENCES account_profile_spa (id)');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal_item ADD CONSTRAINT FK_A73B039C3900F8BE FOREIGN KEY (lightningDealId) REFERENCES data_crawl_asc_lightning_deal (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal DROP FOREIGN KEY FK_CA6308C7A1637001');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal DROP FOREIGN KEY FK_CA6308C7C9744C63');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal DROP FOREIGN KEY FK_CA6308C717D8AA47');
        $this->addSql('ALTER TABLE data_crawl_asc_lightning_deal_item DROP FOREIGN KEY FK_A73B039C3900F8BE');
        $this->addSql('DROP TABLE data_crawl_asc_lightning_deal');
        $this->addSql('DROP TABLE data_crawl_asc_lightning_deal_item');
    }
}
