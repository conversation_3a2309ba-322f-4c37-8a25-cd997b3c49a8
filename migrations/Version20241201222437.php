<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241201222437 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add generated columns for tracking orders and refunds to data_spa_cached_ord_item_fee';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_spa_cached_ord_item_fee ADD hasOrderEvent TINYINT GENERATED ALWAYS AS (IF(
                sumOrdBrb IS NOT NULL OR
                sumOrdPrincipal IS NOT NULL OR 
                sumOrdCommission IS NOT NULL OR
                sumOrdShippingCharge IS NOT NULL OR
                sumOrdFBAPerUnitFulfillmentFee IS NOT NULL OR
                sumOrdShippingChargeback IS NOT NULL OR
                sumOrdPromotionMetaDataDefinitionValue IS NOT NULL OR
                sumOrdTax IS NOT NULL OR
                sumOrdMarketplaceFacilitatorTaxPrincipal IS NOT NULL OR
                sumOrdShippingTax IS NOT NULL OR
                sumOrdMarketplaceFacilitatorTaxShipping IS NOT NULL OR
                sumOrdRefundCommission IS NOT NULL OR
                sumOrdSalesTaxCollectionFee IS NOT NULL OR
                sumOrdShippingHB IS NOT NULL OR
                sumOrdLowValueGoodsTaxPrincipal IS NOT NULL OR
                sumOrdLowValueGoodsTaxShipping IS NOT NULL OR
                sumOrdRestockingFee IS NOT NULL OR
                sumOrdAmazonExclusivesFee IS NOT NULL OR
                sumOrdGiftWrap IS NOT NULL OR
                sumOrdGiftWrapTax IS NOT NULL OR
                sumOrdGiftwrapChargeback IS NOT NULL OR
                sumOrdMarketplaceFacilitatorTaxOther IS NOT NULL OR
                sumOrdMarketplaceFacilitatorVATPrincipal IS NOT NULL OR
                sumOrdMarketplaceFacilitatorVATShipping IS NOT NULL OR
                sumOrdMarketplaceFacilitatorTaxRestockingFee IS NOT NULL OR
                sumOrdGoodwill IS NOT NULL OR
                sumOrdLowValueGoodsTaxOther IS NOT NULL OR
                sumOrdVariableClosingFee IS NOT NULL OR
                sumOrdExportCharge IS NOT NULL OR
                sumOrdGiftwrapCommission IS NOT NULL OR
                sumOrdReturnShipping IS NOT NULL OR
                sumOrdCostOfPointsGranted IS NOT NULL OR
                sumOrdPaymentMethodFee IS NOT NULL OR
                sumOrdCODChargeback IS NOT NULL OR
                sumOrdCostOfPointsReturned IS NOT NULL OR
                sumOrdPointsAdjusted IS NOT NULL OR
                sumOrdDigitalServicesFee IS NOT NULL OR
                sumOrdDigitalServicesFeeFBA IS NOT NULL OR
                sumOrdUnknown IS NOT NULL,
                1, 0
            )) STORED AFTER pASIN, ADD hasRefundEvent TINYINT GENERATED ALWAYS AS (IF(
                sumRefBrb IS NOT NULL OR
                sumRefPrincipal IS NOT NULL OR 
                sumRefCommission IS NOT NULL OR
                sumRefShippingCharge IS NOT NULL OR
                sumRefFBAPerUnitFulfillmentFee IS NOT NULL OR
                sumRefShippingChargeback IS NOT NULL OR
                sumRefPromotionMetaDataDefinitionValue IS NOT NULL OR
                sumRefTax IS NOT NULL OR
                sumRefMarketplaceFacilitatorTaxPrincipal IS NOT NULL OR
                sumRefShippingTax IS NOT NULL OR
                sumRefMarketplaceFacilitatorTaxShipping IS NOT NULL OR
                sumRefRefundCommission IS NOT NULL OR
                sumRefSalesTaxCollectionFee IS NOT NULL OR
                sumRefShippingHB IS NOT NULL OR
                sumRefLowValueGoodsTaxPrincipal IS NOT NULL OR
                sumRefLowValueGoodsTaxShipping IS NOT NULL OR
                sumRefRestockingFee IS NOT NULL OR
                sumRefAmazonExclusivesFee IS NOT NULL OR
                sumRefGiftWrap IS NOT NULL OR
                sumRefGiftWrapTax IS NOT NULL OR
                sumRefGiftwrapChargeback IS NOT NULL OR
                sumRefMarketplaceFacilitatorTaxOther IS NOT NULL OR
                sumRefMarketplaceFacilitatorVATPrincipal IS NOT NULL OR
                sumRefMarketplaceFacilitatorVATShipping IS NOT NULL OR
                sumRefMarketplaceFacilitatorTaxRestockingFee IS NOT NULL OR
                sumRefGoodwill IS NOT NULL OR
                sumRefLowValueGoodsTaxOther IS NOT NULL OR
                sumRefVariableClosingFee IS NOT NULL OR
                sumRefExportCharge IS NOT NULL OR
                sumRefGiftwrapCommission IS NOT NULL OR
                sumRefReturnShipping IS NOT NULL OR
                sumRefCostOfPointsGranted IS NOT NULL OR
                sumRefPaymentMethodFee IS NOT NULL OR
                sumRefCODChargeback IS NOT NULL OR
                sumRefCostOfPointsReturned IS NOT NULL OR
                sumRefPointsAdjusted IS NOT NULL OR
                sumRefDigitalServicesFee IS NOT NULL OR
                sumRefDigitalServicesFeeFBA IS NOT NULL OR
                sumRefUnknown IS NOT NULL,
                1, 0
            )) STORED AFTER hasOrderEvent, ADD netOrder INT GENERATED ALWAYS AS (
                COALESCE(sumOrdBrb, 0) + 
                COALESCE(sumOrdPrincipal, 0) + 
                COALESCE(sumOrdCommission, 0) + 
                COALESCE(sumOrdShippingCharge, 0) + 
                COALESCE(sumOrdFBAPerUnitFulfillmentFee, 0) + 
                COALESCE(sumOrdShippingChargeback, 0) + 
                COALESCE(sumOrdPromotionMetaDataDefinitionValue, 0) + 
                COALESCE(sumOrdTax, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorTaxPrincipal, 0) + 
                COALESCE(sumOrdShippingTax, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorTaxShipping, 0) + 
                COALESCE(sumOrdRefundCommission, 0) + 
                COALESCE(sumOrdSalesTaxCollectionFee, 0) + 
                COALESCE(sumOrdShippingHB, 0) + 
                COALESCE(sumOrdLowValueGoodsTaxPrincipal, 0) + 
                COALESCE(sumOrdLowValueGoodsTaxShipping, 0) + 
                COALESCE(sumOrdRestockingFee, 0) + 
                COALESCE(sumOrdAmazonExclusivesFee, 0) + 
                COALESCE(sumOrdGiftWrap, 0) + 
                COALESCE(sumOrdGiftWrapTax, 0) + 
                COALESCE(sumOrdGiftwrapChargeback, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorTaxOther, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorVATPrincipal, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorVATShipping, 0) + 
                COALESCE(sumOrdMarketplaceFacilitatorTaxRestockingFee, 0) + 
                COALESCE(sumOrdGoodwill, 0) + 
                COALESCE(sumOrdLowValueGoodsTaxOther, 0) + 
                COALESCE(sumOrdVariableClosingFee, 0) + 
                COALESCE(sumOrdExportCharge, 0) + 
                COALESCE(sumOrdGiftwrapCommission, 0) + 
                COALESCE(sumOrdReturnShipping, 0) + 
                COALESCE(sumOrdCostOfPointsGranted, 0) + 
                COALESCE(sumOrdPaymentMethodFee, 0) + 
                COALESCE(sumOrdCODChargeback, 0) + 
                COALESCE(sumOrdCostOfPointsReturned, 0) + 
                COALESCE(sumOrdPointsAdjusted, 0) + 
                COALESCE(sumOrdDigitalServicesFee, 0) + 
                COALESCE(sumOrdDigitalServicesFeeFBA, 0) + 
                COALESCE(sumOrdUnknown, 0)
            ) STORED AFTER hasRefundEvent, ADD netRefund INT GENERATED ALWAYS AS (
                COALESCE(sumRefBrb, 0) + 
                COALESCE(sumRefPrincipal, 0) + 
                COALESCE(sumRefCommission, 0) + 
                COALESCE(sumRefShippingCharge, 0) + 
                COALESCE(sumRefFBAPerUnitFulfillmentFee, 0) + 
                COALESCE(sumRefShippingChargeback, 0) + 
                COALESCE(sumRefPromotionMetaDataDefinitionValue, 0) + 
                COALESCE(sumRefTax, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorTaxPrincipal, 0) + 
                COALESCE(sumRefShippingTax, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorTaxShipping, 0) + 
                COALESCE(sumRefRefundCommission, 0) + 
                COALESCE(sumRefSalesTaxCollectionFee, 0) + 
                COALESCE(sumRefShippingHB, 0) + 
                COALESCE(sumRefLowValueGoodsTaxPrincipal, 0) + 
                COALESCE(sumRefLowValueGoodsTaxShipping, 0) + 
                COALESCE(sumRefRestockingFee, 0) + 
                COALESCE(sumRefAmazonExclusivesFee, 0) + 
                COALESCE(sumRefGiftWrap, 0) + 
                COALESCE(sumRefGiftWrapTax, 0) + 
                COALESCE(sumRefGiftwrapChargeback, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorTaxOther, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorVATPrincipal, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorVATShipping, 0) + 
                COALESCE(sumRefMarketplaceFacilitatorTaxRestockingFee, 0) + 
                COALESCE(sumRefGoodwill, 0) + 
                COALESCE(sumRefLowValueGoodsTaxOther, 0) + 
                COALESCE(sumRefVariableClosingFee, 0) + 
                COALESCE(sumRefExportCharge, 0) + 
                COALESCE(sumRefGiftwrapCommission, 0) + 
                COALESCE(sumRefReturnShipping, 0) + 
                COALESCE(sumRefCostOfPointsGranted, 0) + 
                COALESCE(sumRefPaymentMethodFee, 0) + 
                COALESCE(sumRefCODChargeback, 0) + 
                COALESCE(sumRefCostOfPointsReturned, 0) + 
                COALESCE(sumRefPointsAdjusted, 0) + 
                COALESCE(sumRefDigitalServicesFee, 0) + 
                COALESCE(sumRefDigitalServicesFeeFBA, 0) + 
                COALESCE(sumRefUnknown, 0)
            ) STORED AFTER netOrder, ADD netTotal INT GENERATED ALWAYS AS (netOrder + netRefund) STORED AFTER netRefund');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_spa_cached_ord_item_fee DROP hasOrderEvent, DROP hasRefundEvent, DROP netOrder, DROP netRefund, DROP netTotal');
    }
}
