<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240613170630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Make kp_product.requestUpdateId NOT NULL and set it to requestCreateId if it is NULL';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE kp_product SET requestUpdateId = requestCreateId WHERE requestUpdateId IS NULL');
        $this->addSql('ALTER TABLE kp_product CHANGE requestUpdateId requestUpdateId INT NOT NULL, CHANGE buyboxUsedHistory buyBoxUsedHistory JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE kp_product CHANGE requestUpdateId requestUpdateId INT DEFAULT NULL');
    }
}
