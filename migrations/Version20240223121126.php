<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240223121126 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add mondayUserId to sonata_user table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sonata_user ADD mondayUserId VARCHAR(8) DEFAULT NULL AFTER id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE sonata_user DROP mondayUserId');
    }
}
