<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240521171056 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add tm_task_creative.mondayUrl column';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tm_task_creative ADD mondayUrl VARCHAR(200) DEFAULT NULL AFTER caCompletionDate');
        $this->addSql("UPDATE tm_task_creative SET taskNature = 'ECONTENT_LAUNCH' WHERE taskNature = 'TIME_SENSITIVE'");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tm_task_creative DROP mondayUrl');
        $this->addSql("UPDATE tm_task_creative SET taskNature = 'TIME_SENSITIVE' WHERE taskNature = 'ECONTENT_LAUNCH'");
    }
}
