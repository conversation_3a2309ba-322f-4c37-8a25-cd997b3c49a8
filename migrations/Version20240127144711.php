<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240127144711 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add poe_ to name of data_crawl_asc_niche* tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('RENAME TABLE data_crawl_asc_niche TO data_crawl_asc_poe_niche');
        $this->addSql('RENAME TABLE data_crawl_asc_niche_product TO data_crawl_asc_poe_niche_product');
        $this->addSql('RENAME TABLE data_crawl_asc_niche_search_term_metric TO data_crawl_asc_poe_niche_search_term_metric');
        $this->addSql('RENAME TABLE data_crawl_asc_niche_trends_metric TO data_crawl_asc_poe_niche_trends_metric');

        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ff469f72a1637001 TO IDX_AE4D551AA1637001');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ff469f72c9744c63 TO IDX_AE4D551AC9744C63');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ff469f7217d8aa47 TO IDX_AE4D551A17D8AA47');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_product RENAME INDEX idx_43925b156c663ce3 TO IDX_474536666C663CE3');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_search_term_metric RENAME INDEX idx_32e19fc36c663ce3 TO IDX_851406FB6C663CE3');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_trends_metric RENAME INDEX idx_c115f1036c663ce3 TO IDX_918B146C6C663CE3');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('RENAME TABLE data_crawl_asc_poe_niche TO data_crawl_asc_niche');
        $this->addSql('RENAME TABLE data_crawl_asc_poe_niche_product TO data_crawl_asc_niche_product');
        $this->addSql('RENAME TABLE data_crawl_asc_poe_niche_search_term_metric TO data_crawl_asc_niche_search_term_metric');
        $this->addSql('RENAME TABLE data_crawl_asc_poe_niche_trends_metric TO data_crawl_asc_niche_trends_metric');

        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ae4d551a17d8aa47 TO IDX_FF469F7217D8AA47');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ae4d551aa1637001 TO IDX_FF469F72A1637001');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche RENAME INDEX idx_ae4d551ac9744c63 TO IDX_FF469F72C9744C63');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_product RENAME INDEX idx_474536666c663ce3 TO IDX_43925B156C663CE3');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_search_term_metric RENAME INDEX idx_851406fb6c663ce3 TO IDX_32E19FC36C663CE3');
        $this->addSql('ALTER TABLE data_crawl_asc_poe_niche_trends_metric RENAME INDEX idx_918b146c6c663ce3 TO IDX_C115F1036C663CE3');
    }
}
