<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231228182553 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add varType column to data_crawl_asc_deal_item';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_deal_item ADD varType VARCHAR(1) DEFAULT NULL AFTER sku');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE data_crawl_asc_deal_item DROP varType');
    }
}
