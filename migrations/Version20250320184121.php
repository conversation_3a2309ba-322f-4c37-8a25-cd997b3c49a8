<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250320184121 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Make placementOptionId nullable in spa_fba_inbound_shipment_detail';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE spa_fba_inbound_shipment_detail CHANGE placementOptionId placementOptionId VARCHAR(38) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE spa_fba_inbound_shipment_detail CHANGE placementOptionId placementOptionId VARCHAR(38) NOT NULL');
    }
}
