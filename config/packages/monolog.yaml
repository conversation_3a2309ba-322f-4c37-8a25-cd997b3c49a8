monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists
        - gravitiq_console

when@dev:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                # available level (emergency|alert|critical|error|warning|notice|info|debug){1}
                action_level: warning
                handler: nested
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
            gravitiq_console:
                type: console
                process_psr_3_messages: false
                channels: ["gravitiq_console"]
                formatter: monolog.formatter.gravitiq_console
                verbosity_levels:
                    VERBOSITY_NORMAL: INFO
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console", "!gravitiq_console"]
                verbosity_levels:
                    VERBOSITY_NORMAL: CRITICAL
            deprecation:
                type: "null"
                channels: [deprecation]
#            deprecation:
#                type: stream
#                channels: [deprecation]
#                path: php://stderr

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: info
                handler: nested
                excluded_http_codes: [404, 405]
                channels: ["!event"]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: info
            gravitiq_console:
                type: stream
                process_psr_3_messages: false
                path: "%kernel.logs_dir%/%kernel.environment%.console.log"
                channels: [ "gravitiq_console" ]
                formatter: monolog.formatter.gravitiq_console
                verbosity_levels:
                    VERBOSITY_NORMAL: INFO
            deprecation:
                type: "null"
                channels: [deprecation]

when@prod:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: grouped_log_and_email
                excluded_http_codes: [404, 405]
                buffer_size: 50 # How many messages should be saved? Prevent memory leaks
                channels:   ["!mailer"]
            grouped_log_and_email:
                type:    group
                members: [nested, deduplicated_for_mail, deduplicated_for_slack]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: error
                formatter: monolog.formatter.json
            deduplicated_for_mail:
                type:    deduplication
                handler: basic_mailer
                time: 120
            deduplicated_for_slack:
                type:    deduplication
                handler: basic_slack
                time: 120
            basic_mailer:
                type:       symfony_mailer
                from_email: '<EMAIL>'
                to_email:   '<EMAIL>'
                subject:    'Gravitiq Newton Error %%message%%'
                level:      debug
                formatter:  monolog.formatter.html
                content_type: text/html
            basic_slack:
                type:           slack
                token:          '%env(SLACK_TOKEN)%'
                channel:        '#newton_alert'
                include_extra:  true
                level:          error
                formatter:      monolog.formatter.html
            gravitiq_console:
                type: console
                process_psr_3_messages: false
                channels: ["gravitiq_console"]
                formatter: monolog.formatter.gravitiq_console
                verbosity_levels:
                    VERBOSITY_NORMAL: INFO
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!gravitiq_console"]
            deprecation:
                type: "null"
                channels: [deprecation]
