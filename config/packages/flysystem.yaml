# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        fly_system_url_accessible_local:
            adapter: 'local'
            options:
                directory: '%app.path.url_accessible%'
        fly_system_amazon_awd_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_awd%'
        fly_system_amazon_crawler_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_crawler%'
        fly_system_amazon_finance_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_finance%'
        fly_system_amazon_listings_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_listings%'
        fly_system_amazon_inventory_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_inventory%'
        fly_system_amazon_fba_inbound_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_fba_inbound%'
        fly_system_amazon_orders_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_orders%'
        fly_system_amazon_reports_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_reports%'
        fly_system_amazon_requests_local:
            adapter: 'local'
            options:
                directory: '%app.path.amazon_requests%'
        fly_system_az_rank_local:
            adapter: 'local'
            options:
                directory: '%app.path.az_rank%'
        fly_system_brand_expand_local:
            adapter: 'local'
            options:
                directory: '%app.path.brand_expand%'
        fly_system_datarova_local:
            adapter: 'local'
            options:
                directory: '%app.path.datarova%'
        fly_system_data_dive_local:
            adapter: 'local'
            options:
                directory: '%app.path.data_dive%'
        fly_system_g_drive_alt_local:
            adapter: 'local'
            options:
                directory: '%app.path.g_drive_alt%'
        fly_system_general_api_local:
            adapter: 'local'
            options:
                directory: '%app.path.general_api%'
        fly_system_keepa_local:
            adapter: 'local'
            options:
                directory: '%app.path.keepa%'
        fly_system_levanta_local:
            adapter: 'local'
            options:
                directory: '%app.path.levanta%'
        fly_system_levanta_crawler_local:
            adapter: 'local'
            options:
                directory: '%app.path.levanta_crawler%'
        fly_system_open_ai_local:
            adapter: 'local'
            options:
                directory: '%app.path.open_ai%'
        fly_system_xero_local:
            adapter: 'local'
            options:
                directory: '%app.path.xero%'
        fly_system_rainforest_local:
            adapter: 'local'
            options:
                directory: '%app.path.rainforest%'
        fly_system_shopify_local:
            adapter: 'local'
            options:
                directory: '%app.path.shopify%'
        fly_system_uploads_admin_local:
            adapter: 'local'
            options:
                directory: '%app.path.uploads_admin%'
        fly_system_uploads_prism_local:
            adapter: 'local'
            options:
                directory: '%app.path.uploads_prism%'
