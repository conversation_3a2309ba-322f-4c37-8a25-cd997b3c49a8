# Latest documentation available at https://omines.github.io/datatables-bundle/#configuration
datatables:
    # Set options, as documented at https://datatables.net/reference/option/
    options:
        lengthMenu : [5, 10, 50, 100, 1000]
        pageLength: 50
        dom: "<'row' <'col-sm-12' tr>><'row' <'col-sm-3' f><'col-sm-3 text-right' l><'col-sm-6 text-right'pi>>"

    template_parameters:
        # Example classes to integrate nicely with Bootstrap 3.x
        className: 'table table-striped table-bordered table-hover data-table compact'

    # You can for example override this to "tables" to keep the translation domains separated nicely
    translation_domain: 'messages'

    method: POST
