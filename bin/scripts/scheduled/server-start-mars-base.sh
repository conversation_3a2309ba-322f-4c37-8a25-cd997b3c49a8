#!/bin/bash

source /home/<USER>/.gravitiqConfig
source /home/<USER>/gravitiqTools.sh
ProjectPath=$(find_newton_project_path "${BASH_SOURCE[0]}")
cd "$ProjectPath" || exit
LogFileMain="$ProjectPath/var/log/scripts/aws-daily.log"
MercuryBaseInstance="i-0370eec11644242f1"
VenusBaseInstance="i-0e3d4f383936d3dc9"
MarsBaseInstance="i-0e18444446823b193"

# wrap in braces (a) to redirect everything to file and (b) to force script into memory to protect from file edits
{
  # aws ec2 start-instances --instance-ids i-0370eec11644242f1 --region us-east-1 --profile Newton_S3_ReadWrite
#  aws ec2 start-instances --instance-ids $MercuryBaseInstance --region us-east-1 --profile Newton_S3_ReadWrite
#  aws ec2 start-instances --instance-ids $VenusBaseInstance --region us-east-1 --profile <PERSON>_S3_ReadWrite
  aws ec2 start-instances --instance-ids $MarsBaseInstance --region us-east-1 --profile Newton_S3_ReadWrite

  exit
} &>> "$LogFileMain"
