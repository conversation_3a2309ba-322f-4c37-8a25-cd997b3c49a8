#!/bin/bash

source /home/<USER>/.gravitiqConfig
source /home/<USER>/gravitiqTools.sh
ProjectPath=$(find_newton_project_path "${BASH_SOURCE[0]}")
cd "$ProjectPath" || exit
LogPath="$ProjectPath/var/log/scripts"
LogFileMain="$LogPath/$(basename "${BASH_SOURCE[0]}" .sh).log"
prod_options=( --env=prod --no-debug --runScript="$(basename "${BASH_SOURCE[0]}")" --runServer="$EC2_SERVER_NAME" )
odi_options=( --allReady --withinHours=3 "${prod_options[@]}" --itemLimit=3000 --limit=6000 --subProcess=30 --subProcessTimeout=10800 --lockMins=1200 --lockExpiresAfter=36000 )
opx_options=( --allReady --allowUpdate "${prod_options[@]}" --lockMins=45 --lockExpiresAfter=180 )
{
  echo "------"
  echo "spa orders-sub-items start"
  date

  php bin/console newton:spa-opo --limit=50000 --subProcess=100 --lockCode=spaOrdParseO "${opx_options[@]}" &>> "$LogPath"/spa-ord-parse-backlog-orders.log
  sleep 5m

  for _ in {1..3}; do
    php bin/console newton:spa-opo --limit=50000 --subProcess=100 --lockCode=spaOrdParseO "${opx_options[@]}" &>> "$LogPath"/spa-ord-parse-backlog-orders.log

    php bin/console newton:spa-opi --limit=100000 --subProcess=100 --lockCode=spaOrdParseI "${opx_options[@]}" &>> "$LogPath"/spa-ord-parse-backlog-items.log &
    php bin/console newton:spa-odi GS%                             --lockCode=spaOrdApi "${odi_options[@]}" &>> "$LogPath"/spa-ord-download-backlog-items-d1.log
    # blank to align with venus
    sleep 3m
    wait
  done

  php bin/console newton:spa-opi --limit=100000 --subProcess=100 --lockCode=spaOrdParseI "${opx_options[@]}" &>> "$LogPath"/spa-ord-parse-backlog-items.log

  date
  echo "spa orders-sub-items end"
  echo "------"
  echo ""

  exit
} &>> "$LogFileMain"
