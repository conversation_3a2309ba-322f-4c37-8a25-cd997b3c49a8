#!/bin/bash

source /home/<USER>/.gravitiqConfig
source /home/<USER>/gravitiqTools.sh
ProjectPath=$(find_newton_project_path "${BASH_SOURCE[0]}")
cd "$ProjectPath" || exit
LogPath="$ProjectPath/var/log/scripts"
LogFileMain="$LogPath/$(basename "${BASH_SOURCE[0]}" .sh).log"

{
  echo "------"
  echo "spa orders-main start"
  date

  "$ProjectPath"/bin/scripts/scheduled/mars-base-daily-orders-sub-download-orders.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-daily-orders-sub-download-items.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-daily-orders-sub-download-orders.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-daily-orders-sub-download-items.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-daily-orders-sub-after.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-backlog-orders-sub-download-orders.sh
  "$ProjectPath"/bin/scripts/scheduled/mars-base-backlog-orders-sub-download-items.sh

  date
  echo "spa orders-main end"
  echo "------"
  echo ""

  exit
} &>> "$LogFileMain"
