#!/bin/bash

source /home/<USER>/.gravitiqConfig
source /home/<USER>/gravitiqTools.sh
ProjectPath=$(find_newton_project_path "${BASH_SOURCE[0]}")
cd "$ProjectPath" || exit
LogPath="$ProjectPath/var/log/scripts"
LogFileMain="$LogPath/weekly-thursday.log"
prod_options=( --env=prod --no-debug --runScript="$(basename "${BASH_SOURCE[0]}")" --runServer="$EC2_SERVER_NAME" )

{
  {
    echo "------"
    echo "thursday start"
    date

#    bin/console newton:build:udr % "today midnight" "${prod_options[@]}"

    date
    echo "thursday end"
    echo "------"
    echo ""
  } &>> "$LogFileMain"
}
