#!/bin/bash

source /home/<USER>/.gravitiqConfig
source /home/<USER>/gravitiqTools.sh
ProjectPath=$(find_newton_project_path "${BASH_SOURCE[0]}")
cd "$ProjectPath" || exit
LogPath="$ProjectPath/var/log/scripts"
LogFile="$LogPath/sqd-all.log"
ScriptName="VENUS_BACKLOG_NAM (SQD)"
StartDate="2024-01-01"
EndDate=$(date --date="$(date +'%Y-%m-01') - 2 month" +%Y-%m-%d)
prod_options=( --env=prod --no-debug --runScript="$(basename "${BASH_SOURCE[0]}")" --runServer="$EC2_SERVER_NAME" )
LockCode=( "${prod_options[@]}" --lockCode=sqx-"$EC2_SERVER_NAME" --lockMins=900 --lockExpiresAfter=1200 )

{
  echo "------"
  echo "$ScriptName start"
  php bin/console newton:message:slack SERVER_CRAWL_START --body="${ScriptName}" "${prod_options[@]}"
  date

  # shellcheck disable=SC2043
  for country in US CA; do
    for brand in BB DM GS; do
      echo "Start ${brand} ${country}"
      php bin/console newton:crawl-sc:get-sqd ${brand}_US --country=${country} "${StartDate}" "${EndDate}" "${LockCode[@]}" >> "$LogPath/crawlSqp-${brand}_${country}.log" 2>&1
      echo "Done ${brand} ${country}"
      date
    done
  done

  date
  php bin/console newton:message:slack SERVER_CRAWL_COMPLETE --body="${ScriptName}" "${prod_options[@]}"
  echo "$ScriptName end"
  echo "------"
  echo ""

  exit
} &>> "$LogFile"
