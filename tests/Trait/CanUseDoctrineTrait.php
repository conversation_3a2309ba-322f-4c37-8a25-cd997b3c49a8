<?php

declare(strict_types=1);

namespace App\Tests\Trait;

use App\Service\Trait\HasEntityManagerTrait;
use App\Tests\FunctionalTester;
use App\Tests\UnitTester;
use App\Tools\GravitiqTools;
use Codeception\Exception\ModuleException;
use Codeception\Module\Symfony;
use Doctrine\Bundle\DoctrineBundle\Registry;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Exception\NotSupported;

trait CanUseDoctrineTrait
{
    use CanLoadFixturesTrait, HasEntityManagerTrait;

    protected UnitTester $tester;
    protected Symfony $symfony;

    protected function initDoctrine(): void
    {
        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
            if (!($symfony instanceof Symfony)) {
                throw new ModuleException('',''); // blank because it's just caught below
            }
            $this->symfony = $symfony;

            /** @var Registry $doctrine */
            $doctrine = $this->grabService('doctrine');
            $this->em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
        } catch (ModuleException) {
            $this->fail('Could not retrieve Symfony or Doctrine modules');
        }
    }

    protected function initDoctrineFromFunctionalTester(FunctionalTester $I): void
    {
        /** @var Registry $doctrine */
        $doctrine = $I->grabService('doctrine');
        $this->em = GravitiqTools::getEmFromDoctrine($doctrine, __CLASS__);
    }

    /**
     * @template Tin of object
     * @param class-string<Tin> $className
     * @phpstan-return Tin
     * @return object
     */
    protected function grabService($className): object
    {
        return $this->symfony->grabService($className);
    }

    protected function beginTransaction(): void
    {
        try {
            $this->em->getConnection()->beginTransaction();
        } catch (Exception $e) {
            $this->fail('Could not begin transaction: ' . $e->getMessage());
        }
    }

    protected function rollBackTransaction(string|array|null $postRollbackSql = null, bool $keepConnectionOpen=false): void
    {
        if ($this->em->getConnection()->isTransactionActive()) {
            try {
                $this->em->getConnection()->rollBack();
            } catch (Exception $e) {
                $this->fail('Could not roll back transaction: ' . $e->getMessage());
            }
        }

        if (!empty($postRollbackSql)) {
            if (!is_array($postRollbackSql)) {
                $postRollbackSql = [$postRollbackSql];
            }

            foreach ($postRollbackSql as $sql) {
                try {
                    $this->em->getConnection()->executeQuery($sql);
                } catch (Exception $e) {
                    $this->fail('Could not execute post-rollback SQL: ' . $e->getMessage());
                }
            }
        }

        if (!$keepConnectionOpen) {
            $this->em->close();
            $this->em->getConnection()->close();
        }
    }

    /**
     * @template Tin of object
     * @param class-string<Tin> $entityClass
     * @param mixed $id
     * @return Tin|null
     */
    protected function retrieveEntityById($entityClass, $id) {
        return $this->getRepository($entityClass)->find($id);
    }

    /**
     * @template Tin of object
     * @param class-string<Tin> $entityClass
     * @param mixed $criteria
     * @return Tin|null
     */
    protected function retrieveOneEntityByCriteria($entityClass, $criteria) {
        return $this->getRepository($entityClass)->findOneBy($criteria);
    }

    /**
     * @template Tin of object
     * @param class-string<Tin> $entityClass
     * @param mixed $criteria
     * @param null|string[] $orderBy
     * @return Tin[]|null
     */
    protected function retrieveAllOfEntity($entityClass, $criteria=[], $orderBy=null): ?array
    {
        if (empty($criteria)) {
            return $this->getRepository($entityClass)->findAll();
        } else {
            return $this->getRepository($entityClass)->findBy($criteria, $orderBy);
        }
    }

    /**
     * @param class-string $entityClass
     * @param mixed $criteria
     * @return int|null
     */
    protected function retrieveCountOfEntity(string $entityClass, $criteria=null): ?int
    {
        $entities = $this->retrieveAllOfEntity($entityClass, $criteria);
        if (is_null($entities)) {
            return null;
        }
        return count($entities);
    }

    /**
     * @template Tin of object
     * @param array<class-string<Tin>, int> $entityCountMap
     * @param bool $returnEntities
     * @return array{class-string<Tin>, list<Tin>}|null
     */
    protected function assertEntityCounts(array $entityCountMap, bool $returnEntities=false): ?array
    {
        $retrievedEntities = [];
        foreach ($entityCountMap as $entityClass => $expectedCount) {
            $entities = $this->retrieveAllOfEntity($entityClass);
            $this->assertCount($expectedCount, $entities, "Entity count mismatch for $entityClass");
            if ($returnEntities) {
                $retrievedEntities[$entityClass] = $entities;
            }
        }
        if ($returnEntities) {
            return $retrievedEntities;
        }
        return null;
    }

    /**
     * @param array<class-string, int> $initialCounts
     * @param array<class-string, int> $expectedAddition
     * @return array<class-string, int>
     */
    protected function assertEntityCountsAreAdjusted(array $initialCounts, array $expectedAddition): array
    {
        $expectedCounts = [];
        foreach ($expectedAddition as $key => $value) {
            $expectedCounts[$key] = $initialCounts[$key] + $value;
        }
        $this->assertEntityCounts($expectedCounts);
        return $expectedCounts;
    }

    /**
     * @template Tin of object
     * @param class-string<Tin> $entityClass
     * @return EntityRepository<Tin>
     */
    protected function getRepository(string $entityClass): EntityRepository
    {
        try {
            return $this->em->getRepository($entityClass);
        } catch (NotSupported) {
            throw new \RuntimeException("Could not get repository for $entityClass");
        }
    }
}
