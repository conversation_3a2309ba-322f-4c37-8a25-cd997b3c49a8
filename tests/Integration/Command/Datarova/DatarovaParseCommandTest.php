<?php

namespace App\Tests\Integration\Command\Datarova;

use App\Command\Datarova\DatarovaParseCommand;
use  App\DataFixtures\Service\ReportParser\DatarovaFixtures as Fixtures;
use App\Service\Datarova\DatarovaParser;
use App\Tests\Integration\DoctrineTransactionUnitTest;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use Codeception\Exception\ModuleException;
use Codeception\Module\Symfony;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Tester\CommandTester;

class DatarovaParseCommandTest extends DoctrineTransactionUnitTest
{
    use CanInvokeProtectedMethodTrait;

    protected DatarovaParser $parser;

    protected function _before(): void
    {
        parent::_before();

        /** @var DatarovaParser $parser */
        $parser = $this->grabService(DatarovaParser::class);
        $this->parser = $parser;
    }

    protected function getFixtureClasses() :array
    {
        return [Fixtures::class];
    }

    public function testBasicCommandExecution(): void
    {
        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
        } catch (ModuleException $e) {
            $this->fail('Could not retrieve Symfony module.');
        }

        $loggerMock = $this->createMock(LoggerInterface::class);
        $datarovaParserMock = $this->createMock(DatarovaParser::class);

        $datarovaParserMock->expects($this->atMost(1))
            ->method('parseEntity');

        /** @var ManagerRegistry $doctrineManager */
        $doctrineManager = $symfony->grabService('doctrine');

        $command = new DatarovaParseCommand(
            $doctrineManager,
            $loggerMock,
            $datarovaParserMock
        );

        $command->setApplication(new Application($symfony->kernel));

        $commandTester = new CommandTester($command);

        $commandTester->execute([
            'command' => $command->getName(),
        ]);

        $commandTester->assertCommandIsSuccessful();
    }

    public function testCommandWithLimitParameter(): void
    {
        Fixtures::loadThreeUnparsedRequests($this->em);

        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
        } catch (ModuleException $e) {
            $this->fail('Could not retrieve Symfony module.');
        }

        $loggerMock = $this->createMock(LoggerInterface::class);
        $datarovaParserMock = $this->createMock(DatarovaParser::class);

        $datarovaParserMock->expects($this->atMost(1))
            ->method('parseEntity');

        /** @var ManagerRegistry $doctrineManager */
        $doctrineManager = $symfony->grabService('doctrine');

        $command = new DatarovaParseCommand(
            $doctrineManager,
            $loggerMock,
            $datarovaParserMock
        );

        $command->setApplication(new Application($symfony->kernel));

        $commandTester = new CommandTester($command);

        $commandTester->execute([
            'command' => $command->getName(),
            '--limit' => 1,
        ]);

        $commandTester->assertCommandIsSuccessful();
    }

    public function testCommandWithNoParameters(): void
    {
        Fixtures::loadTwoUnparsedRequestsOnlyOneOfWhichIsParsable($this->em);

        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
        } catch (ModuleException $e) {
            $this->fail('Could not retrieve Symfony module.');
        }

        $loggerMock = $this->createMock(LoggerInterface::class);
        $datarovaParserMock = $this->createMock(DatarovaParser::class);

        $datarovaParserMock->expects($this->atMost(2))
            ->method('parseEntity');

        /** @var ManagerRegistry $doctrineManager */
        $doctrineManager = $symfony->grabService('doctrine');

        $command = new DatarovaParseCommand(
            $doctrineManager,
            $loggerMock,
            $datarovaParserMock
        );

        $command->setApplication(new Application($symfony->kernel));

        $commandTester = new CommandTester($command);

        $commandTester->execute([
            'command' => $command->getName(),
        ]);
    }

    public function testCommandWithAccountIdParameter(): void
    {
        Fixtures::loadUnparsedRequestsWithAccountIds($this->em);

        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
        } catch (ModuleException $e) {
            $this->fail('Could not retrieve Symfony module.');
        }

        $loggerMock = $this->createMock(LoggerInterface::class);
        $datarovaParserMock = $this->createMock(DatarovaParser::class);

        $datarovaParserMock->expects($this->atMost(2))
            ->method('parseEntity');

        /** @var ManagerRegistry $doctrineManager */
        $doctrineManager = $symfony->grabService('doctrine');

        $command = new DatarovaParseCommand(
            $doctrineManager,
            $loggerMock,
            $datarovaParserMock
        );

        $command->setApplication(new Application($symfony->kernel));

        $commandTester = new CommandTester($command);

        $commandTester->execute([
            'command' => $command->getName(),
            '--accountId' => 1,
        ]);
    }

    public function testCommandWithSubProcessParameter(): void
    {
        try {
            /** @var Symfony $symfony */
            $symfony = $this->getModule('Symfony');
        } catch (ModuleException $e) {
            $this->fail('Could not retrieve Symfony module.');
        }

        $loggerMock = $this->createMock(LoggerInterface::class);
        $datarovaParserMock = $this->createMock(DatarovaParser::class);

        $datarovaParserMock->expects($this->atMost(1))
            ->method('parseEntity');

        /** @var ManagerRegistry $doctrineManager */
        $doctrineManager = $symfony->grabService('doctrine');

        $command = new DatarovaParseCommand(
            $doctrineManager,
            $loggerMock,
            $datarovaParserMock
        );

        $command->setApplication(new Application($symfony->kernel));

        $commandTester = new CommandTester($command);

        $commandTester->execute([
            'command' => $command->getName(),
            '--subProcess' => 3,
            '--limit' => 6,
        ]);

        $commandTester->assertCommandIsSuccessful();
    }
}