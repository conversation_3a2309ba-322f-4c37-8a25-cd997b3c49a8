<?php


namespace App\Tests\Integration\Repository;

use App\Entity\AccountAmazon;
use App\Entity\Brand;
use App\Repository\AccountAmazonRepository;
use App\Tests\Trait\CanUseDoctrineTrait;
use App\Tests\UnitTester;
use Codeception\Test\Unit;

class AccountAmazonRepositoryTest extends Unit
{
    use CanUseDoctrineTrait;

    protected UnitTester $tester;
    protected AccountAmazonRepository $repo;

    protected function _before(): void
    {
        $this->initDoctrine();
        $this->beginTransaction();

        /** @var AccountAmazonRepository $accountRepo */
        $accountRepo = $this->getRepository(AccountAmazon::class);
        $this->repo = $accountRepo;
    }

    protected function _after(): void
    {
        $this->rollBackTransaction();
    }

    // tests
    public function testRetrieveBrandCodesMatching(): void
    {
        $brandCodes = $this->repo->retrieveBrandCodesMatching('%');
        $this->assertCount(1, $brandCodes, 'Should dedupe and only return one brand code');
        $this->assertEquals(['ZZ'], $brandCodes, 'Should return array of brandCode strings');

        $this->testWildcardCounts(0);


        $this->addBrandWhichMatchesTrailingWildcard();


        $brandCodes = $this->repo->retrieveBrandCodesMatching('%');
        $this->assertCount(2, $brandCodes, 'Should dedupe and only return one brand code');
        sort($brandCodes); // spec doesn't require a particular order, so sort just for test comparison
        $this->assertEquals(['ZX','ZZ'], $brandCodes, 'Should return array of brandCode strings');

        $this->testWildcardCounts(1);
    }

    protected function addBrandWhichMatchesTrailingWildcard(): void
    {
        $newBrand = new AccountAmazon();
        $newBrand->setBrandCode('ZX');
        $newBrand->setRegion('NAM');
        $newBrand->setAccountName('Test ZX');
        $newBrand->setRoleArn('');
        $newBrand->setAwsAccessKeyId('');
        $newBrand->setAwsSecretAccessKey('');
        $newBrand->setLwaClientId('');
        $newBrand->setLwaClientSecret('');
        $this->persist($newBrand);
        $this->flushChanges();
    }
    protected function testWildcardCounts(int $extraBrandsAdded = 0): void
    {
        $brandCodes = $this->repo->retrieveBrandCodesMatching('Z');
        $this->assertCount(0, $brandCodes, 'Should not treat brandCodePartial as a wildcard unless it contains one');

        $brandCodes = $this->repo->retrieveBrandCodesMatching('Z%');
        $this->assertCount(1 + $extraBrandsAdded, $brandCodes, 'Should work with trailing wildcard');

        $brandCodes = $this->repo->retrieveBrandCodesMatching('%Z');
        $this->assertCount(1, $brandCodes, 'Should work with leading wildcard');

        $brandCodes = $this->repo->retrieveBrandCodesMatching('ZZ');
        $this->assertCount(1, $brandCodes, 'Should work with complete brandCode wildcard');

        $brandCodes = $this->repo->retrieveBrandCodesMatching('X%');
        $this->assertCount(0, $brandCodes, 'Should return no matches with non-existing partial');
    }
}
