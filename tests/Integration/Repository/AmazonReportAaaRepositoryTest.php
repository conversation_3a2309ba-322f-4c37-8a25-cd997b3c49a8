<?php

namespace App\Tests\Integration\Repository;

use App\DataFixtures\Repository\AmazonReportAaaRepositoryFixtures as Fixtures;
use App\Entity\AmazonReportAaa;
use App\Repository\AmazonReportAaaRepository;
use App\Tests\Integration\DoctrineTransactionUnitTest;

class AmazonReportAaaRepositoryTest extends DoctrineTransactionUnitTest
{
    protected AmazonReportAaaRepository $repo;

    protected function getFixtureClasses() :array
    {
        return [Fixtures::class];
    }

    protected function _before(): void
    {
        parent::_before();

        $this->repo = $this->em->getRepository(AmazonReportAaa::class);
    }

    // tests
    public function testRetrieveAllReadyToParse()
    {
        /** @var AmazonReportAaa[] $results */
        $results = $this->repo->retrieveAllReadyToParse();
        $this->assertCount(1, $results, 'Should only return one record');
        foreach ($results as $result) {
            $this->assertEquals(Fixtures::REPORT_ID_READY_TO_PARSE, $result->getAzReportId(), 'Should return known report');
        }

        $ro = $this->repo->initRo();
        $ro->setOptionReadyToParse(true);
        $ro->setOptionUnexpectedZero(true);

        /** @var AmazonReportAaa[] $results */
        $results = $this->repo->retrieveAllReadyToParse(null, $ro);
        $this->assertCount(1, $results, 'Should only return one record');
        foreach ($results as $result) {
            $this->assertEquals(Fixtures::REPORT_ID_UNEXPECTED_ZERO, $result->getAzReportId(), 'Should return known report');
        }
    }
}
