<?php

namespace App\Tests\Integration\Service\Shopify;

use App\DataFixtures\Service\ReportParser\ParseShopifyProductFixtures as Fixtures;
use App\Entity\Shopify\Product;
use App\Entity\Shopify\ProductImage;
use App\Entity\Shopify\ProductOption;
use App\Entity\Shopify\ProductVariant;
use App\Repository\Shopify\ProductImageRepository;
use App\Repository\Shopify\ProductOptionRepository;
use App\Repository\Shopify\ProductVariantRepository;
use App\Service\Shopify\ShopifyManager;
use App\Service\Trait\FileSystemSwitchboardTrait;
use App\Tests\Integration\DoctrineTransactionUnitTest;
use App\Tests\Trait\CanUseDoctrineTrait;
use App\Tests\Trait\Unit\DoctrineMockBuilderTrait;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Monolog\Handler\TestHandler;
use Monolog\Logger;

class ShopifyManagerParseShopifyProductTest extends DoctrineTransactionUnitTest
{
    use DoctrineMockBuilderTrait;
    use CanUseDoctrineTrait;
    use FileSystemSwitchboardTrait;
    protected ShopifyManager $manager;
    protected ProductVariantRepository $productVariantRepo;
    protected ProductOptionRepository $productOptionRepo;
    protected ProductImageRepository $productImageRepo;

    protected function _before(): void
    {
        parent::_before();

        /** @var ShopifyManager $manager */
        $manager = $this->grabService(ShopifyManager::class);
        $this->manager = $manager;

        /** @var ProductVariantRepository $productVariantRepo */
        $productVariantRepo = $this->getRepository(ProductVariant::class);
        $this->productVariantRepo = $productVariantRepo;

        /** @var ProductOptionRepository $productOptionRepo */
        $productOptionRepo = $this->getRepository(ProductOption::class);
        $this->productOptionRepo = $productOptionRepo;

        /** @var ProductImageRepository $productImageRepo */
        $productImageRepo = $this->getRepository(ProductImage::class);
        $this->productImageRepo = $productImageRepo;
    }

    protected function getFixtureClasses() :array
    {
        return [Fixtures::class];
    }

    private function createTestDataForSuccess(): void
    {
        $apiRequest = Fixtures::createShopifyApiRequestProductCreateNewRecord($this->em);
        $responseData = $this->manager->loadResponseFromFile($apiRequest);

        foreach ($responseData as $productData) {
            $this->manager->parseShopifyProduct($productData, $apiRequest);
        }
    }

    private function createTestDataForFail(): void
    {
        $apiRequest = Fixtures::createShopifyApiRequestProductEmptyDataInFile($this->em);
        $responseData = $this->manager->loadResponseFromFile($apiRequest);

        foreach ($responseData as $productData) {
            $this->manager->parseShopifyProduct($productData, $apiRequest);
        }
    }

    /**
     * @given a shopify product data
     * @when the data is parsed
     * @then the product, product variant, product option, and product image records are created
     * @and if there are manual edits to the data, the records are updated
     * @and if reparsed with the same data, the records are not duplicated but updated and the manual updates are discarded
     * @and if the records are soft-deleted, they are restored when reparsed
     */
    public function testParseShopifyProductCreateNewRecord(): void
    {
        $apiRequest = Fixtures::createShopifyApiRequestProductCreateNewRecord($this->em);
        $responseData = $this->manager->loadResponseFromFile($apiRequest);

        $this->assertNull($this->retrieveOneEntityByCriteria(Product::class, ['spfProductId' => 'SPF1']), 'Product should not exist before parsing');
        $this->assertNull($this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfProductId' => 'SPF1']), 'ProductVariant should not exist before parsing');
        $this->assertNull($this->retrieveOneEntityByCriteria(ProductOption::class, ['spfProductId' => 'SPF1']), 'ProductOption should not exist before parsing');
        $this->assertNull($this->retrieveOneEntityByCriteria(ProductImage::class, ['spfProductId' => 'SPF1']), 'ProductImage should not exist before parsing');

        foreach ($responseData as $productData) {
            $this->manager->parseShopifyProduct($productData, $apiRequest);
        }

        $this->assertEquals(1, $this->countDataRecordsForShopifyProduct());
        $this->assertEquals(2, $this->countDataRecordsForShopifyProductVariant());
        $this->assertEquals(2, $this->countDataRecordsForShopifyProductOption());
        $this->assertEquals(2, $this->countDataRecordsForShopifyProductImage());

        $product = $this->retrieveOneEntityByCriteria(Product::class, ['spfProductId' => 'SPF1']);
        $productVariant1stSet = $this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR1']);
        $productOption1stSet = $this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT1']);
        $productImage1stSet = $this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG1']);
        $productVariant2ndSet = $this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR2']);
        $productOption2ndSet = $this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT2']);
        $productImage2ndSet = $this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG2']);

        $this->assertParseShopifyProductCreateNewRecord($product, $productVariant1stSet, $productOption1stSet, $productImage1stSet, $productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);

        $this->manuallyEditDataOf1stSetOfVariantOptionImage($productVariant1stSet, $productOption1stSet, $productImage1stSet);
        $this->manuallyEditIdsOf2ndSetOfVariantOptionImage($productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);

        $this->assert1stSetOfDataHasUpdatedFieldsWithIdsRetained($productVariant1stSet, $productOption1stSet, $productImage1stSet);
        $this->assert2ndSetOfDataHasUpdatedIds($productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);

        foreach ($responseData as $productData) {
            $this->manager->parseShopifyProduct($productData, $apiRequest);
        }

        $product = $this->retrieveOneEntityByCriteria(Product::class, ['spfProductId' => 'SPF1']);
        try {
            $productVariant1stSet = $this->productVariantRepo->retrieveOneByShopifyVariantIdAndProductId('VAR1', 'SPF1');
            $productOption1stSet = $this->productOptionRepo->retrieveOneByShopifyOptionIdAndProductId('OPT1', 'SPF1');
            $productImage1stSet = $this->productImageRepo->retrieveOneByShopifyImageIdAndProductId('IMG1', 'SPF1');
        } catch (NonUniqueResultException $e) {
            $this->fail($e->getMessage());
        }
        $this->assertNotNull($productVariant1stSet, 'Record should exist at start of test');
        $this->assertNotNull($productOption1stSet, 'Record should exist at start of test');
        $this->assertNotNull($productImage1stSet, 'Record should exist at start of test');

        $productVariant2ndSet = $this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR2']);
        $productOption2ndSet = $this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT2']);
        $productImage2ndSet = $this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG2']);

        $this->assertEquals(1, $this->countDataRecordsForShopifyProduct());
        $this->assertParseShopifyProductCreateNewRecord($product, $productVariant1stSet, $productOption1stSet, $productImage1stSet, $productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);

        $this->assert1stSetOfDataHasIdsRetained($productVariant1stSet, $productOption1stSet, $productImage1stSet);

        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR2']));
        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT2']));
        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG2']));

        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR1', 'deletedAt' => null]));
        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT1', 'deletedAt' => null]));
        $this->assertNotNull($this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG1', 'deletedAt' => null]));

        try {
            $this->em->remove($productVariant1stSet);
            $this->em->remove($productOption1stSet);
            $this->em->remove($productImage1stSet);
            $this->em->flush();
        } catch (ORMException $e) {
            $this->fail($e->getMessage());
        }

        try {
            $existingSoftDeletedProductVariantRecord = $this->productVariantRepo->retrieveOneByShopifyVariantIdAndProductId('VAR1', 'SPF1');
            $existingSoftDeletedProductOptionRecord = $this->productOptionRepo->retrieveOneByShopifyOptionIdAndProductId('OPT1', 'SPF1');
            $existingSoftDeletedProductImageRecord = $this->productImageRepo->retrieveOneByShopifyImageIdAndProductId('IMG1', 'SPF1');
        } catch (NonUniqueResultException $e) {
            $this->fail($e->getMessage());
        }
        $this->assertNull($existingSoftDeletedProductVariantRecord, 'Record should not exist after being soft-deleted');
        $this->assertNull($existingSoftDeletedProductOptionRecord, 'Record should not exist after being soft-deleted');
        $this->assertNull($existingSoftDeletedProductImageRecord, 'Record should not exist after being soft-deleted');

        try {
            $existingSoftDeletedProductVariantRecord = $this->productVariantRepo->retrieveOneByShopifyVariantIdAndProductId('VAR1', 'SPF1', true);
            $existingSoftDeletedProductOptionRecord = $this->productOptionRepo->retrieveOneByShopifyOptionIdAndProductId('OPT1', 'SPF1', true);
            $existingSoftDeletedProductImageRecord = $this->productImageRepo->retrieveOneByShopifyImageIdAndProductId('IMG1', 'SPF1', true);
        } catch (NonUniqueResultException $e) {
            $this->fail($e->getMessage());
        }
        $this->assertNotNull($existingSoftDeletedProductVariantRecord, 'Record should still exist after being soft-deleted');
        $this->assertNotNull($existingSoftDeletedProductOptionRecord, 'Record should still exist after being soft-deleted');
        $this->assertNotNull($existingSoftDeletedProductImageRecord, 'Record should still exist after being soft-deleted');

        foreach ($responseData as $productData) {
            $this->manager->parseShopifyProduct($productData, $apiRequest);
        }

        try {
            $existingProductVariantRecord = $this->productVariantRepo->retrieveOneByShopifyVariantIdAndProductId('VAR1', 'SPF1');
            $existingProductOptionRecord = $this->productOptionRepo->retrieveOneByShopifyOptionIdAndProductId('OPT1', 'SPF1');
            $existingProductImageRecord = $this->productImageRepo->retrieveOneByShopifyImageIdAndProductId('IMG1', 'SPF1');
        } catch (NonUniqueResultException $e) {
            $this->fail($e->getMessage());
        }
        $this->assertNotNull($existingProductVariantRecord, 'Record should be restored and undeleted');
        $this->assertNotNull($existingProductOptionRecord, 'Record should be restored and undeleted');
        $this->assertNotNull($existingProductImageRecord, 'Record should be restored and undeleted');
    }

    protected function manuallyEditDataOf1stSetOfVariantOptionImage(ProductVariant $productVariant1stSet, ProductOption $productOption1stSet, ProductImage $productImage1stSet): void
    {
        $productVariant1stSet->setTitle('Variant 1 UPDATED');
        $productVariant1stSet->setPrice('100.00');

        $productOption1stSet->setName('Option 1 UPDATED');
        $productOption1stSet->setOptionsValues('Option 1 UPDATED');

        $productImage1stSet->setAlt('Image 1 UPDATED');
        $productImage1stSet->setHeight(5000);

        $this->flushChanges();
    }

    protected function manuallyEditIdsOf2ndSetOfVariantOptionImage(ProductVariant $productVariant2ndSet, ProductOption $productOption2ndSet, ProductImage $productImage2ndSet): void
    {
        $productVariant2ndSet->setSpfProductId('SPF2');
        $productVariant2ndSet->setSpfVariantId('VAR3');
        $productOption2ndSet->setSpfProductId('SPF2');
        $productOption2ndSet->setSpfOptionsId('OPT3');
        $productImage2ndSet->setSpfProductId('SPF2');
        $productImage2ndSet->setSpfImageId('IMG3');
        $this->flushChanges();
    }

    protected function assert2ndSetOfDataHasUpdatedIds(ProductVariant $productVariant2ndSet, ProductOption $productOption2ndSet, ProductImage $productImage2ndSet): void
    {
        $this->assertEquals('VAR3', $productVariant2ndSet->getSpfVariantId());
        $this->assertEquals('OPT3', $productOption2ndSet->getSpfOptionsId());
        $this->assertEquals('IMG3', $productImage2ndSet->getSpfImageId());
    }

    protected function assert1stSetOfDataHasUpdatedFieldsWithIdsRetained(ProductVariant $productVariant1stSet, ProductOption $productOption1stSet, ProductImage $productImage1stSet): void
    {
        $this->assertEquals('VAR1', $productVariant1stSet->getSpfVariantId());
        $this->assertEquals('Variant 1 UPDATED', $productVariant1stSet->getTitle());
        $this->assertEquals('100.00', $productVariant1stSet->getPrice());

        $this->assertEquals('OPT1', $productOption1stSet->getSpfOptionsId());
        $this->assertEquals('Option 1 UPDATED', $productOption1stSet->getName());
        $this->assertEquals('Option 1 UPDATED', $productOption1stSet->getOptionsValues());

        $this->assertEquals('IMG1', $productImage1stSet->getSpfImageId());
        $this->assertEquals('Image 1 UPDATED', $productImage1stSet->getAlt());
        $this->assertEquals(5000, $productImage1stSet->getHeight());
    }

    protected function assert1stSetOfDataHasIdsRetained(ProductVariant $productVariant1stSet, ProductOption $productOption1stSet, ProductImage $productImage1stSet): void
    {
        $this->assertEquals('VAR1', $productVariant1stSet->getSpfVariantId());
        $this->assertEquals('OPT1', $productOption1stSet->getSpfOptionsId());
        $this->assertEquals('IMG1', $productImage1stSet->getSpfImageId());
    }

    protected function assertParseShopifyProductCreateNewRecord(
        Product $product,
        ProductVariant $productVariant1stSet,
        ProductOption $productOption1stSet,
        ProductImage $productImage1stSet,
        ProductVariant $productVariant2ndSet,
        ProductOption $productOption2ndSet,
        ProductImage $productImage2ndSet
    ): void
    {
        $this->assertEquals('SPF1', $product->getSpfProductId());
        $this->assertEquals('Product 1', $product->getTitle());
        $this->assertEquals('<p class="p1"> Product 1 </p>', $product->getBodyHtml());
        $this->assertEquals('Baebody Beauty', $product->getVendor());
        $this->assertEquals('tea', $product->getProductType());
        $this->assertDates('2020-06-18 21:27:43', $product->getCreatedAt());
        $this->assertEquals('14-day-detox-tea-bags', $product->getHandle());
        $this->assertDates('2023-08-31 06:25:38', $product->getUpdatedAt());
        $this->assertDates('2022-11-28 10:47:49', $product->getPublishedAt());
        $this->assertEquals('product-3ingredients', $product->getTemplateSuffix());
        $this->assertEquals('active', $product->getStatus());
        $this->assertEquals('web', $product->getPublishedScope());
        $this->assertEquals('_badge_Best_Seller, Blends, live, Tea', $product->getTags());
        $this->assertEquals('gid://shopify/Product/4634759692382', $product->getAdminGraphqlApiId());

        $this->assertEquals('VAR1', $productVariant1stSet->getSpfVariantId());
        $this->assertEquals('SPF1', $productVariant1stSet->getSpfProductId());
        $this->assertEquals('Variant 1', $productVariant1stSet->getTitle());
        $this->assertEquals('25.00', $productVariant1stSet->getPrice());
        $this->assertEquals('B801C', $productVariant1stSet->getSku());
        $this->assertEquals(1, $productVariant1stSet->getPosition());
        $this->assertEquals('deny', $productVariant1stSet->getInventoryPolicy());
        $this->assertEquals('25.00', $productVariant1stSet->getCompareAtPrice());
        $this->assertEquals('manual', $productVariant1stSet->getFulfillmentService());
        $this->assertEquals('shopify', $productVariant1stSet->getInventoryManagement());
        $this->assertEquals('Option 1', $productVariant1stSet->getOption1());
        $this->assertEquals('Option 2', $productVariant1stSet->getOption2());
        $this->assertNull($productVariant1stSet->getOption3());
        $this->assertDates('2023-02-01 15:13:29', $productVariant1stSet->getCreatedAt());
        $this->assertDates('2023-08-31 06:05:32', $productVariant1stSet->getUpdatedAt());
        $this->assertTrue($productVariant1stSet->isTaxable());
        $this->assertEquals('858466006100', $productVariant1stSet->getBarcode());
        $this->assertEquals(10, $productVariant1stSet->getGrams());
        $this->assertEquals('IMG1', $productVariant1stSet->getSpfImageId());
        $this->assertEquals(5, $productVariant1stSet->getWeight());
        $this->assertEquals('lb', $productVariant1stSet->getWeightUnit());
        $this->assertEquals(12345678901231, $productVariant1stSet->getSpfInventoryItemId());
        $this->assertEquals(33, $productVariant1stSet->getInventoryQuantity());
        $this->assertEquals(33, $productVariant1stSet->getOldInventoryQuantity());
        $this->assertTrue($productVariant1stSet->isRequiresShipping());
        $this->assertEquals('gid://shopify/ProductVariant/VAR1', $productVariant1stSet->getAdminGraphqlApiId());

        $this->assertEquals('VAR2', $productVariant2ndSet->getSpfVariantId());
        $this->assertEquals('SPF1', $productVariant2ndSet->getSpfProductId());
        $this->assertEquals('Variant 2', $productVariant2ndSet->getTitle());
        $this->assertEquals('50.00', $productVariant2ndSet->getPrice());
        $this->assertEquals('B801D', $productVariant2ndSet->getSku());
        $this->assertEquals(2, $productVariant2ndSet->getPosition());
        $this->assertEquals('approve', $productVariant2ndSet->getInventoryPolicy());
        $this->assertEquals('50.00', $productVariant2ndSet->getCompareAtPrice());
        $this->assertEquals('auto', $productVariant2ndSet->getFulfillmentService());
        $this->assertEquals('shopify', $productVariant2ndSet->getInventoryManagement());
        $this->assertEquals('Option 1', $productVariant2ndSet->getOption1());
        $this->assertEquals('Option 2', $productVariant2ndSet->getOption2());
        $this->assertNull($productVariant2ndSet->getOption3());
        $this->assertDates('2023-02-01 15:13:29', $productVariant2ndSet->getCreatedAt());
        $this->assertDates('2023-08-31 06:05:32', $productVariant2ndSet->getUpdatedAt());
        $this->assertTrue($productVariant2ndSet->isTaxable());
        $this->assertEquals('858466006101', $productVariant2ndSet->getBarcode());
        $this->assertEquals(11, $productVariant2ndSet->getGrams());
        $this->assertEquals('IMG2', $productVariant2ndSet->getSpfImageId());
        $this->assertEquals(6, $productVariant2ndSet->getWeight());
        $this->assertEquals('lb', $productVariant2ndSet->getWeightUnit());
        $this->assertEquals(12345678901232, $productVariant2ndSet->getSpfInventoryItemId());
        $this->assertEquals(66, $productVariant2ndSet->getInventoryQuantity());
        $this->assertEquals(66, $productVariant2ndSet->getOldInventoryQuantity());
        $this->assertTrue($productVariant2ndSet->isRequiresShipping());
        $this->assertEquals('gid://shopify/ProductVariant/VAR2', $productVariant2ndSet->getAdminGraphqlApiId());

        $this->assertEquals('OPT1', $productOption1stSet->getSpfOptionsId());
        $this->assertEquals('SPF1', $productOption1stSet->getSpfProductId());
        $this->assertEquals('Size', $productOption1stSet->getName());
        $this->assertEquals(1, $productOption1stSet->getPosition());
        $this->assertEquals('Option 1', $productOption1stSet->getOptionsValues());

        $this->assertEquals('OPT2', $productOption2ndSet->getSpfOptionsId());
        $this->assertEquals('SPF1', $productOption2ndSet->getSpfProductId());
        $this->assertEquals('Size', $productOption2ndSet->getName());
        $this->assertEquals(2, $productOption2ndSet->getPosition());
        $this->assertEquals('Option 2', $productOption2ndSet->getOptionsValues());

        $this->assertEquals('IMG1', $productImage1stSet->getSpfImageId());
        $this->assertEquals('SPF1', $productImage1stSet->getSpfProductId());
        $this->assertEquals(1, $productImage1stSet->getPosition());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getCreatedAt());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getUpdatedAt());
        $this->assertEquals('14 Day Detox Tea - Baebody', $productImage1stSet->getAlt());
        $this->assertEquals(1500, $productImage1stSet->getWidth());
        $this->assertEquals(1500, $productImage1stSet->getHeight());
        $this->assertEquals('https://cdn.shopify.com/s/files/1/0278/6839/7662/products/14-day-detox-tea-639290.jpg?v=1688536584', $productImage1stSet->getSrc());
        $this->assertEquals('', $productImage1stSet->getVariantIds());
        $this->assertEquals('gid://shopify/ProductImage/IMG1', $productImage1stSet->getAdminGraphqlApiId());

        $this->assertEquals('IMG2', $productImage2ndSet->getSpfImageId());
        $this->assertEquals('SPF1', $productImage2ndSet->getSpfProductId());
        $this->assertEquals(2, $productImage2ndSet->getPosition());
        $this->assertDates('2023-07-05 05:56:24', $productImage2ndSet->getCreatedAt());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getUpdatedAt());
        $this->assertEquals('14 Day Detox Tea - Baebody', $productImage2ndSet->getAlt());
        $this->assertEquals(3000, $productImage2ndSet->getWidth());
        $this->assertEquals(3000, $productImage2ndSet->getHeight());
        $this->assertEquals('https://cdn.shopify.com/s/files/1/0278/6839/7662/products/14-day-detox-tea-639290.jpg?v=1688536584', $productImage2ndSet->getSrc());
        $this->assertEquals('', $productImage2ndSet->getVariantIds());
        $this->assertEquals('gid://shopify/ProductImage/IMG2', $productImage2ndSet->getAdminGraphqlApiId());
    }

    /**
     * @param string $expectedDate
     * @param \DateTimeInterface $actualDate
     */
    public function assertDates(string $expectedDate, \DateTimeInterface $actualDate): void
    {
        $formattedActualDate = $actualDate->format('Y-m-d H:i:s');
        $this->assertEquals($expectedDate, $formattedActualDate);
    }

    /**
     * @given a shopify product data
     * @when the data is parsed
     * @then the product, product variant, product option, and product image records are created
     * @and if given a shopify product data that has existing records, then the records are updated
     */
    public function testParseShopifyProductUpdateExistingRecord(): void
    {
        $apiRequestOne = Fixtures::createShopifyApiRequestProductCreateNewRecord($this->em);
        $responseDataOne = $this->manager->loadResponseFromFile($apiRequestOne);

        foreach ($responseDataOne as $productDataOne) {
            $this->manager->parseShopifyProduct($productDataOne, $apiRequestOne);
        }

        $product = $this->retrieveOneEntityByCriteria(Product::class, ['spfProductId' => 'SPF1']);
        $productVariant1stSet = $this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR1']);
        $productOption1stSet = $this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT1']);
        $productImage1stSet = $this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG1']);
        $productVariant2ndSet = $this->retrieveOneEntityByCriteria(ProductVariant::class, ['spfVariantId' => 'VAR2']);
        $productOption2ndSet = $this->retrieveOneEntityByCriteria(ProductOption::class, ['spfOptionsId' => 'OPT2']);
        $productImage2ndSet = $this->retrieveOneEntityByCriteria(ProductImage::class, ['spfImageId' => 'IMG2']);

        $this->assertParseShopifyProductCreateNewRecord($product, $productVariant1stSet, $productOption1stSet, $productImage1stSet, $productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);

        $apiRequestTwo = Fixtures::createShopifyApiRequestProductUpdateExistingRecord($this->em);
        $responseDataTwo = $this->manager->loadResponseFromFile($apiRequestTwo);

        foreach ($responseDataTwo as $productDataTwo) {
                $this->manager->parseShopifyProduct($productDataTwo, $apiRequestTwo);
        }

        $this->assertParseShopifyProductUpdateExistingRecord($product, $productVariant1stSet, $productOption1stSet, $productImage1stSet, $productVariant2ndSet, $productOption2ndSet, $productImage2ndSet);
    }

    protected function assertParseShopifyProductUpdateExistingRecord(
        Product $product,
        ProductVariant $productVariant1stSet,
        ProductOption $productOption1stSet,
        ProductImage $productImage1stSet,
        ProductVariant $productVariant2ndSet,
        ProductOption $productOption2ndSet,
        ProductImage $productImage2ndSet
    ): void
    {
        $this->assertEquals('SPF1', $product->getSpfProductId());
        $this->assertEquals('Product 1 UPDATED', $product->getTitle(), 'Product title should be updated');
        $this->assertEquals('<p class="p1"> Product 1  UPDATED</p>', $product->getBodyHtml(), 'Product bodyHtml should be updated');
        $this->assertEquals('Baebody Beauty UPDATED', $product->getVendor(), 'Product vendor should be updated');
        $this->assertEquals('tea', $product->getProductType());
        $this->assertDates('2020-06-18 21:27:43', $product->getCreatedAt());
        $this->assertEquals('14-day-detox-tea-bags', $product->getHandle());
        $this->assertDates('2023-08-31 06:25:38', $product->getUpdatedAt());
        $this->assertDates('2022-11-28 10:47:49', $product->getPublishedAt());
        $this->assertEquals('product-3ingredients UPDATED', $product->getTemplateSuffix(), 'Product templateSuffix should be updated');
        $this->assertEquals('active', $product->getStatus());
        $this->assertEquals('web', $product->getPublishedScope());
        $this->assertEquals('_badge_Best_Seller, Blends, live, Tea UPDATED', $product->getTags(), 'Product tags should be updated');
        $this->assertEquals('gid://shopify/Product/SPF1/UPDATED', $product->getAdminGraphqlApiId());

        $this->assertEquals('VAR1', $productVariant1stSet->getSpfVariantId());
        $this->assertEquals('SPF1', $productVariant1stSet->getSpfProductId());
        $this->assertEquals('Variant 1 UPDATED', $productVariant1stSet->getTitle(), 'ProductVariant title should be updated');
        $this->assertEquals('50.00', $productVariant1stSet->getPrice(), 'ProductVariant price should be updated');
        $this->assertEquals('B801C', $productVariant1stSet->getSku());
        $this->assertEquals(1, $productVariant1stSet->getPosition());
        $this->assertEquals('deny', $productVariant1stSet->getInventoryPolicy());
        $this->assertEquals('50.00', $productVariant1stSet->getCompareAtPrice(), 'ProductVariant compareAtPrice should be updated');
        $this->assertEquals('manual', $productVariant1stSet->getFulfillmentService());
        $this->assertEquals('shopify', $productVariant1stSet->getInventoryManagement());
        $this->assertEquals('Option 1 UPDATED', $productVariant1stSet->getOption1(), 'ProductVariant option1 should be updated');
        $this->assertEquals('Option 2 UPDATED', $productVariant1stSet->getOption2(), 'ProductVariant option2 should be updated');
        $this->assertNull($productVariant1stSet->getOption3());
        $this->assertDates('2023-02-01 15:13:29', $productVariant1stSet->getCreatedAt());
        $this->assertDates('2023-08-31 06:05:32', $productVariant1stSet->getUpdatedAt());
        $this->assertTrue($productVariant1stSet->isTaxable());
        $this->assertEquals('858466006100', $productVariant1stSet->getBarcode());
        $this->assertEquals(20, $productVariant1stSet->getGrams(), 'ProductVariant grams should be updated');
        $this->assertEquals('IMG1', $productVariant1stSet->getSpfImageId());
        $this->assertEquals(10, $productVariant1stSet->getWeight(), 'ProductVariant weight should be updated');
        $this->assertEquals('lb', $productVariant1stSet->getWeightUnit());
        $this->assertEquals(12345678901231, $productVariant1stSet->getSpfInventoryItemId());
        $this->assertEquals(66, $productVariant1stSet->getInventoryQuantity(), 'ProductVariant inventoryQuantity should be updated');
        $this->assertEquals(66, $productVariant1stSet->getOldInventoryQuantity(), 'ProductVariant oldInventoryQuantity should be updated');
        $this->assertTrue($productVariant1stSet->isRequiresShipping());
        $this->assertEquals('gid://shopify/ProductVariant/VAR1', $productVariant1stSet->getAdminGraphqlApiId());

        $this->assertEquals('VAR2', $productVariant2ndSet->getSpfVariantId());
        $this->assertEquals('SPF1', $productVariant2ndSet->getSpfProductId());
        $this->assertEquals('Variant 2 UPDATED', $productVariant2ndSet->getTitle(), 'ProductVariant title should be updated');
        $this->assertEquals('100.00', $productVariant2ndSet->getPrice(), 'ProductVariant price should be updated');
        $this->assertEquals('B801D', $productVariant2ndSet->getSku());
        $this->assertEquals(2, $productVariant2ndSet->getPosition());
        $this->assertEquals('approve', $productVariant2ndSet->getInventoryPolicy());
        $this->assertEquals('100.00', $productVariant2ndSet->getCompareAtPrice(), 'ProductVariant compareAtPrice should be updated');
        $this->assertEquals('auto', $productVariant2ndSet->getFulfillmentService());
        $this->assertEquals('shopify', $productVariant2ndSet->getInventoryManagement());
        $this->assertEquals('Option 1 UPDATED', $productVariant2ndSet->getOption1(), 'ProductVariant option1 should be updated');
        $this->assertEquals('Option 2 UPDATED', $productVariant2ndSet->getOption2(), 'ProductVariant option2 should be updated');
        $this->assertNull($productVariant2ndSet->getOption3());
        $this->assertDates('2023-02-01 15:13:29', $productVariant2ndSet->getCreatedAt());
        $this->assertDates('2023-08-31 06:05:32', $productVariant2ndSet->getUpdatedAt());
        $this->assertTrue($productVariant2ndSet->isTaxable());
        $this->assertEquals('858466006101', $productVariant2ndSet->getBarcode());
        $this->assertEquals(11, $productVariant2ndSet->getGrams());
        $this->assertEquals('IMG2', $productVariant2ndSet->getSpfImageId());
        $this->assertEquals(6, $productVariant2ndSet->getWeight());
        $this->assertEquals('lb', $productVariant2ndSet->getWeightUnit());
        $this->assertEquals(12345678901232, $productVariant2ndSet->getSpfInventoryItemId());
        $this->assertEquals(100, $productVariant2ndSet->getInventoryQuantity(), 'ProductVariant inventoryQuantity should be updated');
        $this->assertEquals(100, $productVariant2ndSet->getOldInventoryQuantity(), 'ProductVariant oldInventoryQuantity should be updated');
        $this->assertTrue($productVariant2ndSet->isRequiresShipping());
        $this->assertEquals('gid://shopify/ProductVariant/VAR2', $productVariant2ndSet->getAdminGraphqlApiId());

        $this->assertEquals('OPT1', $productOption1stSet->getSpfOptionsId());
        $this->assertEquals('SPF1', $productOption1stSet->getSpfProductId());
        $this->assertEquals('Size UPDATED', $productOption1stSet->getName(), 'ProductOption name should be updated');
        $this->assertEquals(1, $productOption1stSet->getPosition());
        $this->assertEquals('Option 1 UPDATED', $productOption1stSet->getOptionsValues(), 'ProductOption optionsValues should be updated');

        $this->assertEquals('OPT2', $productOption2ndSet->getSpfOptionsId());
        $this->assertEquals('SPF1', $productOption2ndSet->getSpfProductId());
        $this->assertEquals('Size UPDATED', $productOption2ndSet->getName(), 'ProductOption name should be updated');
        $this->assertEquals(2, $productOption2ndSet->getPosition());
        $this->assertEquals('Option 2 UPDATED', $productOption2ndSet->getOptionsValues(), 'ProductOption optionsValues should be updated');

        $this->assertEquals('IMG1', $productImage1stSet->getSpfImageId());
        $this->assertEquals('SPF1', $productImage1stSet->getSpfProductId());
        $this->assertEquals(1, $productImage1stSet->getPosition());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getCreatedAt());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getUpdatedAt());
        $this->assertEquals('14 Day Detox Tea - Baebody UPDATED', $productImage1stSet->getAlt(), 'ProductImage alt should be updated');
        $this->assertEquals(1500, $productImage1stSet->getWidth());
        $this->assertEquals(1500, $productImage1stSet->getHeight());
        $this->assertEquals('https://cdn.shopify.com/s/files/1/0278/6839/7662/products/14-day-detox-tea-639290.jpg?v=1688536584', $productImage1stSet->getSrc());
        $this->assertEquals('', $productImage1stSet->getVariantIds());
        $this->assertEquals('gid://shopify/ProductImage/IMG1/UPDATED', $productImage1stSet->getAdminGraphqlApiId(), 'ProductImage adminGraphqlApiId should be updated');

        $this->assertEquals('IMG2', $productImage2ndSet->getSpfImageId());
        $this->assertEquals('SPF1', $productImage2ndSet->getSpfProductId());
        $this->assertEquals(2, $productImage2ndSet->getPosition());
        $this->assertDates('2023-07-05 05:56:24', $productImage2ndSet->getCreatedAt());
        $this->assertDates('2023-07-05 05:56:24', $productImage1stSet->getUpdatedAt());
        $this->assertEquals('14 Day Detox Tea - Baebody UPDATED', $productImage2ndSet->getAlt(), 'ProductImage alt should be updated');
        $this->assertEquals(3000, $productImage2ndSet->getWidth());
        $this->assertEquals(3000, $productImage2ndSet->getHeight());
        $this->assertEquals('https://cdn.shopify.com/s/files/1/0278/6839/7662/products/14-day-detox-tea-639290.jpg?v=1688536584', $productImage2ndSet->getSrc());
        $this->assertEquals('', $productImage2ndSet->getVariantIds());
        $this->assertEquals('gid://shopify/ProductImage/IMG2/UPDATED', $productImage2ndSet->getAdminGraphqlApiId(), 'ProductImage adminGraphqlApiId should be updated');
    }

    /**
     * @given a shopify product data
     * @when the data is parsed
     * @then the product, product variant, product option, and product image records are created
     * @and if given a shopify product data that has existing records with no updates, then the records are not updated
     * @and no additional records are created
     */
    public function testParseShopifyProductWillNotCreateDuplicateAfterRecall(): void
    {
        $this->createTestDataForSuccess();
        $initialCallProductsCount = $this->getRepository(Product::class)->count([]);
        $initialCallVariantsCount = $this->getRepository(ProductVariant::class)->count([]);
        $initialCallImagesCount = $this->getRepository(ProductImage::class)->count([]);
        $initialCallOptionsCount = $this->getRepository(ProductOption::class)->count([]);

        $this->createTestDataForSuccess();

        $afterRecallProductsCount = $this->getRepository(Product::class)->count([]);
        $this->assertEquals($initialCallProductsCount, $afterRecallProductsCount, 'No additional products should be created.');

        $afterRecallVariantsCount = $this->getRepository(ProductVariant::class)->count([]);
        $this->assertEquals($initialCallVariantsCount, $afterRecallVariantsCount, 'No additional product variants should be created.');

        $afterRecallOptionsCount = $this->getRepository(ProductOption::class)->count([]);
        $this->assertEquals($initialCallOptionsCount, $afterRecallOptionsCount, 'No additional product options should be created.');

        $afterRecallImagesCount = $this->getRepository(ProductImage::class)->count([]);
        $this->assertEquals($initialCallImagesCount, $afterRecallImagesCount, 'No additional product images should be created.');
    }

    /**
     * @given an empty shopify product data
     * @when the data is parsed
     * @then no records are created
     * @and the parser logs an error
     */
    public function testParseShopifyProductFail(): void
    {
        $testLogger = new TestHandler();
        $this->manager->setLogger(new Logger('test_logger', [$testLogger]));

        $this->createTestDataForFail();

        $this->assertTrue($testLogger->hasErrorThatMatches('/Product ID is missing in Shopify data: .*/'));
    }

    private function countDataRecordsForShopifyProduct(): int
    {
        return $this->retrieveCountOfEntity(Product::class);
    }

    private function countDataRecordsForShopifyProductVariant(): int
    {
        return count($this->retrieveAllOfEntity(ProductVariant::class, ['deletedAt' => null]));
    }

    private function countDataRecordsForShopifyProductOption(): int
    {
        return count($this->retrieveAllOfEntity(ProductOption::class, ['deletedAt' => null]));
    }

    private function countDataRecordsForShopifyProductImage(): int
    {
        return count($this->retrieveAllOfEntity(ProductImage::class, ['deletedAt' => null]));
    }
}