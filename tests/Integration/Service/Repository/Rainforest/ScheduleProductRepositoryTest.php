<?php

namespace App\Tests\Integration\Service\Repository\Rainforest;

use App\DataFixtures\Repository\Rainforest\ScheduleProductFixtures as Fixtures;
use App\Entity\Rainforest\ScheduleProduct;
use App\Repository\Rainforest\ScheduleProductRepository;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\CanUseDoctrineTrait;
use Codeception\Attribute\DataProvider;
use Codeception\Test\Unit;

class ScheduleProductRepositoryTest extends Unit
{
    use CanUseDoctrineTrait;
    use CanInvokeProtectedMethodTrait;

    protected function _before(): void
    {
        $this->initDoctrine();
        $this->beginTransaction();
    }

    protected function _after(): void
    {
        $this->rollBackTransaction();
    }

    public function testRetrieveStaleSchedulesBasic(): void
    {
        // Call fixture to add data for api_rainforest_rf
        Fixtures::loadScheduleProduct($this->em);

        // Retrieve stale schedules
        /** @var ScheduleProductRepository $repository */
        $repository = $this->getRepository(ScheduleProduct::class);

        $staleSchedules = $repository->retrieveStaleSchedules();
        $this->assertCount(2, $staleSchedules, 'should be 2 schedules in the fixtures');

        $staleSchedules = $repository->retrieveStaleSchedules(['US']);
        $this->assertCount(1, $staleSchedules, 'countryCode should filter results to a single entry');
        $staleSchedules = reset($staleSchedules);
        $this->assertEquals('US', $staleSchedules->getCountryCode(), 'returned schedule should be in US if countryCode=US');
        $this->assertEquals(Fixtures::ASIN1, $staleSchedules->getAsin(), 'returned schedule should have known ASIN');

        $staleSchedules = $repository->retrieveStaleSchedules(['UK']);
        $this->assertCount(1, $staleSchedules, 'countryCode should filter results to a single entry');
        $staleSchedules = reset($staleSchedules);
        $this->assertEquals('UK', $staleSchedules->getCountryCode(), 'returned schedule should be in UK if countryCode=UK');
        $this->assertEquals(Fixtures::ASIN1, $staleSchedules->getAsin(), 'returned schedule should have known ASIN');
    }

    /**
     * @param null|string|list<string> $countryCodes
     * @param null|string|list<string> $asins
     * @param bool $incFailedRequests
     * @param int $expectedCount
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestRetrieveStaleSchedules')]
    public function testRetrieveStaleSchedules(string|array|null $countryCodes, string|array|null $asins, bool $incFailedRequests, int $expectedCount, string $message=''): void
    {
        // Call fixture to add data for api_rainforest_rf
        Fixtures::loadScheduleProduct($this->em);

        // Retrieve stale schedules
        /** @var ScheduleProductRepository $repository */
        $repository = $this->getRepository(ScheduleProduct::class);

        $staleSchedules = $repository->retrieveStaleSchedules($countryCodes, $asins, $incFailedRequests);
        $this->assertCount( $expectedCount, $staleSchedules, $message);

    }

    /**
     * @return list<array{null|string|list<string>, null|string|list<string>, bool, int, string}>
     */
    protected function dataProviderForTestRetrieveStaleSchedules(): array
    {
        $asinReal = Fixtures::ASIN1;
        $asinFake = 'XXXXXXXXXX';

        return [
            [['US'], null,                  false, 1, 'countryCode should filter results to a single entry'],
            [['UK'], null,                  false, 1, 'countryCode should filter results to a single entry'],
            [['UK'], $asinReal,             false, 1, 'countryCode and ASIN string should filter results to a single entry'],
            [['UK'], [$asinReal],           false, 1, 'countryCode and ASIN array should filter results to a single entry'],
            [['UK'], [$asinReal,$asinFake], false, 1, 'Non-existent ASIN in filter should not stop actual ASIN from matching'],
            [['UK'], [$asinFake],           false, 0, 'Non-existent ASIN filter should return zero matches'],
            [['UK'], [],                    false, 1, 'Blank array ASIN filter should not prevent matches'],
            [['UK'], '',                    false, 1, 'Blank string ASIN filter should not prevent matches'],
            [null,   $asinReal,             false, 2, 'countryCode filter is optional'],
            [['US', 'UK'],null,             false, 2, 'Passing in both countries should retrieve schedules for both countries'],
            ['ZZ',   null,                  false, 0, 'Passing in non-existent country should retrieve zero schedules'],
            [null,   null,                  false, 2, 'Passing in null country should retrieve all schedules'],
            [[],     null,                  false, 2, 'Passing in blank country should retrieve all schedules'],
            [['US'], null,                  true,  1, 'Schedule with no linked requests should be returned for includeFailedRequests=true'],
            [['US'], null,                  false, 1, 'Schedule with no linked requests should be returned for includeFailedRequests=false'],
        ];
    }
}