<?php

namespace App\Tests\Integration\Service\Paa;

use App\DataFixtures\Paa\PaaGetItemsFixtures as Fixtures;
use App\Entity\Paa\ApiRequestPaa;
use App\Entity\Paa\DataPaaItem;
use App\Repository\PaaRequest\DataPaaItemRepository;
use App\Service\Paa\AmazonProductAdvertisingApiParser;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\CanUseDoctrineTrait;
use Codeception\Test\Unit;
use Doctrine\ORM\Exception\NotSupported;

class PaaParseGetItemsTest extends Unit
{
    use CanUseDoctrineTrait;
    use CanInvokeProtectedMethodTrait;

    protected AmazonProductAdvertisingApiParser $parser;
    protected DataPaaItemRepository $repo;

    protected function _before(): void
    {
        $this->initDoctrine();
        $this->beginTransaction();

        /** @var AmazonProductAdvertisingApiParser $parser */
        $parser = $this->grabService(AmazonProductAdvertisingApiParser::class);
        $this->parser = $parser;

        try {
            /** @var DataPaaItemRepository $repo */
            $repo = $this->em->getRepository(DataPaaItem::class);
            $this->repo = $repo;
        } catch (NotSupported $e) {
            throw new \RuntimeException('Could not retrieve repositories', 0, $e);
        }
    }

    protected function _after(): void
    {
        $this->rollBackTransaction();
    }

    public function testParseGetItems(): void {
        $this->loadFixtures([Fixtures::class]);

        $this->assertCount(0, $this->repo->findAll(), 'Should have no item records before we start');

        $requestIds = Fixtures::getItemsRequestIds();
        $expectedItemAsins = Fixtures::getExpectedItemAsins();

        $this->parser->parseEntitiesOrIds($requestIds, true);

        $expectedItemsCount = count($requestIds);
        $itemsData = $this->repo->findAll();
        $this->assertCount($expectedItemsCount, $itemsData, 'Should create one new item from every request');
        foreach ($itemsData as $item) {
            $requestId = $item->getCreatedFromRequest()->getId();
            $this->assertEquals($expectedItemAsins[$requestId], $item->getAsin(), 'Item should have the expected asin');
        }
    }

    public function testParseAndUpdateGetItems(): void {
        $this->loadFixtures([Fixtures::class]);
        $expectedItemsCount = 0;

        $this->assertCount($expectedItemsCount, $this->repo->findAll(), 'Should have no item records before we start');

        foreach (Fixtures::getItemsRequests() as $i => $request) {
            $itemRequest = $this->retrieveEntityById(ApiRequestPaa::class, $request['createdFromRequest']);
            ++$expectedItemsCount;
            $itemData = $this->assertParserCreatesNewItemForRequest($itemRequest, $request['initialTitle'], $expectedItemsCount);
            $this->assertEquals($request['initialTitle'], $itemData->getTitle(), 'Should have the expected initial title');
            $this->assertEquals($request['createdFromRequest'], $itemData->getCreatedFromRequest()->getId(), 'Should have the expected createdFromRequest id');
        }

        foreach (Fixtures::getItemsRequests() as $i => $request) {
            $itemRequest = $this->retrieveEntityById(ApiRequestPaa::class, $request['updatedFromRequest']);
            $itemData = $this->assertParserUpdatesItemForRequest($itemRequest, $request['updatedTitle'], $expectedItemsCount);
            $this->assertEquals($request['updatedTitle'], $itemData->getTitle(), 'Should have the expected updated title');
            $this->assertEquals($request['updatedFromRequest'], $itemData->getUpdatedFromRequest()->getId(), 'Should have the expected updatedFromRequest id');
        }
    }

    protected function assertParserCreatesNewItemForRequest(ApiRequestPaa $request, string $expectedTitle, int $expectedItemsCount): DataPaaItem {
        $parserOutput = $this->parser->parseEntitiesOrIds($request, true);
        $this->assertParseOutput($parserOutput, [1, 1, 0, 0], 'Should parse one entity (also updates one, but the count is not returned)');
        $this->assertCount($expectedItemsCount, $this->repo->findAll(), 'Should have one more record after parsing');

        $itemData = $this->repo->findOneBy(['title' => $expectedTitle]);
        $this->assertNotNull($itemData, 'Should have an item record for the request');
        $this->assertEquals($request->getId(), $itemData->getCreatedFromRequest()->getId(), 'Item record should have the request id as the request');
        $this->assertNull($itemData->getUpdatedFromRequest(), 'Item record should have null updated from request id');
        return $itemData;
    }

    protected function assertParserUpdatesItemForRequest(ApiRequestPaa $request, string $expectedTitle, int $expectedItemsCount): DataPaaItem
    {
        $parserOutput = $this->parser->parseEntitiesOrIds($request, true);
        $this->assertParseOutput($parserOutput, [1, 0, 0, 0], 'Should parse one entity (also updates one, but the count is not returned)');
        $this->assertCount($expectedItemsCount, $this->repo->findAll(), 'Should have same number of records after parsing');

        $itemData = $this->repo->findOneBy(['title' => $expectedTitle]);
        $this->assertNotNull($itemData, 'Should have an item record for the request');
        $this->assertNotNull($itemData->getUpdatedFromRequest()->getId());
        $this->assertEquals($request->getId(), $itemData->getUpdatedFromRequest()->getId(), 'Item record should record the id of the updating record');
        return $itemData;
    }

    /**
     * @param array{countEntitiesParsed: int, countRecordsCreated: int, countRecordsUpdated: int, countRecordsDiscarded: int} $parseOutput
     * @param array{int, int, int, int} $expectedValues
     * @param string $message
     * @return void
     */
    protected function assertParseOutput(array $parseOutput, array $expectedValues, string $message): void
    {
        $parseOutputKeys = array_keys($parseOutput);
        $parseOutputValues = array_values($parseOutput);
        foreach ($parseOutputValues as $i => $value) {
            $this->assertEquals($expectedValues[$i], $value, $message . ' - ' . $parseOutputKeys[$i]);
        }
    }
}