<?php

namespace App\Tests\Integration\Service\Crawler;

use App\DataFixtures\Service\Crawler\AmazonCreatorConnectionsMessageFixtures as Fixtures;
use App\Entity\AccountProfileSpa;
use App\Entity\Crawler\AscCreatorConnectionsContact;
use App\Entity\Crawler\AscCreatorConnectionsMessage;
use App\Repository\AccountProfileSpaRepository;
use App\Repository\Crawler\AscCreatorConnectionsContactRepository;
use App\Service\Crawler\AmazonSellerCentralParser;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\CanUseDoctrineTrait;
use Codeception\Test\Unit;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Exception\NotSupported;

class AmazonParseCreatorConnectionsMessageTest extends Unit
{
    use CanUseDoctrineTrait;
    use CanInvokeProtectedMethodTrait;

    protected AccountProfileSpa $profile;
    protected AmazonSellerCentralParser $parser;
    protected AscCreatorConnectionsContactRepository $contactRepository;

    /**
     * @throws NotSupported
     */
    protected function _before(): void
    {
        $this->initDoctrine();
        $this->beginTransaction();

        /** @var AmazonSellerCentralParser $parser */
        $parser = $this->grabService(AmazonSellerCentralParser::class);
        $this->parser = $parser;

        /** @var AscCreatorConnectionsContactRepository $contactRepository */
        $contactRepository = $this->em->getRepository(AscCreatorConnectionsContact::class);
        $this->contactRepository = $contactRepository;

        /** @var AccountProfileSpaRepository $profileRep */
        $profileRep = $this->getRepository(AccountProfileSpa::class);
        $this->profile = $profileRep->find(2);
    }

    protected function _after(): void
    {
        $this->rollBackTransaction();
    }

    public function testParseMessages(): void
    {
        $this->loadFixtures([Fixtures::class]);
        $entityCounts = [
            AscCreatorConnectionsContact::class => $this->retrieveCountOfEntity(AscCreatorConnectionsContact::class),
            AscCreatorConnectionsMessage::class => $this->retrieveCountOfEntity(AscCreatorConnectionsMessage::class),
        ];

        $requestId = Fixtures::getRequestIdInitial();
        $this->parser->parseEntitiesOrIds($requestId);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            AscCreatorConnectionsContact::class => 1,
            AscCreatorConnectionsMessage::class => 1,
        ]);

        $expectedData  = Fixtures::getExpectedMessagesData()[$requestId];
        $contactData = $this->contactRepository->retrieveByContextValidatorToken($expectedData['data']['contextValidatorToken'], $this->profile);

        $this->assertNotNull($contactData, 'Contact should not be null');
        $this->assertExpectedMatchesActual($expectedData['data'], $contactData);
    }

    public function testParseAndUpdateMessages(): void
    {
        $this->loadFixtures([Fixtures::class]);
        $entityCounts = [
            AscCreatorConnectionsContact::class => $this->retrieveCountOfEntity(AscCreatorConnectionsContact::class),
            AscCreatorConnectionsMessage::class => $this->retrieveCountOfEntity(AscCreatorConnectionsMessage::class),
        ];

        // Parse initial data
        $initialRequestId = Fixtures::getRequestIdInitial();
        $this->parser->parseEntitiesOrIds($initialRequestId);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            AscCreatorConnectionsContact::class => 1,
            AscCreatorConnectionsMessage::class => 1,
        ]);

        // Parse updated data followed by a brand reply
        $updatedRequestIdWithCreatorReply = Fixtures::getRequestIdUpdateFollowedByBrandReply();
        $this->parser->parseEntitiesOrIds($updatedRequestIdWithCreatorReply, true);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            AscCreatorConnectionsContact::class => 0,
            AscCreatorConnectionsMessage::class => 1,
        ]);

        $expectedData  = Fixtures::getExpectedMessagesData()[$updatedRequestIdWithCreatorReply];
        $contact = $this->contactRepository->retrieveByContextValidatorToken($expectedData['data']['contextValidatorToken'], $this->profile);

        $this->assertNotNull($contact, 'Contact should not be null after update');
        $this->assertExpectedMatchesActual($expectedData['data'], $contact);

        // Parse data where everything is the same, but the Creator's token has changed
        $newTokenRequestId = Fixtures::getRequestIdNewToken();
        $this->parser->parseEntitiesOrIds($newTokenRequestId, true);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            AscCreatorConnectionsContact::class => 0,
            AscCreatorConnectionsMessage::class => 0,
        ]);

        $expectedData  = Fixtures::getExpectedMessagesData()[$newTokenRequestId];
        $contact = $this->contactRepository->retrieveByContextValidatorToken($expectedData['data']['contextValidatorToken'], $this->profile);

        $this->assertNotNull($contact, 'Contact should not be null after update');
        $this->assertExpectedMatchesActual($expectedData['data'], $contact);


        // Parse updated data followed by a creator reply after the brand reply
        $updatedRequestIdWithCreatorReply = Fixtures::getRequestIdUpdateFollowedByCreatorReply();
        $this->parser->parseEntitiesOrIds($updatedRequestIdWithCreatorReply, true);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            AscCreatorConnectionsContact::class => 0,
            AscCreatorConnectionsMessage::class => 2,
        ]);

        $expectedData  = Fixtures::getExpectedMessagesData()[$updatedRequestIdWithCreatorReply];
        $contact = $this->contactRepository->retrieveByContextValidatorToken($expectedData['data']['contextValidatorToken'], $this->profile);

        $this->assertNotNull($contact, 'Contact should not be null after update');
        $this->assertExpectedMatchesActual($expectedData['data'], $contact);
    }

    /**
     * @param array<string, mixed> $expectedData
     * @param AscCreatorConnectionsContact|AscCreatorConnectionsMessage $actualData
     */
    protected function assertExpectedMatchesActual(array $expectedData, AscCreatorConnectionsContact|AscCreatorConnectionsMessage $actualData): void
    {
        foreach ($expectedData as $key => $expectedValue) {
            if ($key === 'messages') {
                $this->assertMessagesMatch($expectedValue, $actualData->getMessages());
                continue;
            }
            $getter = 'get' . ucfirst($key);
            if ($actualData->$getter() instanceof \DateTimeInterface) {
                $this->assertEquals($expectedValue, $actualData->$getter()->format('Y-m-d H:i:s'), 'Failed asserting that ' . $key . ' matches expected value');
            } elseif ('createdFromRequest' === $key || 'updatedFromRequest' === $key) {
                if (is_null($expectedValue)) {
                    $this->assertNull($actualData->$getter(), 'Failed asserting that ' . $key . ' is null');
                } else {
                    $this->assertNotNull($actualData->$getter(), 'Failed asserting that ' . $key . ' is not null');
                    $this->assertEquals($expectedValue, $actualData->$getter()->getId(), 'Failed asserting that ' . $key . ' is ' . $expectedValue);
                }
            } else {
                $this->assertEquals($expectedValue, $actualData->$getter(), 'Failed asserting that ' . $key . ' matches expected value');
            }
        }
    }

    /**
     * @param array<string, mixed> $expectedMessages
     * @param Collection<int, AscCreatorConnectionsMessage> $actualMessages
     */
    protected function assertMessagesMatch(array $expectedMessages, Collection $actualMessages): void
    {
        $this->assertCount(count($expectedMessages), $actualMessages, 'Failed asserting that the number of messages matches expected value');
        foreach ($actualMessages as $message) {
            $expectedMessage = $expectedMessages[$message->getAzMessageId()];
            $this->assertNotNull($expectedMessage, 'Failed asserting that the message exists in the expected data');
            $this->assertExpectedMatchesActual($expectedMessage, $message);
        }
    }
}
