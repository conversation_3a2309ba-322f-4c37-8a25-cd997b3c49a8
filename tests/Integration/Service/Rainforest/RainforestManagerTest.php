<?php

namespace App\Tests\Integration\Service\Rainforest;

use App\DataFixtures\Repository\Rainforest\ProductFixtures as Fixtures;
use App\Entity\Rainforest\Product;
use App\Entity\Rainforest\ScheduleProduct;
use App\Service\Rainforest\RainforestManager;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\CanUseDoctrineTrait;
use Codeception\Attribute\DataProvider;
use Codeception\Test\Unit;
use PHPUnit\Framework\ExpectationFailedException;

class RainforestManagerTest extends Unit
{
    use CanUseDoctrineTrait;
    use CanInvokeProtectedMethodTrait;

    protected RainforestManager $rainforestManager;

    protected function _before(): void
    {
        $this->initDoctrine();
        $this->beginTransaction();

        /** @var RainforestManager $rainforestManager */
        $rainforestManager = $this->grabService(RainforestManager::class);
        $this->rainforestManager = $rainforestManager;
    }

    protected function _after(): void
    {
        $this->rollBackTransaction();
    }

    /**
     * @given a Product linked to a ScheduleProduct
     * @and the ScheduleProduct has alertChanges set to true
     * @and the ApiRequest is fresh
     * @when we call processProductMonitorAlert on a Product that could create an alert
     * @then triggerProductMonitorAlert should be called
     */
    public function testProcessProductMonitorAlert_BasicShouldSendAlert(): void
    {
        $data = Fixtures::loadProductAndApiRequestLinkedToScheduleProduct($this->em);
        $this->flushChangesAlterProductAndCallProcessProductMonitorAlert($data[Fixtures::PRODUCT], true, $data['scheduleProduct']);
    }

    /**
     * @given a Product linked to a ScheduleProduct
     * @and the ScheduleProduct has alertChanges set to false
     * @and the ApiRequest is fresh
     * @when we call processProductMonitorAlert on a Product that could create an alert
     * @then triggerProductMonitorAlert should NOT be called
     */
    public function testProcessProductMonitorAlert_NoAlertSettingShouldNotSendAlert(): void
    {
        $data = Fixtures::loadProductAndApiRequestLinkedToScheduleProduct($this->em);
        $data['scheduleProduct']->setAlertChanges(false);
        $this->flushChangesAlterProductAndCallProcessProductMonitorAlert($data[Fixtures::PRODUCT], false);
    }

    /**
     * @given a Product linked to a ScheduleProduct
     * @and the ScheduleProduct has alertChanges set to true
     * @and the ApiRequest is old
     * @when we call processProductMonitorAlert on a Product that could create an alert
     * @then triggerProductMonitorAlert should NOT be called
     */
    public function testProcessProductMonitorAlert_OldRequestShouldNotSendAlert(): void
    {
        $data = Fixtures::loadProductAndApiRequestLinkedToScheduleProduct($this->em);
        $data['apiRequest']->setRequestDate(new \DateTime('-8 hours'));
        $this->flushChangesAlterProductAndCallProcessProductMonitorAlert($data[Fixtures::PRODUCT], false);
    }

    /**
     * @given a Product NOT linked to a ScheduleProduct
     * @and the ApiRequest is fresh
     * @when we call processProductMonitorAlert on a Product that could create an alert
     * @then triggerProductMonitorAlert should NOT be called
     */
    public function testProcessProductMonitorAlert_NoScheduleShouldNotSendAlert(): void
    {
        $data = Fixtures::loadProductAndApiRequest($this->em);
        $this->flushChangesAlterProductAndCallProcessProductMonitorAlert($data[Fixtures::PRODUCT], false);
    }

    /**
     * @param array<string, mixed> $newValueMap
     * @param list<string> $expectedAlertArray
     * @param string $message
     */
    #[DataProvider('dataProviderForTestProcessProductMonitorAlert')]
    public function testProcessProductMonitorAlert(array $newValueMap, array $expectedAlertArray, string $message): void
    {
        $data = Fixtures::loadProductAndApiRequestLinkedToScheduleProduct($this->em);
        $this->flushChangesMakeSpecifiedChangesToProductAndCallProcessProductMonitorAlert($data[Fixtures::PRODUCT], $newValueMap, $expectedAlertArray, $data['scheduleProduct'], $message);
    }

    /**
     * @return list<array{array<string, mixed>, list<string>}>
     * @noinspection PhpUnusedPrivateMethodInspection - used by test
     */
    private function dataProviderForTestProcessProductMonitorAlert(): array
    {
        return [
            // no real change -> no alert
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 1.00],        [], 'No alert if no real change in salesRank'],
            [['salesRankTlc'=>Fixtures::PRODUCT_SALES_RANK_TLC],        [], 'No alert if no real change in salesRankTlc'],
            [['numSiblings'=>Fixtures::PRODUCT_NUM_SIBLINGS * 1.0],     [], 'No alert if no real change in numSiblings'],
            [['asin'=>Fixtures::PRODUCT_ASIN],                          [], 'No alert if no real change in asin'],
            [['ratingsTotal'=>Fixtures::PRODUCT_RATINGS_TOTAL * 1.0],   [], 'No alert if no real change in ratingsTotal'],
            [['reviewsTotal'=>Fixtures::PRODUCT_REVIEWS_TOTAL * 1.0],   [], 'No alert if no real change in reviewsTotal'],
            [['rating50'=>Fixtures::PRODUCT_RATING50 * 1.0],            [], 'No alert if no real change in rating50'],
            // only changes above 30% trigger alert
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 0.69],        [RainforestManager::MESSAGE_SALES_RANK_TLC_THIRTY_PERCENT], 'Should alert when sales fall by more than 30%'],
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 0.70],        [RainforestManager::MESSAGE_SALES_RANK_TLC_THIRTY_PERCENT], 'Should alert when sales fall by exactly 30%'],
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 0.71],        [], 'No alert when sales fall by less than 30%'],
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 1.29],        [], 'No alert when sales rise by less than 30%'],
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 1.30],        [RainforestManager::MESSAGE_SALES_RANK_TLC_THIRTY_PERCENT], 'Should alert when sales rise by exactly 30%'],
            [['salesRank'=>Fixtures::PRODUCT_SALES_RANK * 1.31],        [RainforestManager::MESSAGE_SALES_RANK_TLC_THIRTY_PERCENT], 'Should alert when sales rise by more than 30%'],
            // any change triggers alert
            [['salesRankTlc'=>'something new'],                         [RainforestManager::MESSAGE_SALES_RANK_TLC], 'Should alert if salesRankTlc changes'],
            [['numSiblings'=>Fixtures::PRODUCT_NUM_SIBLINGS - 1],       [RainforestManager::MESSAGE_NUM_SIBLINGS], 'Should alert if numSiblings goes down'],
            [['numSiblings'=>Fixtures::PRODUCT_NUM_SIBLINGS + 1],       [RainforestManager::MESSAGE_NUM_SIBLINGS], 'Should alert if numSiblings goes up'],
            [['variantAsins'=>null],                                    [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if all siblings are removed'],
            [['variantAsins'=>''],                                      [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if all siblings are removed'],
            [['variantAsins'=>Fixtures::PRODUCT_SIBLING2],              [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if first sibling is removed'],
            [['variantAsins'=>Fixtures::PRODUCT_SIBLING1],              [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if second sibling is removed'],
            [['variantAsins'=>Fixtures::PRODUCT_SIBLING1 . ',B000003333'],      [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if second sibling is changed'],
            [['variantAsins'=>Fixtures::PRODUCT_VARIANT_ASINS . ',B000003333'], [RainforestManager::MESSAGE_CHANGED_SIBLINGS], 'Should alert if new sibling is added'],
            [['asin'=>'a_new_asin'],                                    [RainforestManager::MESSAGE_ASIN], 'Should alert if asin changes'],
            // increases do no trigger alert, but falls do
            [['ratingsTotal'=>Fixtures::PRODUCT_RATINGS_TOTAL + 1],     [], 'Should not alert if ratings go up'],
            [['reviewsTotal'=>Fixtures::PRODUCT_REVIEWS_TOTAL + 1],     [], 'Should not alert if reviews go up'],
            [['rating50'=>Fixtures::PRODUCT_RATING50 + 5],              [], 'Should not alert if rating50 goes up'],
            [['ratingsTotal'=>Fixtures::PRODUCT_RATINGS_TOTAL - 1],     [RainforestManager::MESSAGE_RATINGS_TOTAL], 'Should alert if ratings go down'],
            [['reviewsTotal'=>Fixtures::PRODUCT_REVIEWS_TOTAL - 1],     [RainforestManager::MESSAGE_REVIEWS_TOTAL], 'Should alert if reviews go down'],
            [['rating50'=>Fixtures::PRODUCT_RATING50 - 5],              [RainforestManager::MESSAGE_RATING50], 'Should alert if rating50 goes down'],
        ];
    }

    #[DataProvider('dataProviderForTestProductMonitorWarnMayBeOffline')]
    public function testProductMonitorWarnMayBeOffline(bool $apiRequestFailed, bool $expectsWarnings, string $message): void
    {
        $data = Fixtures::loadProductAndApiRequestLinkedToScheduleProduct($this->em);
        if ($apiRequestFailed) {
            $data['apiRequest']->setStatusToFatal();
        }
        $this->flushChanges();

        if ($expectsWarnings) {
            $expectedMessages = [$this->invokeProtectedMethod($this->rainforestManager, 'buildMaybeOfflineMessage', [$data['apiRequest']])];
        } else {
            $expectedMessages = [];
        }

        $rainforestManagerPartialMock = $this->createRainforestManagerPartialMock($expectedMessages, $data['scheduleProduct']);
        try {
            $rainforestManagerPartialMock->productMonitorWarnMayBeOffline($data['apiRequest']);
        } catch (ExpectationFailedException $e) {
            if (empty($message)) {
                throw $e;
            }
            $this->fail($message);
        }
    }

    /**
     * @return list<array{bool, bool, string}>
     */
    protected function dataProviderForTestProductMonitorWarnMayBeOffline(): array
    {
        return [
            [true, true, 'Should warn if ApiRequest failed'],
            [false, false, 'Should not warn if ApiRequest succeeded'],
        ];
    }

    /**
     * @param list<string> $expectedMessages
     * @param ?ScheduleProduct $schedule
     * @return RainforestManager
     */
    private function createRainforestManagerPartialMock(array $expectedMessages = [], ?ScheduleProduct $schedule = null): RainforestManager
    {
        $rainforestManagerPartialMock = $this->getMockBuilder(RainforestManager::class)
            ->onlyMethods(['triggerProductMonitorAlert'])
            ->disableOriginalConstructor()
            ->getMock();
        $this->setProtectedProperty($rainforestManagerPartialMock, 'em', $this->em);

        if (empty($expectedMessages)) {
            $rainforestManagerPartialMock->expects($this->never())
                ->method('triggerProductMonitorAlert');
        } else {
            $rainforestManagerPartialMock->expects($this->once())
                ->method('triggerProductMonitorAlert')
                ->with($schedule, $expectedMessages)
            ;
        }

        return $rainforestManagerPartialMock;
    }

    /**
     * @param Product $product
     * @param array<string, mixed> $newValueMap
     * @param list<string> $expectedAlertArray
     * @param ?ScheduleProduct $schedule
     * @param string $message
     * @return void
     */
    private function flushChangesMakeSpecifiedChangesToProductAndCallProcessProductMonitorAlert(
        Product          $product,
        array            $newValueMap,
        array            $expectedAlertArray = [],
        ?ScheduleProduct $schedule = null,
        string           $message = ''
    ): void
    {
        $this->flushChanges();

        foreach ($newValueMap as $field => $newValue) {
            $setter = 'set' . ucfirst($field);
            $product->$setter($newValue);
        }

        $rainforestManagerPartialMock = $this->createRainforestManagerPartialMock($expectedAlertArray, $schedule);
        try {
            $rainforestManagerPartialMock->processProductMonitorAlert($product);
        } catch (ExpectationFailedException $e) {
            if (empty($message)) {
                throw $e;
            }
            $this->fail($message);
        }
    }

    private function flushChangesAlterProductAndCallProcessProductMonitorAlert(Product $product, bool $expectAlert, ?ScheduleProduct $schedule=null): void
    {
        $this->flushChanges();

        $product->setSalesRankTlc('new_value');
        $expectedAlertArray = $expectAlert ? [RainforestManager::MESSAGE_SALES_RANK_TLC] : [];
        $rainforestManagerPartialMock = $this->createRainforestManagerPartialMock($expectedAlertArray, $schedule);
        $rainforestManagerPartialMock->processProductMonitorAlert($product);
    }
}