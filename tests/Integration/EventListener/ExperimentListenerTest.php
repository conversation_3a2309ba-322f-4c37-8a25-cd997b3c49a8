<?php

namespace App\Tests\Integration\EventListener;

use App\DataFixtures\Service\ExperimentResult\ExperimentManagerFixtures;
use App\Entity\AccountProfileSpa;
use App\Entity\ExperimentResult\Experiment;
use App\Service\ExperimentResult\ExperimentManager;
use App\Service\Messaging\SlackMessenger;
use App\Tests\Integration\DoctrineTransactionUnitTest;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\CanUseDoctrineTrait;
use Codeception\Attribute\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;

class ExperimentListenerTest extends DoctrineTransactionUnitTest
{
    use CanUseDoctrineTrait, CanInvokeProtectedMethodTrait;

    protected ExperimentManager $manager;
    protected SlackMessenger|MockObject $slackMessengerMock;

    /**
     * @var Experiment[]
     */
    protected array $experiments = [];

    protected function _before(): void
    {
        parent::_before();
        $this->setupExperimentManagerWithMock();
    }

    private function setupExperimentManagerWithMock(): void
    {
        /** @var ExperimentManager $manager */
        $manager = $this->grabService(ExperimentManager::class);
        $this->slackMessengerMock = $this->createMock(SlackMessenger::class);
        $this->setProtectedProperty($manager, 'slackMessenger', $this->slackMessengerMock);
        $this->manager = $manager;
    }

    #[DataProvider('experimentStatusChangeProvider')]
    public function testExperimentStatusChange(
        string $initialStatus,
        string $newStatus,
        ?bool $expectedIsNotified,
        string $message
    ): void {
        $this
            ->givenAnAccountWithExperiments()
            ->givenExperimentInitialStatus($initialStatus)
            ->whenExperimentStatusIsUpdatedTo($newStatus, $expectedIsNotified)
            ->thenExperimentShouldHaveNotificationStatus($expectedIsNotified, $message)
        ;
    }

    /**
     * @return array<string, array{initialStatus: string, newStatus: string, expectedIsNotified: ?bool, message: string}>
     */
    protected function experimentStatusChangeProvider(): array
    {
        return [
            'Status change to cancelled should notify' => [
                'initialStatus' => 'STATUS_IN_PROGRESS',
                'newStatus' => 'STATUS_CANCELLED',
                'expectedIsNotified' => true,
                'message' => 'Experiment should be marked as notified after status change to cancelled'
            ],
            'Status change to in progress should not notify' => [
                'initialStatus' => 'STATUS_IN_PROGRESS',
                'newStatus' => 'STATUS_IN_PROGRESS',
                'expectedIsNotified' => null,
                'message' => 'Experiment should not be marked as notified after status change to in progress'
            ],
            'Status change to complete should notify' => [
                'initialStatus' => 'STATUS_IN_PROGRESS',
                'newStatus' => 'STATUS_COMPLETE',
                'expectedIsNotified' => true,
                'message' => 'Experiment should be marked as notified after status change to complete'
            ],
        ];
    }

    private function givenAnAccountWithExperiments(): static
    {
        $account = $this->getRepository(AccountProfileSpa::class)->find(1);
        $this->experiments = ExperimentManagerFixtures::createExperiments($this->em, $account);
        $this->flushChanges();
        return $this;
    }

    private function givenExperimentInitialStatus(string $statusConst): static
    {
        $initialStatus = $this->getProtectedConst($this->experiments[0], $statusConst);
        $this->experiments[0]->setStatus($initialStatus);
        $this->experiments[0]->setIsNotified(null); // Reset notification status
        $this->flushChanges();
        $this->assertNull($this->experiments[0]->isIsNotified(), 'Experiment should initially not be marked as notified');
        return $this;
    }

    private function whenExperimentStatusIsUpdatedTo(string $statusConst, ?bool $expectedIsNotified): static
    {
        if (empty($expectedIsNotified)) {
            $this->slackMessengerMock
                ->expects($this->never())
                ->method('sendMessageToPurposeChannelForBrandCountry')
            ;
        } else {
            $this->slackMessengerMock
                ->expects($this->exactly(1))
                ->method('sendMessageToPurposeChannelForBrandCountry')
                ->willReturn(['someMessageId'])
            ;
        }


        $newStatus = $this->getProtectedConst($this->experiments[0], $statusConst);
        $this->experiments[0]->setStatus($newStatus);
        $this->flushChanges();
        return $this;
    }

    private function thenExperimentShouldHaveNotificationStatus(?bool $expectedIsNotified, string $message): static
    {
        $this->assertSame($expectedIsNotified, $this->experiments[0]->isIsNotified(), $message);
        return $this;
    }
}
