{"Reports": [{"ReportID": "TrialBalance", "ReportName": "Trial Balance", "ReportType": "TrialBalance", "ReportTitles": ["Trial Balance", "Demo Company (Global)", "As at 18 February 2025"], "ReportDate": "18 February 2025", "Rows": [{"RowType": "Header", "Cells": [{"Value": "Account"}, {"Value": "Debit"}, {"Value": "Credit"}, {"Value": "YTD Debit"}, {"Value": "YTD Credit"}]}, {"RowType": "Section", "Title": "Revenue", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Sales (200)", "Attributes": [{"Id": "account", "Value": "d1ebb97b-d207-4ccb-9ab6-8a466a8b4d39"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d1ebb97b-d207-4ccb-9ab6-8a466a8b4d39"}]}, {"Value": "1417.60", "Attributes": [{"Id": "account", "Value": "d1ebb97b-d207-4ccb-9ab6-8a466a8b4d39"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d1ebb97b-d207-4ccb-9ab6-8a466a8b4d39"}]}, {"Value": "9483.54", "Attributes": [{"Id": "account", "Value": "d1ebb97b-d207-4ccb-9ab6-8a466a8b4d39"}]}]}]}, {"RowType": "Section", "Title": "Expenses", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Advertising (400)", "Attributes": [{"Id": "account", "Value": "eb01b8c7-f9b0-49ef-86f4-ed89b4ac33d5"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "eb01b8c7-f9b0-49ef-86f4-ed89b4ac33d5"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "eb01b8c7-f9b0-49ef-86f4-ed89b4ac33d5"}]}, {"Value": "7809.47", "Attributes": [{"Id": "account", "Value": "eb01b8c7-f9b0-49ef-86f4-ed89b4ac33d5"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "eb01b8c7-f9b0-49ef-86f4-ed89b4ac33d5"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Consulting & Accounting (412)", "Attributes": [{"Id": "account", "Value": "d2429b4f-ad8b-45e5-815a-13f5ecb0d5c1"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "d2429b4f-ad8b-45e5-815a-13f5ecb0d5c1"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d2429b4f-ad8b-45e5-815a-13f5ecb0d5c1"}]}, {"Value": "29.00", "Attributes": [{"Id": "account", "Value": "d2429b4f-ad8b-45e5-815a-13f5ecb0d5c1"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d2429b4f-ad8b-45e5-815a-13f5ecb0d5c1"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Entertainment (420)", "Attributes": [{"Id": "account", "Value": "6a7d9c59-1d5a-4474-8c61-3da1f980db13"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "6a7d9c59-1d5a-4474-8c61-3da1f980db13"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "6a7d9c59-1d5a-4474-8c61-3da1f980db13"}]}, {"Value": "1522.00", "Attributes": [{"Id": "account", "Value": "6a7d9c59-1d5a-4474-8c61-3da1f980db13"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "6a7d9c59-1d5a-4474-8c61-3da1f980db13"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Freight & Courier (425)", "Attributes": [{"Id": "account", "Value": "c4b1c463-9913-4672-a8b8-01a3b546126f"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c4b1c463-9913-4672-a8b8-01a3b546126f"}]}, {"Value": "10.00", "Attributes": [{"Id": "account", "Value": "c4b1c463-9913-4672-a8b8-01a3b546126f"}]}, {"Value": "105.50", "Attributes": [{"Id": "account", "Value": "c4b1c463-9913-4672-a8b8-01a3b546126f"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c4b1c463-9913-4672-a8b8-01a3b546126f"}]}]}, {"RowType": "Row", "Cells": [{"Value": "General Expenses (429)", "Attributes": [{"Id": "account", "Value": "4281c446-efb4-445d-b32d-c441a4ef5678"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "4281c446-efb4-445d-b32d-c441a4ef5678"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "4281c446-efb4-445d-b32d-c441a4ef5678"}]}, {"Value": "120.09", "Attributes": [{"Id": "account", "Value": "4281c446-efb4-445d-b32d-c441a4ef5678"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "4281c446-efb4-445d-b32d-c441a4ef5678"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Light, Power, Heating (445)", "Attributes": [{"Id": "account", "Value": "c61d3100-d95f-4ce4-ba75-c03cc767b825"}]}, {"Value": "100.32", "Attributes": [{"Id": "account", "Value": "c61d3100-d95f-4ce4-ba75-c03cc767b825"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c61d3100-d95f-4ce4-ba75-c03cc767b825"}]}, {"Value": "225.82", "Attributes": [{"Id": "account", "Value": "c61d3100-d95f-4ce4-ba75-c03cc767b825"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c61d3100-d95f-4ce4-ba75-c03cc767b825"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Motor Vehicle Expenses (449)", "Attributes": [{"Id": "account", "Value": "470f3b54-9cbe-420a-8d13-22cb17fd9d40"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "470f3b54-9cbe-420a-8d13-22cb17fd9d40"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "470f3b54-9cbe-420a-8d13-22cb17fd9d40"}]}, {"Value": "517.18", "Attributes": [{"Id": "account", "Value": "470f3b54-9cbe-420a-8d13-22cb17fd9d40"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "470f3b54-9cbe-420a-8d13-22cb17fd9d40"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Office Expenses (453)", "Attributes": [{"Id": "account", "Value": "79fc6227-e183-409a-ba39-77028e0ceeff"}]}, {"Value": "107.11", "Attributes": [{"Id": "account", "Value": "79fc6227-e183-409a-ba39-77028e0ceeff"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "79fc6227-e183-409a-ba39-77028e0ceeff"}]}, {"Value": "220.86", "Attributes": [{"Id": "account", "Value": "79fc6227-e183-409a-ba39-77028e0ceeff"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "79fc6227-e183-409a-ba39-77028e0ceeff"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Printing & Stationery (461)", "Attributes": [{"Id": "account", "Value": "a8c9ca73-91ef-4163-a4f6-7d892201c087"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "a8c9ca73-91ef-4163-a4f6-7d892201c087"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "a8c9ca73-91ef-4163-a4f6-7d892201c087"}]}, {"Value": "72.70", "Attributes": [{"Id": "account", "Value": "a8c9ca73-91ef-4163-a4f6-7d892201c087"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "a8c9ca73-91ef-4163-a4f6-7d892201c087"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Purchases (300)", "Attributes": [{"Id": "account", "Value": "ae8074e0-1ac1-4b37-8546-afa9fa6ace9e"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "ae8074e0-1ac1-4b37-8546-afa9fa6ace9e"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "ae8074e0-1ac1-4b37-8546-afa9fa6ace9e"}]}, {"Value": "775.98", "Attributes": [{"Id": "account", "Value": "ae8074e0-1ac1-4b37-8546-afa9fa6ace9e"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "ae8074e0-1ac1-4b37-8546-afa9fa6ace9e"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Rent (469)", "Attributes": [{"Id": "account", "Value": "d76027eb-0256-4b69-9f72-4751312e2749"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "d76027eb-0256-4b69-9f72-4751312e2749"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d76027eb-0256-4b69-9f72-4751312e2749"}]}, {"Value": "2182.44", "Attributes": [{"Id": "account", "Value": "d76027eb-0256-4b69-9f72-4751312e2749"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d76027eb-0256-4b69-9f72-4751312e2749"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Repairs and Maintenance (473)", "Attributes": [{"Id": "account", "Value": "5098e313-2775-40c3-9bb4-6b1f86d89a47"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "5098e313-2775-40c3-9bb4-6b1f86d89a47"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "5098e313-2775-40c3-9bb4-6b1f86d89a47"}]}, {"Value": "64.20", "Attributes": [{"Id": "account", "Value": "5098e313-2775-40c3-9bb4-6b1f86d89a47"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "5098e313-2775-40c3-9bb4-6b1f86d89a47"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Telephone & Internet (489)", "Attributes": [{"Id": "account", "Value": "3b4fee92-7ed1-4d63-b00e-8fdf13927e05"}]}, {"Value": "50.00", "Attributes": [{"Id": "account", "Value": "3b4fee92-7ed1-4d63-b00e-8fdf13927e05"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "3b4fee92-7ed1-4d63-b00e-8fdf13927e05"}]}, {"Value": "93.25", "Attributes": [{"Id": "account", "Value": "3b4fee92-7ed1-4d63-b00e-8fdf13927e05"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "3b4fee92-7ed1-4d63-b00e-8fdf13927e05"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Travel - National (493)", "Attributes": [{"Id": "account", "Value": "f5211403-43fb-49aa-82de-9fc1f6bc3e8c"}]}, {"Value": "223.56", "Attributes": [{"Id": "account", "Value": "f5211403-43fb-49aa-82de-9fc1f6bc3e8c"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "f5211403-43fb-49aa-82de-9fc1f6bc3e8c"}]}, {"Value": "223.56", "Attributes": [{"Id": "account", "Value": "f5211403-43fb-49aa-82de-9fc1f6bc3e8c"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "f5211403-43fb-49aa-82de-9fc1f6bc3e8c"}]}]}]}, {"RowType": "Section", "Title": "Assets", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Accounts Receivable (610)", "Attributes": [{"Id": "account", "Value": "9af45928-6ad1-462d-b4c6-46d8f58255be"}]}, {"Value": "1544.55", "Attributes": [{"Id": "account", "Value": "9af45928-6ad1-462d-b4c6-46d8f58255be"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "9af45928-6ad1-462d-b4c6-46d8f58255be"}]}, {"Value": "9194.51", "Attributes": [{"Id": "account", "Value": "9af45928-6ad1-462d-b4c6-46d8f58255be"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "9af45928-6ad1-462d-b4c6-46d8f58255be"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Business Bank Account (090)", "Attributes": [{"Id": "account", "Value": "562555f2-8cde-4ce9-8203-0363922537a4"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "562555f2-8cde-4ce9-8203-0363922537a4"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "562555f2-8cde-4ce9-8203-0363922537a4"}]}, {"Value": "1760.54", "Attributes": [{"Id": "account", "Value": "562555f2-8cde-4ce9-8203-0363922537a4"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "562555f2-8cde-4ce9-8203-0363922537a4"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Computer Equipment (720)", "Attributes": [{"Id": "account", "Value": "37e5d4fc-fd6e-489c-83da-fdb8cb058d48"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "37e5d4fc-fd6e-489c-83da-fdb8cb058d48"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "37e5d4fc-fd6e-489c-83da-fdb8cb058d48"}]}, {"Value": "3774.49", "Attributes": [{"Id": "account", "Value": "37e5d4fc-fd6e-489c-83da-fdb8cb058d48"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "37e5d4fc-fd6e-489c-83da-fdb8cb058d48"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Office Equipment (710)", "Attributes": [{"Id": "account", "Value": "0289f44f-bdad-48e8-ab5a-502e9d949bd7"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "0289f44f-bdad-48e8-ab5a-502e9d949bd7"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "0289f44f-bdad-48e8-ab5a-502e9d949bd7"}]}, {"Value": "923.79", "Attributes": [{"Id": "account", "Value": "0289f44f-bdad-48e8-ab5a-502e9d949bd7"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "0289f44f-bdad-48e8-ab5a-502e9d949bd7"}]}]}]}, {"RowType": "Section", "Title": "Liabilities", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Accounts Payable (800)", "Attributes": [{"Id": "account", "Value": "c0f7668c-1c46-47bf-8d65-62f8ba2da059"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c0f7668c-1c46-47bf-8d65-62f8ba2da059"}]}, {"Value": "404.73", "Attributes": [{"Id": "account", "Value": "c0f7668c-1c46-47bf-8d65-62f8ba2da059"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "c0f7668c-1c46-47bf-8d65-62f8ba2da059"}]}, {"Value": "8405.66", "Attributes": [{"Id": "account", "Value": "c0f7668c-1c46-47bf-8d65-62f8ba2da059"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Historical Adjustment (840)", "Attributes": [{"Id": "account", "Value": "44ee8a46-0b5f-46ca-b2e3-fbdae39fd13c"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "44ee8a46-0b5f-46ca-b2e3-fbdae39fd13c"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "44ee8a46-0b5f-46ca-b2e3-fbdae39fd13c"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "44ee8a46-0b5f-46ca-b2e3-fbdae39fd13c"}]}, {"Value": "4130.98", "Attributes": [{"Id": "account", "Value": "44ee8a46-0b5f-46ca-b2e3-fbdae39fd13c"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Sales Tax (820)", "Attributes": [{"Id": "account", "Value": "f0bb2f96-7530-416e-b8f0-bcec05e5ff04"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "f0bb2f96-7530-416e-b8f0-bcec05e5ff04"}]}, {"Value": "77.26", "Attributes": [{"Id": "account", "Value": "f0bb2f96-7530-416e-b8f0-bcec05e5ff04"}]}, {"Value": "10.04", "Attributes": [{"Id": "account", "Value": "f0bb2f96-7530-416e-b8f0-bcec05e5ff04"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "f0bb2f96-7530-416e-b8f0-bcec05e5ff04"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Unpaid Expense <PERSON> (801)", "Attributes": [{"Id": "account", "Value": "bab792be-f547-43b3-b81b-289bd52f1dcf"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "bab792be-f547-43b3-b81b-289bd52f1dcf"}]}, {"Value": "115.95", "Attributes": [{"Id": "account", "Value": "bab792be-f547-43b3-b81b-289bd52f1dcf"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "bab792be-f547-43b3-b81b-289bd52f1dcf"}]}, {"Value": "115.95", "Attributes": [{"Id": "account", "Value": "bab792be-f547-43b3-b81b-289bd52f1dcf"}]}]}]}, {"RowType": "Section", "Title": "Equity", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Retained <PERSON><PERSON><PERSON><PERSON> (960)", "Attributes": [{"Id": "account", "Value": "d2e8fca5-83cc-475d-9efb-03fe0dc57fd4"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d2e8fca5-83cc-475d-9efb-03fe0dc57fd4"}, {"Id": "fromDate", "Value": ""}, {"Id": "toDate", "Value": "12/31/2024"}]}, {"Value": "0.00", "Attributes": [{"Id": "account", "Value": "d2e8fca5-83cc-475d-9efb-03fe0dc57fd4"}, {"Id": "fromDate", "Value": ""}, {"Id": "toDate", "Value": "12/31/2024"}]}, {"Value": "", "Attributes": [{"Id": "account", "Value": "d2e8fca5-83cc-475d-9efb-03fe0dc57fd4"}, {"Id": "fromDate", "Value": ""}, {"Id": "toDate", "Value": "12/31/2024"}]}, {"Value": "7489.29", "Attributes": [{"Id": "account", "Value": "d2e8fca5-83cc-475d-9efb-03fe0dc57fd4"}, {"Id": "fromDate", "Value": ""}, {"Id": "toDate", "Value": "12/31/2024"}]}]}]}, {"RowType": "Section", "Title": "", "Rows": [{"RowType": "SummaryRow", "Cells": [{"Value": "Total"}, {"Value": "2025.54"}, {"Value": "2025.54"}, {"Value": "29625.42"}, {"Value": "29625.42"}]}]}, {"RowType": "Section", "Title": "", "Rows": [{"RowType": "SummaryRow", "Cells": [{"Value": "Total"}, {"Value": "2025.54"}, {"Value": "2025.54"}, {"Value": "29625.42"}, {"Value": "29625.42"}]}]}], "UpdatedDateUTC": "/Date(*************)/", "Fields": []}]}