<?php

declare(strict_types=1);

namespace App\Tests\Unit\Templates\Sonata\Form\Type;

use PHPUnit\Framework\TestCase;

class SonataTypeModelAutocompleteUnitTest extends TestCase
{
    public function testBuildDataQueryForBrandCode(): void
    {
        $originalFile = '/vendor/sonata-project/admin-bundle/src/Resources/views/Form/Type/sonata_type_model_autocomplete.html.twig';
        $modifiedFile = '/templates/sonata/form/type/sonata_type_model_autocomplete.html.twig';

        $originalContents = $this->readInAndCleanUpFile($originalFile);
        $modifiedContents = $this->readInAndCleanUpFile($modifiedFile);

        $this->assertEquals($originalContents, $modifiedContents, 'vendor version of sonata_type_model_autocomplete should not be modified');
        // Note: if the vendor version of sonata_type_model_autocomplete is modified then we need to update
        // our version of sonata_type_model_autocomplete to match the vendor version and then check that the
        // over-ridden version(s) (e.g. gravitiq_type_model_autocomplete) are still correct.
    }

    private function readInAndCleanUpFile(string $filename): string
    {
        $projectRoot = realpath(__DIR__ . '/../../../../../..');
        $file = fopen($projectRoot . $filename, 'r');

        $skipLines = [
            "{% apply spaceless %}",
            "{% block stm_autocomplete_select %}",
            "{% block stm_autocomplete_hidden_inputs_wrap %}",
            "{% block stm_autocomplete_field_actions %}",
            "{% block stm_autocomplete_script %}",
            "{% endblock %}",
            "{% endapply %}",
        ];

        $contents = '';
        $bodyStart = false;
        $line = fgets($file);
        while (false !== $line) {
            if ($bodyStart) {
                $thisLine = trim($line);
                if (!in_array($thisLine, $skipLines)) {
                    $contents .= $thisLine . "\n";
                }
            } else {
                if ('#}' === $line) {
                    $bodyStart = true;
                }
            }
            $line = fgets($file);
        }
        fclose($file);

        return $contents;
    }
}