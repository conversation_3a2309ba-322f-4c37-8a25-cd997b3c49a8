<?php

namespace App\Tests\Unit\Service\Tracker;

use App\Service\FileSystemSwitchboard;
use App\Service\Tracker\SupplierManager;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\Unit\DoctrineMockBuilderTrait;
use App\Tests\Unit\GravitiqUnitTest;
use Codeception\Attribute\DataProvider;
use Psr\Log\LoggerInterface;

class SupplierManagerUnitTest extends GravitiqUnitTest
{
    use CanInvokeProtectedMethodTrait, DoctrineMockBuilderTrait;

    private const string VALUE_SAME_AS_INPUT = '~SAME~';

    /**
     * @param array{list<string>} $inputData
     * @param array{list<string>} $expectedSanitizedData
     * @param list<string> $expectedErrors
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestValidateAndSanitizeSupplierPriceListData')]
    public function testValidateAndSanitizeSupplierPriceListData(array $inputData, array $expectedSanitizedData, array $expectedErrors, string $message): void
    {
        $supplierManager = $this->createManagerWithMocks();
        [$sanitizedData, $errors] = $this->invokeProtectedMethod($supplierManager, 'validateAndSanitizeSupplierPriceListData', [$inputData]);
        $this->assertEquals($expectedSanitizedData, $sanitizedData, $message . ' - Sanitized Data');
        $this->assertEquals($expectedErrors, $errors, $message . ' - Errors');
    }

    /**
     * @return list<array{array{list<string>}, array{list<string>}, list<string>, string>
     * @noinspection PhpUnusedPrivateMethodInspection - because it is used as DataProvider
     * @phpstan-ignore-next-line
     */
    private function dataProviderForTestValidateAndSanitizeSupplierPriceListData(): array
    {
        $headers = SupplierManager::HEADER_SUPPLIER_PRICE_LIST;
        return [
            // Test Case 1: All fields valid
            [
                // Input Data
                [
                    [
                        $headers['SUPPLIER_ID'] => '1',
                        $headers['ISKU'] => 'ab-1234',
                        $headers['PRODUCT_CODE'] => 'XYZ',
                        $headers['LEAD_TIME'] => '30',
                        $headers['CURRENCY'] => 'USD',
                        $headers['QTY_1'] => '100',
                        $headers['PRICE_1'] => '10.00',
                        $headers['QTY_2'] => '200',
                        $headers['PRICE_2'] => '9.50',
                        $headers['VALID_FROM'] => '2024-01-01',
                        $headers['VALID_TO'] => '2024-12-31'
                    ]
                ],
                // Expected Sanitized Data
                [
                    [
                        $headers['SUPPLIER_ID'] => '1',
                        $headers['ISKU'] => 'AB-1234',
                        $headers['PRODUCT_CODE'] => 'XYZ',
                        $headers['LEAD_TIME'] => '30',
                        $headers['CURRENCY'] => 'USD',
                        $headers['QTY_1'] => '100',
                        $headers['PRICE_1'] => '10.00',
                        $headers['QTY_2'] => '200',
                        $headers['PRICE_2'] => '9.50',
                        $headers['VALID_FROM'] => '2024-01-01',
                        $headers['VALID_TO'] => '2024-12-31'
                    ]
                ],
                // Expected Errors
                [],
                // Message
                'All valid fields should pass without errors.'
            ],
            // Test Case 2: Missing required fields and invalid formats
            [
                // Input Data
                [
                    [
                        $headers['SUPPLIER_ID'] => '', // Required but missing
                        $headers['ISKU'] => 'InvalidISKU', // Invalid format
                        $headers['PRODUCT_CODE'] => '', // Required but missing
                        $headers['LEAD_TIME'] => 'notanumber', // Invalid format
                        $headers['CURRENCY'] => '123', // Invalid currency code
                        $headers['QTY_1'] => null, // Required but missing
                        $headers['PRICE_1'] => 'notaprice', // Invalid format
                        $headers['VALID_FROM'] => '01-01-2024', // Invalid date format
                        $headers['VALID_TO'] => 'invalid-date' // Invalid date format
                    ]
                ],
                // Expected Sanitized Data
                [],
                // Expected Errors
                [
                    "Missing 'SupplierID' in row 2.",
                    "Missing 'qty1' in row 2.",
                    "'SupplierID' is required but missing in row 2.",
                    "Invalid 'Blueprint ISKU' format for 'INVALIDISKU' in row 2.",
                    "Invalid 'leadTime (days)' value: notanumber. Must be a whole number in row 2.",
                    "Invalid 'currency' value: 123. Must be a three-letter currency code in row 2.",
                    "'qty1' is required but missing in row 2.",
                    "Invalid 'price1' format for 'notaprice'. Must be a numeric value in row 2.",
                    "Invalid 'validFrom' format for '01-01-2024'. Must be in Y-m-d format in row 2.",
                    "Invalid 'validTo' format for 'invalid-date'. Must be in Y-m-d format in row 2.",
                    "Missing qty1 in row 2.",
                    "Missing price1 in row 2.",
                ],
                // Message
                'Missing required fields and invalid formats should result in errors.'
            ],
        ];
    }

    /**
     * @param string $header
     * @param string $value
     * @param mixed $expectedValue
     * @param ?string $expectedErrorStart
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestSanitizeAndValidateSupplierPriceListColumnValue')]
    public function testSanitizeAndValidateSupplierPriceListColumnValue(string $header, string $value, mixed $expectedValue, ?string $expectedErrorStart, string $message): void
    {
        $supplierManager = $this->createManagerWithMocks();
        $result = $this->invokeProtectedMethod($supplierManager, 'sanitizeAndValidateSupplierPriceListColumnValue', [$header, $value]);
        $expectedValue = $expectedValue === self::VALUE_SAME_AS_INPUT ? $value : $expectedValue;
        if (is_null($expectedErrorStart)) {
            $this->assertNull($result['error'], $message . ' - Error');
        } else {
            $this->assertStringStartsWith($expectedErrorStart, $result['error']??'', $message . ' - Error');
        }
        $this->assertEquals($expectedValue, $result['value'], $message . ' - Sanitized Data');
    }

    /**
     * @return list<array{string, string, mixed, ?string, string|null}>
     * @noinspection PhpUnusedPrivateMethodInspection - because it is used as DataProvider
     * @phpstan-ignore-next-line
     */
    private function dataProviderForTestSanitizeAndValidateSupplierPriceListColumnValue(): array
    {
        $headers = SupplierManager::HEADER_SUPPLIER_PRICE_LIST;
        $same = self::VALUE_SAME_AS_INPUT;
        return [
         // [header,                 input, expectedOutput, expectedErrorPrefix, message]
            [$headers['SUPPLIER_ID'],   '', null,   "'{$headers['SUPPLIER_ID']}' is required but missing",  'Required field'],
            [$headers['ISKU'],          '', null,   "'{$headers['ISKU']}' is required but missing",         'Required field'],
            [$headers['PRODUCT_CODE'],  '', null,   null, 'Blank input is ok'],
            [$headers['LEAD_TIME'],     '', null,   null, 'Blank input is ok'],
            [$headers['CURRENCY'],      '', null,   "'{$headers['CURRENCY']}' is required but missing",     'Required field'],
            [$headers['QTY_NOTES'],     '', '',     null, 'Blank input is ok'],
            [$headers['QTY_1'],         '', null,   "'{$headers['QTY_1']}' is required but missing",        'Required field'],
            [$headers['PRICE_1'],       '', null,   "'{$headers['PRICE_1']}' is required but missing",      'Required field'],
            [$headers['QTY_2'],         '', '',     null, 'Blank input is ok'],
            [$headers['PRICE_2'],       '', '',     null, 'Blank input is ok'],
            [$headers['QTY_3'],         '', '',     null, 'Blank input is ok'],
            [$headers['PRICE_3'],       '', '',     null, 'Blank input is ok'],
            [$headers['QTY_4'],         '', '',     null, 'Blank input is ok'],
            [$headers['PRICE_4'],       '', '',     null, 'Blank input is ok'],
            [$headers['QTY_5'],         '', '',     null, 'Blank input is ok'],
            [$headers['PRICE_5'],       '', '',     null, 'Blank input is ok'],
            [$headers['VALID_FROM'],    '', '',     null, 'Blank input is ok'],
            [$headers['VALID_TO'],      '', '',     null, 'Blank input is ok'],
            [$headers['NOTES'],         '', '',     null, 'Blank input is ok'],

            [$headers['ISKU'],          'XX-THIS-IS-OK',    $same,  null,                               'Valid iSKU format'],
            [$headers['ISKU'],          'XX-THIS-IS-£BAD',  null,   "Invalid '{$headers['ISKU']}' format ", 'Invalid iSKU - wrong characters'],
            [$headers['ISKU'],          'XXTHIS-IS-BAD',    null,   "Invalid '{$headers['ISKU']}' format ", 'Invalid iSKU - missing brand code'],
            [$headers['ISKU'],          'XX-this-is-ok',    'XX-THIS-IS-OK',  null,                     'Valid iSKU should be cleaned up to upper case'],

            [$headers['LEAD_TIME'],     '30',               $same,  null,                               'Valid lead time'],
            [$headers['LEAD_TIME'],     '3.0',              null,   "Invalid '{$headers['LEAD_TIME']}' value: 3.0. Must be a whole number", 'Invalid lead time'],

            [$headers['CURRENCY'],      'GBP',              $same,  null,                                   "Valid currency"],
            [$headers['CURRENCY'],      'GBPX',             null,   "Invalid '{$headers['CURRENCY']}' ",    "Invalid currency (too long)"],
            [$headers['CURRENCY'],      'US',               null,   "Invalid '{$headers['CURRENCY']}' ",    "Invalid currency (too short)"],
            [$headers['CURRENCY'],      'US2',              null,   "Invalid '{$headers['CURRENCY']}' ",    "Invalid currency (not all letters)"],
            [$headers['CURRENCY'],      'gbp',              'GBP',  null,                                   "Valid currency, should be converted to upper case"],
            [$headers['CURRENCY'],      'RMB',              'CNY',  null,                                   "Alias for CNY is changed to ISO currency code"],
            [$headers['CURRENCY'],      'rmb',              'CNY',  null,                                   "Alias for CNY is changed to ISO currency code"],

            [$headers['QTY_1'],         '123',              $same,  null, 'Valid qty1'],
            [$headers['QTY_1'],         '12.3',             null,   "Invalid '{$headers['QTY_1']}' format for", 'Invalid qty1: should not contain decimal'],
            [$headers['QTY_1'],         '12k',              null,   "Invalid '{$headers['QTY_1']}' format for", 'Invalid qty1: should not contain letters'],
            [$headers['PRICE_1'],       '5',                $same,  null, 'Valid price1 integer'],
            [$headers['PRICE_1'],       '5.2',              $same,  null, 'Valid price1 with one decimal'],
            [$headers['PRICE_1'],       '5.23',             $same,  null, 'Valid price1 with two decimals'],
            [$headers['PRICE_1'],       '5.234',            $same,  null, 'Valid price1 with three decimals'],
            [$headers['PRICE_1'],       '$5.23',            null,   "Invalid '{$headers['PRICE_1']}' format for", 'Invalid price1: should only contain numbers'],

            [$headers['VALID_FROM'],    '2024-12-31',       $same,  null, 'Valid from date'],
            [$headers['VALID_FROM'],    '2024-31-12',       null,  "Invalid '{$headers['VALID_FROM']}' format for", 'Invalid from date'],
            [$headers['VALID_TO'],      '2024-12-31',       $same,  null, 'Valid to date'],
            [$headers['VALID_TO'],      '2024-31-12',       null,  "Invalid '{$headers['VALID_TO']}' format for", 'Invalid to date'],
        ];
    }

    /**
     * @param array<string, mixed> $input
     * @param list<string> $expectedErrors
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestValidateEntireSupplierPriceListRow')]
    public function testValidateEntireSupplierPriceListRow(array $input, array $expectedErrors, string $message): void
    {
        $supplierManager = $this->createManagerWithMocks();
        $result = $this->invokeProtectedMethod($supplierManager, 'validateEntireSupplierPriceListRow', [$input]);
        $this->assertEquals($expectedErrors, $result, $message);
    }

    /**
     * @return list<array{array<string, mixed>, list<string>, string}>
     * @noinspection PhpUnusedPrivateMethodInspection - because it is used as DataProvider
     * @phpstan-ignore-next-line
     */
    private function dataProviderForTestValidateEntireSupplierPriceListRow(): array
    {
        $missQty1 = 'Missing qty1';
        $missPrice1 = 'Missing price1';
        $missPart2 = 'Need to set price AND qty for price break 2';
        $rangeQty2 = 'Qty break 2 must be greater than qty break 1';
        $rangePrice2v2 = 'Price break 2 (2) must be less than price break 1 (2)';
        $rangePrice201 = 'Price break 2 (2.01) must be less than price break 1 (2)';
        $missing2 = 'Price break #1 and #3 provided, but #2 is missing';
        return [
            [[],                                                [$missQty1,$missPrice1],    'empty input warns about missing break1'],
            [['qty1'=>0],                                       [$missQty1,$missPrice1],    'zero qty1 creates error'],
            [['qty1'=>1],                                       [$missPrice1],              'missing price1 creates warning'],
            [['price1'=>0],                                     [$missQty1,$missPrice1],    'zero price1 creates error'],
            [['price1'=>1],                                     [$missQty1],                'missing qty1 creates warning'],
            [['qty1'=>1,'price1'=>1],                           [],                         'valid break1 has no warnings'],
            [['qty1'=>1,'price1'=>1,'qty2'=>0,'price2'=>0],     [],                         'empty break2 is ignored'],
            [['qty1'=>1,'price1'=>1,'qty2'=>2,'price2'=>0],     [$missPart2],               'missing price2 creates warning'],
            [['qty1'=>2,'price1'=>2,'qty2'=>3],                 [$missPart2],               'missing price2 creates warning'],
            [['qty1'=>1,'price1'=>2,'qty2'=>0,'price2'=>1],     [$missPart2],               'missing price2 creates warning'],
            [['qty1'=>2,'price1'=>2,'price2'=>1],               [$missPart2],               'missing price2 creates warning'],
            [['qty1'=>1,'price1'=>2,'qty2'=>1,'price2'=>2],     [$rangeQty2,$rangePrice2v2],'same qty or price creates warning'],
            [['qty1'=>1,'price1'=>2,'qty2'=>1,'price2'=>1.99],  [$rangeQty2],               'same qty creates warning'],
            [['qty1'=>1,'price1'=>2,'qty2'=>2,'price2'=>2],     [$rangePrice2v2],           'same price creates warning'],
            [['qty1'=>2,'price1'=>2,'qty2'=>1,'price2'=>1.99],  [$rangeQty2],               'lower qty creates warning'],
            [['qty1'=>2,'price1'=>2,'qty2'=>3,'price2'=>2.01],  [$rangePrice201],           'higher price creates warning'],
            [['qty1'=>2,'price1'=>2,'qty2'=>3,'price2'=>1],     [],                         'higher qty and lower price is good'],
            [['qty1'=>2,'price1'=>2,'qty3'=>3,'price3'=>1],     [$missing2],                'break 1+3 warns about missing 2'],
            [['qty1'=>2,'price1'=>5,'qty2'=>3,'price2'=>4,'qty3'=>4,'price3'=>3], [],       'works with 3 valid breaks'],
        ];
    }

    private function createManagerWithMocks(): SupplierManager
    {
        $switchboardMock = $this->createMock(FileSystemSwitchboard::class);
        $loggerMock = $this->createMock(LoggerInterface::class);
        $doctrineMock = $this->buildDoctrineMockWithoutExpectations();
        return new SupplierManager($loggerMock, $doctrineMock, $switchboardMock, 'test');
    }
}