<?php

namespace App\Tests\Unit\Service\Tracker;

use App\Service\FileSystemSwitchboard;
use App\Service\Tracker\BoxPlanManager;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\Unit\DoctrineMockBuilderTrait;
use App\Tests\Unit\GravitiqUnitTest;
use Codeception\Attribute\DataProvider;
use Psr\Log\LoggerInterface;

class BoxPlanManagerUnitTest extends GravitiqUnitTest
{
    use CanInvokeProtectedMethodTrait, DoctrineMockBuilderTrait;

    /**
     * @param array{list<string>} $inputData
     * @param array{list<string>} $expectedSanitizedData
     * @param list<string> $expectedErrors
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestValidateBoxPlanRawData')]
    public function testValidateBoxPlanRawData(array $inputData, array $expectedSanitizedData, array $expectedErrors, string $message): void
    {
        $boxPlanManager = $this->createManagerWithMocks();
        [$sanitizedData, $errors] = $this->invokeProtectedMethod($boxPlanManager, 'validateBoxPlanRawData', [$inputData]);

        $this->assertEquals($expectedSanitizedData, $sanitizedData, $message . ' - Sanitized Data');
        $this->assertEquals($expectedErrors, $errors, $message . ' - Errors');
    }

    /**
     * @return list<array{array{list<string>}, array{list<string>}, list<string>, string>
     * @noinspection PhpUnusedPrivateMethodInspection - because it is used as DataProvider
     * @phpstan-ignore-next-line
     */
    private function dataProviderForTestValidateBoxPlanRawData(): array
    {
        $headers = BoxPlanManager::HEADER_BOX_PLAN;
        return [
            // Test Case 1: All fields valid
            [
                'inputData' => [
                    [
                        $headers['BOX_PLAN_GROUP'] => 'Group1',
                        $headers['DESCRIPTION'] => 'Sample Description',
                        $headers['WEIGHT'] => '1000',
                        $headers['DIMENSIONS'] => '10x20x30',
                        $headers['ISKU'] => 'XX-TEST-01',
                        $headers['QTY'] => '10',
                    ]
                ],
                'expectedSanitizedData' => [
                    [
                        $headers['BOX_PLAN_GROUP'] => 'Group1',
                        $headers['DESCRIPTION'] => 'Sample Description',
                        $headers['WEIGHT'] => '1000',
                        $headers['DIMENSIONS'] => '10x20x30',
                        $headers['ISKU'] => 'XX-TEST-01',
                        $headers['QTY'] => '10',
                    ]
                ],
                'expectedErrors' => [],
                'message' => 'All valid fields should pass without errors',
            ],
            // Test Case 2: Missing required fields (BOX_PLAN_GROUP and ISKU)
            [
                'inputData' => [
                    [
                        $headers['DESCRIPTION'] => 'Missing Group and ISKU',
                        $headers['WEIGHT'] => '500',
                        $headers['DIMENSIONS'] => '15x25x35',
                        $headers['QTY'] => '5',
                    ]
                ],
                'expectedSanitizedData' => [],
                'expectedErrors' => [
                    "Missing 'BoxPlanGroup' in row 2.",
                    "Missing 'ISKU' in row 2.",
                ],
                'message' => 'Missing required fields should result in errors',
            ],
            // Test Case 3: Invalid formats (WEIGHT, DIMENSIONS, QTY)
            [
                'inputData' => [
                    [
                        $headers['BOX_PLAN_GROUP'] => 'InvalidFormats',
                        $headers['ISKU'] => 'XX-TEST-01',
                        $headers['WEIGHT'] => 'one thousand', // Invalid weight format
                        $headers['DIMENSIONS'] => '10xx30', // Invalid dimensions format
                        $headers['QTY'] => 'ten', // Invalid quantity format
                    ]
                ],
                'expectedSanitizedData' => [],
                'expectedErrors' => [
                    "'Qty' must be an integer in row 2.",
                    "'Weight' must be an integer in row 2.",
                    "'Dimensions' format is invalid in row 2.",
                    "Missing 'Dimensions' for BoxPlanGroup 'InvalidFormats'."
                ],
                'message' => 'Invalid formats should result in errors',
            ],
            // Test Case 4: Inconsistent data within the same group (WEIGHT and DIMENSIONS)
            [
                'inputData' => [
                    [
                        $headers['BOX_PLAN_GROUP'] => 'InconsistentGroup',
                        $headers['ISKU'] => 'XX-TEST-01',
                        $headers['WEIGHT'] => '1000',
                        $headers['DIMENSIONS'] => '10x20x30',
                        $headers['QTY'] => '10',
                    ],
                    [
                        $headers['BOX_PLAN_GROUP'] => 'InconsistentGroup',
                        $headers['ISKU'] => 'XX-TEST-02',
                        $headers['WEIGHT'] => '1500', // Inconsistent weight
                        $headers['DIMENSIONS'] => '15x25x35', // Inconsistent dimensions
                        $headers['QTY'] => '15',
                    ]
                ],
                'expectedSanitizedData' => [],
                'expectedErrors' => [
                    "Inconsistent 'Weight' found in BoxPlanGroup 'InconsistentGroup' at row 3.",
                    "Inconsistent 'Dimensions' found in BoxPlanGroup 'InconsistentGroup' at row 3.",
                ],
                'message' => 'Inconsistent data within the same group should result in errors',
            ],
            // Test Case 5: Duplicate ISKU within the same group
            [
                'inputData' => [
                    [
                        $headers['BOX_PLAN_GROUP'] => 'DuplicateISKUGroup',
                        $headers['ISKU'] => 'XX-TEST-01',
                        $headers['WEIGHT'] => '1000',
                        $headers['DIMENSIONS'] => '10x20x30',
                        $headers['QTY'] => '10',
                    ],
                    [
                        $headers['BOX_PLAN_GROUP'] => 'DuplicateISKUGroup',
                        $headers['ISKU'] => 'XX-TEST-01', // Duplicate ISKU
                        $headers['WEIGHT'] => '1000',
                        $headers['DIMENSIONS'] => '10x20x30',
                        $headers['QTY'] => '15',
                    ]
                ],
                'expectedSanitizedData' => [],
                'expectedErrors' => [
                    "Duplicate 'ISKU' found in BoxPlanGroup 'DuplicateISKUGroup' at row 3.",
                ],
                'message' => 'Duplicate ISKU within the same group should result in errors',
            ],
        ];
    }

    private function createManagerWithMocks(): BoxPlanManager
    {
        $switchboardMock = $this->createMock(FileSystemSwitchboard::class);
        $loggerMock = $this->createMock(LoggerInterface::class);
        $doctrineMock = $this->buildDoctrineMockWithoutExpectations();
        return new BoxPlanManager($loggerMock, $doctrineMock, $switchboardMock, 'test');
    }
}