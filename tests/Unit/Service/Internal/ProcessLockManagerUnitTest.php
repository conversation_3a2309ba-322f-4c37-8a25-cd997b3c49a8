<?php

namespace App\Tests\Unit\Service\Internal;

use App\Entity\Internal\ProcessLock;
use App\Service\Internal\ProcessLockManager;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Unit\GravitiqUnitTest;
use Codeception\Attribute\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;

class ProcessLockManagerUnitTest extends GravitiqUnitTest
{
    use CanInvokeProtectedMethodTrait;

    /**
     * @var array<string, ProcessLock>
     */
    protected array $lockMap;

    /**
     * @param string|ProcessLock $lockOrLockCode
     * @param int|null $lockExpiresAfter
     * @param bool $expectedResult
     * @param string $description
     *
     * @throws \Exception
     */
    #[DataProvider('dataProviderForIsLocked')]
    public function testIsLocked(string|ProcessLock $lockOrLockCode, ?int $lockExpiresAfter, bool $expectedResult, string $description): void
    {
        $this->initLockMap();

        if ($lockOrLockCode instanceof ProcessLock) {
            // getLockEntity should not be called
            $expectedGetLockEntityCalls = 0;
            $lockCode = $lockOrLockCode->getLockCode();
        } elseif (!empty($lockOrLockCode)) {
            // getLockEntity should be called
            $expectedGetLockEntityCalls = 1;
            $lockCode = $lockOrLockCode;
        } else {
            // Empty lock code, getLockEntity should not be called
            $expectedGetLockEntityCalls = 0;
            $lockCode = '';
        }

        if (str_contains($lockCode, ProcessLock::PARENT_CHILD_SEPARATOR)) {
            ++$expectedGetLockEntityCalls;
        } else {
            if (str_starts_with($lockCode, 'lock4_parent_not_locked')) {
                ++$expectedGetLockEntityCalls;
            } elseif (str_starts_with($lockCode, 'lock5_parent_not_locked')) {
                ++$expectedGetLockEntityCalls;
            }
        }

        // Create a partial mock of ProcessLockManager
        /** @var ProcessLockManager|MockObject $processLockManagerMock */
        $processLockManagerMock = $this->createPartialMock(ProcessLockManager::class, ['getLockEntity','retrieveChildrenForLockCode']);
        $lockMap = $this->lockMap;
        $processLockManagerMock
            ->expects($this->exactly($expectedGetLockEntityCalls))
            ->method('getLockEntity')
            ->willReturnCallback(function ($lockCode) use ($lockMap) {
                return $lockMap[$lockCode] ?? null;
            })
        ;
        $processLockManagerMock
//            ->expects($this->exactly($expectedGetLockEntityCalls))
            ->method('retrieveChildrenForLockCode')
            ->willReturnCallback(function ($lockCode) use ($lockMap) {
                $matches = [];
                if (!str_contains($lockCode, ProcessLock::PARENT_CHILD_SEPARATOR)) {
                    $lockCode .= ProcessLock::PARENT_CHILD_SEPARATOR;
                }
                foreach ($lockMap as $loopLockCode => $lock) {
                    if (str_starts_with($loopLockCode, $lockCode)) {
                        $matches[] = $lock;
                    }
                }
                return $matches;
            })
        ;

        /** @var LoggerInterface|MockObject $loggerMock */
        $loggerMock = $this->createMock(LoggerInterface::class);
        $processLockManagerMock->setLogger($loggerMock);

        $result = $processLockManagerMock->isLocked($lockOrLockCode, $lockExpiresAfter);
        $this->assertEquals($expectedResult, $result, $description);
    }

    /**
     * Data provider for testIsLocked.
     *
     * @return list<array{lockOrLockCode: string|ProcessLock, lockExpiresAfter: ?int, expectedResult: bool, description: string}>
     * @throws \Exception
     */
    public function dataProviderForIsLocked(): array
    {
        $this->initLockMap();

        return [
            [
                'lockOrLockCode'   => '',
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'Empty lock code, should return false',
            ],
            [
                'lockOrLockCode'   => 'unknown_lock',
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'Lock not found, should return false',
            ],
            [
                'lockOrLockCode'   => 'lock1_locked_10mins_ago',
                'lockExpiresAfter' => null,
                'expectedResult'   => true,
                'description'      => 'Active lock without expiration, should return true',
            ],
            [
                'lockOrLockCode'   => 'lock2_unlocked_1min_ago',
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'Unlocked lock, should return false',
            ],
            [
                'lockOrLockCode'   => 'lock1_locked_10mins_ago',
                'lockExpiresAfter' => 5,
                'expectedResult'   => false,
                'description'      => 'Active lock expired, should return false',
            ],
            [
                'lockOrLockCode'   => 'lock1_locked_10mins_ago',
                'lockExpiresAfter' => 15,
                'expectedResult'   => true,
                'description'      => 'Active lock not expired, should return true',
            ],
            [
                'lockOrLockCode'   => 'lock3_locked_with_warning',
                'lockExpiresAfter' => null,
                'expectedResult'   => true,
                'description'      => 'Active lock older than expected, should return true and log warning',
            ],
            [
                'lockOrLockCode'   => $this->lockMap['lock1_locked_10mins_ago'],
                'lockExpiresAfter' => null,
                'expectedResult'   => true,
                'description'      => 'ProcessLock instance passed, active lock, should return true',
            ],
            [
                'lockOrLockCode'   => $this->lockMap['lock2_unlocked_1min_ago'],
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'ProcessLock instance passed, unlocked lock, should return false',
            ],
            [
                'lockOrLockCode'   => $this->lockMap['lock1_locked_10mins_ago'],
                'lockExpiresAfter' => 5,
                'expectedResult'   => false,
                'description'      => 'ProcessLock instance passed, expired lock, should return false',
            ],

            [
                'lockOrLockCode'   => 'lock1_locked_10mins_ago:child',
                'lockExpiresAfter' => 15,
                'expectedResult'   => true,
                'description'      => 'Child with locked parent, should return true',
            ],
            [
                'lockOrLockCode'   => 'lock2_unlocked_1min_ago:child',
                'lockExpiresAfter' => 15,
                'expectedResult'   => false,
                'description'      => 'Child with unlocked parent, should return false',
            ],

            [
                'lockOrLockCode'   => 'lock4_parent_not_locked',
                'lockExpiresAfter' => null,
                'expectedResult'   => true,
                'description'      => 'Parent with locked child, should return true',
            ],
            [
                'lockOrLockCode'   => 'lock4_parent_not_locked:lock4c_child_locked',
                'lockExpiresAfter' => null,
                'expectedResult'   => true,
                'description'      => 'Child directly locked (unlocked parent), should return true',
            ],
            [
                'lockOrLockCode'   => 'lock5_parent_not_locked',
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'Parent with unlocked child, should return false',
            ],
            [
                'lockOrLockCode'   => 'lock5_parent_not_locked:lock5c_child_not_locked',
                'lockExpiresAfter' => null,
                'expectedResult'   => false,
                'description'      => 'Child not locked (unlocked parent), should return false',
            ],
        ];
    }

    protected function initLockMap(): void
    {
        $now = new \DateTimeImmutable();
        $templateLock = new ProcessLock();
        $templateLock
            ->setUnlockDate(null)
            ->setWarnAfterDate(null)
        ;

        $lock1 = clone $templateLock;
        $lock1
            ->setLockCode('lock1_locked_10mins_ago')
            ->setLockDate($now->sub(new \DateInterval('PT10M'))) // Locked 10 minutes ago
        ;

        $lock2 = clone $templateLock;
        $lock2
            ->setLockCode('lock2_unlocked_1min_ago')
            ->setLockDate($now->sub(new \DateInterval('PT15M'))) // Locked 15 minutes ago
            ->setUnlockDate($now->sub(new \DateInterval('PT1M'))) // Unlocked 1 minute ago
        ;

        $lock3 = clone $templateLock;
        $lock3
            ->setLockCode('lock3_locked_with_warning')
            ->setLockDate($now->sub(new \DateInterval('PT20M'))) // Locked 20 minutes ago
            ->setWarnAfterDate($now->sub(new \DateInterval('PT5M'))) // Warn if older than 5 minutes ago
        ;

        $lock4 = clone $templateLock;
        $lock4
            ->setLockCode('lock4_parent_not_locked')
            ->setLockDate($now->sub(new \DateInterval('PT15M'))) // Locked 15 minutes ago
            ->setUnlockDate($now->sub(new \DateInterval('PT1M'))) // Unlocked 1 minute ago
        ;
        $lock4c = clone $templateLock;
        $lock4c
            ->setLockCode('lock4_parent_not_locked:lock4c_child_locked')
            ->setLockDate($now->sub(new \DateInterval('PT15M'))) // Locked 15 minutes ago
        ;

        $lock5 = clone $lock4;
        $lock5
            ->setLockCode('lock5_parent_not_locked')
        ;
        $lock5c = clone $lock4c;
        $lock5c
            ->setLockCode('lock5_parent_not_locked:lock5c_child_not_locked')
            ->setUnlockDate($now->sub(new \DateInterval('PT1M'))) // Unlocked 1 minute ago
        ;

        $this->lockMap = [
            'lock1_locked_10mins_ago'   => $lock1,
            'lock2_unlocked_1min_ago'   => $lock2,
            'lock3_locked_with_warning' => $lock3,
            'lock4_parent_not_locked'   => $lock4,
            'lock4_parent_not_locked:lock4c_child_locked'       => $lock4c,
            'lock5_parent_not_locked'   => $lock5,
            'lock5_parent_not_locked:lock5c_child_not_locked'   => $lock5c,
        ];
    }
}