<?php

namespace App\Tests\Unit\Trait;

use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tests\Trait\Unit\DoctrineMockBuilderTrait;
use App\Tests\Unit\GravitiqUnitTest;
use Codeception\Attribute\DataProvider;
use App\Tests\Override\Entity\Trait\ComparableTraitBooleanGetterTestEntity;
use App\Tests\Override\Entity\Trait\ComparableTraitTestEntity;

class ComparableTraitUnitTest extends GravitiqUnitTest
{
    use DoctrineMockBuilderTrait, CanInvokeProtectedMethodTrait;

    /**
     * @param array<string, mixed> $item1
     * @param array<string, mixed> $item2
     * @param bool $expected
     * @param string $message
     * @return void
     */
    #[DataProvider('dataProviderForTestIsSignificantlyDifferentToRecord')]
    public function testIsSignificantlyDifferentToRecord(array $item1, array $item2, bool $expected, string $message): void
    {
        $orderItem1 = new ComparableTraitTestEntity();
        $orderItem2 = new ComparableTraitTestEntity();

        if (!empty($item1)) {
            foreach ($item1 as $field => $value) {
                $setter = 'set' . ucfirst($field);
                $orderItem1->$setter($value);
                if (!empty($item2)) $orderItem2->$setter($item2[$field]);
            }
        } else {
            foreach ($item2 as $field => $value) {
                $setter = 'set' . ucfirst($field);
                $orderItem2->$setter($value);
            }
        }

        $this->assertEquals($expected, $orderItem1->isSignificantlyDifferentToRecord($orderItem2), $message);
    }

    /**
     * @return list<array{array<string, mixed>, array<string, mixed>, bool, string}>
     */
    protected function dataProviderForTestIsSignificantlyDifferentToRecord(): array
    {
        return [
            // string field tests
            [
                ['stringField' => 'abc'],
                ['stringField' => 'abc'],
                false, 'Should be same: Identical records'
            ],
            [
                ['stringField' => 'ABC'],
                ['stringField' => 'XYZ'],
                true, 'Should be different: different strings'
            ],
            [
                ['stringField' => 'ABC'],
                ['stringField' => 'abc'],
                true, 'Should be different: upper case compared to lower case'
            ],

            // boolean field tests
            [
                ['boolField' => true],
                ['boolField' => null],
                true, 'Should be different: Boolean true compared to null'
            ],
            [
                ['boolField' => true],
                ['boolField' => false],
                true, 'Should be different: Boolean true compared to false'
            ],
            [
                ['boolField' => false],
                ['boolField' => null],
                true, 'Should be different: Boolean false compared to null'
            ],

            // integer field tests
            [
                ['intField' => 10],
                ['intField' => 0],
                true, 'Should be different: 10 compared to 0'
            ],
            [
                ['intField' => 10],
                ['intField' => null],
                true, 'Should be different: 10 compared to null'
            ],
            [
                ['intField' => 0],
                ['intField' => 0],
                false, 'Should be same: 0 compared to 0'
            ],
            [
                ['intField' => 0],
                ['intField' => null],
                true, 'Should be different: 0 compared to null'
            ],
            [
                ['intField' => 100000],
                ['intField' => 100001],
                true, 'Should be different: integers with 1.00001 ratio'
            ],
            [
                ['intField' => 100001],
                ['intField' => 100002],
                false, 'Should be same: integers with < 1.00001 ratio'
            ],

            // float field tests
            [
                ['floatField' => 3.0],
                ['floatField' => 3],
                false, 'Should be same: 3.0 compared to 3'
            ],
            [
                ['floatField' => 5.0],
                ['floatField' => null],
                true, 'Should be different: 5.0 compared to null'
            ],
            [
                ['floatField' => 0.0],
                ['floatField' => null],
                true, 'Should be different: 0.0 compared to null'
            ],
            [
                ['floatField' => 0.0],
                ['floatField' => 0.00],
                false, 'Should be same: 0.0 compared to 0.00'
            ],
            [
                ['floatField' => 1000.00],
                ['floatField' => 1000.01],
                true, 'Should be different: floats with 1.00001 ratio'
            ],
            [
                ['floatField' => 1000.02],
                ['floatField' => 1000.01],
                false, 'Should be same: floats with < 1.00001 ratio'
            ],

            //ignored field tests
            [
                ['ignoreField' => '123'],
                ['ignoreField' => '456'],
                false, 'Should be same: different ignoredField values are ignored'
            ],
            [
                ['id' => 123],
                ['id' => 456],
                false, 'Should be same: different id values are ignored'
            ],

            // uninitialised field tests
            [
                [],
                ['stringField' => 'abc'],
                true, 'Should be different: uninitialised field compared to real string'
            ],
            [
                ['stringField' => 'abc'],
                [],
                true, 'Should be different: real string compared to uninitialised field'
            ],
        ];
    }


    public function testComparableTraitIfMethodDoesNotExists(): void
    {
        $item2 = new ComparableTraitBooleanGetterTestEntity();

        $entityMock = $this->createMock(ComparableTraitBooleanGetterTestEntity::class);
        $entityMock
            ->expects($this->once())
            ->method('isBoolFieldWithIsGetter');
        $entityMock
            ->expects($this->once())
            ->method('hasBoolFieldWithHasGetter');

        $item2->isSignificantlyDifferentToRecord($entityMock);
    }

}


