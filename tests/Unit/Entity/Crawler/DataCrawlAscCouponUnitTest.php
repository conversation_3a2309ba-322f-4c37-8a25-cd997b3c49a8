<?php

namespace App\Tests\Unit\Entity\Crawler;

use App\Entity\Crawler\DataCrawlAscCoupon;
use PHPUnit\Framework\TestCase;

class DataCrawlAscCouponUnitTest extends TestCase
{
    public function testCreateCloneForDates(): void
    {
        $coupon = new DataCrawlAscCoupon();
        $coupon
            ->setStartDateFromString('2024-01-01')
            ->setEndDateFromString('2024-01-05')
            ->setBudgetFromDecimal(1000.00)
        ;

        $clonedCoupon = $coupon->createCloneForDates('2024-01-01', '2024-01-10');
        $this->assertNotSame($coupon, $clonedCoupon, 'Cloned coupon should not be the same object');
        $this->assertEquals('2024-01-01', $clonedCoupon->getStartDate()->format('Y-m-d'), 'Cloned coupon should have new start date');
        $this->assertEquals('2024-01-10', $clonedCoupon->getEndDate()->format('Y-m-d'), 'Cloned coupon should have new end date');
        $this->assertEquals($coupon->getBudgetPerDay(), $clonedCoupon->getBudgetPerDay(), 'Cloned coupon should have same daily budget');
        $this->assertEquals(2000*100, $clonedCoupon->getBudget(), 'Cloned coupon should have double the total budget (it is for twice as many days)');
    }

    public function testGetBudgetPerDay(): void
    {
        $coupon = new DataCrawlAscCoupon();
        $coupon
            ->setStartDateFromString('2024-01-01')
            ->setEndDateFromString('2024-01-05')
            ->setBudgetFromDecimal(1000.00)
        ;
        $this->assertEquals(200*100, $coupon->getBudgetPerDay(), '$1000 over 5 days should give Budget per day of $200 (in cents)');

        $coupon->setBudgetFromDecimal(100.00);
        $this->assertEquals(20*100, $coupon->getBudgetPerDay(), '$100 over 5 days should give Budget per day of $20 (in cents)');

        $coupon->setBudgetFromDecimal(123.21);
        $this->assertEquals(24.64*100, $coupon->getBudgetPerDay(), 'Budget per day should be rounded to nearest cent');
    }

    public function testSetBudgetBasedOnDailySpend(): void
    {
        $coupon = new DataCrawlAscCoupon();
        $coupon
            ->setStartDateFromString('2024-01-01')
            ->setEndDateFromString('2024-01-05')
            ->setBudgetBasedOnDailySpend(50*100) // $50 in cents
        ;
        $this->assertEquals(250, $coupon->getBudgetAsDecimal(), '$50 per day for 5 days should give a total of $250');

        $coupon->setBudgetBasedOnDailySpend(10*100); // $10 in cents, too low so minimum will be used
        $this->assertEquals(100, $coupon->getBudgetAsDecimal(), 'Budget per day should never be below the minimum ($100)');
    }
}