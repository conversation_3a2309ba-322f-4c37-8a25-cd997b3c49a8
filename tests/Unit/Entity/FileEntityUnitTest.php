<?php

namespace App\Tests\Unit\Entity;

use App\Entity\FileEntity;
use PHPUnit\Framework\TestCase;

class FileEntityUnitTest extends TestCase
{
    /**
     * @dataProvider dataProviderTestGenerateFilenamePrefix
     */
    public function testGenerateFilenamePrefix(int $id, string $expected, string $message): void
    {
        $testObject = new class () extends FileEntity {
            public function setId(int $id): void
            { $this->id = $id; }
        };

        $testObject->setId($id);
        $actual = $testObject->generateFilenamePrefix();
        $this->assertEquals($expected, $actual, $message);
    }

    /**
     * @return list<array{int, string, string}>
     */
    public function dataProviderTestGenerateFilenamePrefix(): array
    {
        return [
            [1,         '0/',       'number below 1000 return 0'],
            [999,       '0/',       'number below 1000 return 0'],
            [1000,      '1/',       '1000 returns 1'],
            [1001,      '1/',       '1000 returns 1'],
            [56789,     '56/',      'multiples of 1000 returns the thousands part'],
            [999999,    '999/',     'multiples of 1000 returns the thousands part'],
            [1000000,   'm/1/0/',   'number over 1m return m/#/ and then the smaller part'],
            [1000001,   'm/1/0/',   'number over 1m return m/#/ and then the smaller part'],
            [1000999,   'm/1/0/',   'number over 1m return m/#/ and then the smaller part'],
            [99000999,  'm/99/0/',  'number over 1m return m/#/ and then the smaller part'],
            [99999999,  'm/99/999/','number over 1m return m/#/ and then the smaller part'],
            [**********,'b/1/222/0/','number over 1b return b/#/#/ and then the smaller part'],
        ];
    }
}
