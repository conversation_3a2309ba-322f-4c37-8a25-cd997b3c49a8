<?php

namespace App\Tests\Unit\Entity\ExperimentResult;

//use App\Entity\ExperimentResult\Experiment;
//use App\Entity\ExperimentResult\ResultData;
//use App\Entity\ExperimentResult\Target;
use App\Entity\ExperimentResult\Experiment;
use App\Entity\ExperimentResult\ResultData;
use Codeception\Attribute\DataProvider;
use PHPUnit\Framework\TestCase;

class ExperimentUnitTest extends TestCase
{
    #[DataProvider('dataProviderForTestCalculateProbBBetter')]
    public function testCalculateProbBBetter(?float $rsSignificance, ?float $caConversionDelta, ?float $expected, ?string $message=null): void
    {
        $experiment = new Experiment();
        $experiment->setRsSignificance($rsSignificance);
        $experiment->setCaConversionDelta($caConversionDelta);
        $actual = $experiment->calculateProbBBetter();
        $this->assertEquals($expected, $actual, $message);
    }

    /**
     * @return list<array{float, float, float, ?string}>
     */
    protected function dataProviderForTestCalculateProbBBetter(): array
    {
        return [
            [0.25, 0.01, 0.25, 'Should return rsSignificance when B is better than A'],
            [0.25, -0.1, 0.75, 'Should return 1-rsSignificance when A is better than B'],
            [null, 0.25, null, 'Should return null if rsSignificance is null'],
            [0.25, null, null, 'Should return null if caConversionDelta is null'],
            [null, null, null, 'Should return null if rsSignificance or caConversionDelta are null'],
            [0,    0.25, null, 'Should return null if rsSignificance is zero'],
            [0.25, 0,    null, 'Should return null if caConversionDelta is zero'],
            [0,    0,    null, 'Should return null if rsSignificance and caConversionDelta are zero'],
        ];
    }

    public function testCalculateProjectedImpactFields(): void
    {
        $experiment = new Experiment();
        $experiment->addResultData($this->buildMockResultData());

        $this->assertEquals(1500, $experiment->calculateBestCaseProjectedAnnualIncrementalUnits(), 'calculateBestCaseProjectedAnnualIncrementalUnits');
        $this->assertEquals(1000, $experiment->calculateMostLikelyProjectedAnnualIncrementalUnits(), 'calculateMostLikelyProjectedAnnualIncrementalUnits');
        $this->assertEquals(500, $experiment->calculateWorstCaseProjectedAnnualIncrementalUnits(), 'calculateWorstCaseProjectedAnnualIncrementalUnits');
        // the same ResultData responds to both units and sales, so we can use the same data for both
        $this->assertEquals(1500, $experiment->calculateBestCaseProjectedAnnualIncrementalSales(), 'calculateBestCaseProjectedAnnualIncrementalSales');
        $this->assertEquals(1000, $experiment->calculateMostLikelyProjectedAnnualIncrementalSales(), 'calculateMostLikelyProjectedAnnualIncrementalSales');
        $this->assertEquals(500, $experiment->calculateWorstCaseProjectedAnnualIncrementalSales(), 'calculateWorstCaseProjectedAnnualIncrementalSales');
    }

    protected function buildMockResultData(): ResultData
    {
        $resultData = $this->createPartialMock(ResultData::class, ['calculateBaseline','isTypeAsinUnits','isTypeAsinSales']);
        $resultData
            ->method('calculateBaseline')
            ->willReturn(0.5)
        ;
        $resultData
            ->method('isTypeAsinUnits')
            ->willReturn(true)
        ;
        $resultData
            ->method('isTypeAsinSales')
            ->willReturn(true)
        ;

        $resultData
            ->setEndDate(new \DateTime('2023-01-02'))
            ->setPerformanceLower(1000)
            ->setPerformance(2000)
            ->setPerformanceUpper(3000)
        ;
        return $resultData;
    }


    // @todo - refactor to use same ResultData as used in AmazonParseExperimentResultsTest
    //         can then use the expected values from that test (as taken from Amazon) and remove
    //         the Integration test testCalculatedExperimentValues() (because it will be covered here instead)
//    private Experiment $experiment;
//
//    protected function setUp(): void
//    {
//        parent::setUp();
//        $this->experiment = new Experiment();
//
//        // ASIN-SALES
//        $resultData1 = new ResultData();
//        $resultData1->setControlSamples(126435.0);
//        $resultData1->setControlSum(26557.030000001);
//        $resultData1->setControlIndicatorCount(1377.0);
//        $resultData1->setTreatmentSamples(126242.0);
//        $resultData1->setTreatmentSum(28007.780000001);
//        $resultData1->setTreatmentIndicatorCount(1481.0);
//        $resultData1->setResultMetricType('TOTAL_SALES');
//        $resultData1->setDimensions('ASIN');
//        $resultData1->setPerformanceLower(-0.02429288921209);
//        $resultData1->setPerformance(0.045912836055396);
//        $resultData1->setPerformanceUpper(0.11611856132288);
//        $resultData1->setStartDate(new \DateTime('2023-07-26 00:00:00'));
//        $resultData1->setEndDate(new \DateTime('2023-08-09 00:00:00'));
//
//        // ASIN-UNIT
//        $resultData2 = new ResultData();
//        $resultData2->setControlSamples(126435.0);
//        $resultData2->setControlSum(8452.4199999999);
//        $resultData2->setControlIndicatorCount(459.0);
//        $resultData2->setTreatmentSamples(126242.0);
//        $resultData2->setTreatmentSum(9992.2599999999);
//        $resultData2->setTreatmentIndicatorCount(542.0);
//        $resultData2->setResultMetricType('PAID_UNITS');
//        $resultData2->setDimensions('ASIN');
//        $resultData2->setPerformanceLower(0.026630628696226);
//        $resultData2->setPerformance(0.14974553154999);
//        $resultData2->setPerformanceUpper(0.27286043440376);
//        $resultData2->setStartDate(new \DateTime('2023-07-26 00:00:00'));
//        $resultData2->setEndDate(new \DateTime('2023-08-09 00:00:00'));
//
//        // ASIN-SEARCH-UNIT
//        $resultData3 = new ResultData();
//        $resultData3->setControlSamples(126435.0);
//        $resultData3->setControlSum(468.0);
//        $resultData3->setControlIndicatorCount(459.0);
//        $resultData3->setTreatmentSamples(126242.0);
//        $resultData3->setTreatmentSum(553.0);
//        $resultData3->setTreatmentIndicatorCount(542.0);
//        $resultData3->setResultMetricType('PAID_UNITS');
//        $resultData3->setDimensions('ASIN,FIRST_DISCOVERY_SEARCH');
//        $resultData3->setPerformanceLower(0.025381232745816);
//        $resultData3->setPerformance(0.15019803582713);
//        $resultData3->setPerformanceUpper(0.15019803582713);
//        $resultData3->setStartDate(new \DateTime('2023-07-26 00:00:00'));
//        $resultData3->setEndDate(new \DateTime('2023-08-09 00:00:00'));
//
//        // ASIN-SEARCH-SALES
//        $resultData4 = new ResultData();
//        $resultData4->setControlSamples(126435.0);
//        $resultData4->setControlSum(1433.0);
//        $resultData4->setControlIndicatorCount(1377.0);
//        $resultData4->setTreatmentSamples(126242.0);
//        $resultData4->setTreatmentSum(1522.0);
//        $resultData4->setTreatmentIndicatorCount(1481.0);
//        $resultData4->setResultMetricType('TOTAL_SALES');
//        $resultData4->setDimensions('ASIN,FIRST_DISCOVERY_SEARCH');
//        $resultData4->setPerformanceLower(-0.017190485612405);
//        $resultData4->setPerformance(0.052028462721908);
//        $resultData4->setPerformanceUpper(0.12124741105622);
//        $resultData4->setStartDate(new \DateTime('2023-07-26 00:00:00'));
//        $resultData4->setEndDate(new \DateTime('2023-08-09 00:00:00'));
//
//        $this->experiment->addResultData($resultData1);
//        $this->experiment->addResultData($resultData2);
//        $this->experiment->addResultData($resultData3);
//        $this->experiment->addResultData($resultData4);
//
//        $target1 = new Target();
//        $target1->setProductTitle('Product1');
//
//        $target2 = new Target();
//        $target2->setProductTitle('Product2');
//
//        $this->experiment->addTarget($target1);
//        $this->experiment->addTarget($target2);
//    }
//
//    public function testGetExperimentDurationInWeeks(): void
//    {
//        $this->experiment->setStartDate('2023-01-01 07:00:00');
//        $this->experiment->setEndDate('2023-01-29 23:03:49');
//
//        $expectedWeeks = '4 weeks';
//        $this->assertEquals($expectedWeeks, $this->experiment->calculateExperimentDurationInWeeks(), 'Experiment duration should be 4 weeks');
//    }
//
//    public function testGetExperimentDurationInDays(): void
//    {
//        $this->experiment->setStartDate('2023-07-26 07:00:00');
//        $this->experiment->setEndDate('2023-08-18 23:03:49');
//
//        $expectedDays = 23;
//        $this->assertEquals($expectedDays, $this->experiment->calculateExperimentDurationInDays(), 'Experiment duration should be 23 days');
//    }
//
//    public function testGetAllExperimentTargetProductTitle(): void
//    {
//        $expectedTitles = 'Product1 | Product2';
//        $this->assertEquals($expectedTitles, $this->experiment->getAllExperimentTargetProductTitle());
//    }
//
//    public function testGetAllExperimentResultDataControlSample(): void
//    {
//        $expectedResult = 505740;
//        $this->assertEquals($expectedResult, $this->experiment->calculateAllControlSample(), 'Total of control samples should be returned');
//    }
//
//    public function testGetAllExperimentResultDataTreatmentSample(): void
//    {
//        $expectedResult = 504968;
//        $this->assertEquals($expectedResult, $this->experiment->calculateAllTreatmentSample(), 'Total of treatment samples should be returned');
//    }
//
//    public function testGetTotalOfControlSampleAndTreatmentSample(): void
//    {
//        $expectedResult = 1010708;
//        $this->assertEquals($expectedResult, $this->experiment->calculateTotalOfControlSampleAndTreatmentSample(), 'Total of control and treatment samples should be returned');
//    }
//
//    public function testGetAverageControlsPerVisitor(): void
//    {
//        $expectedResult = 0.07298305453395203;
//        $this->assertEquals($expectedResult, $this->experiment->calculateAveragePerVisitorControl(), 'Average controls per visitor should be returned');
//    }
//
//    public function testGetAverageTreatmentsPerVisitor(): void
//    {
//        $expectedResult = 0.07936154370178089;
//        $this->assertEquals($expectedResult, $this->experiment->calculateAveragePerVisitorTreatment(), 'Average treatments per visitor should be returned');
//    }
//
//    public function testGetDifferenceOfConversionRates(): void
//    {
//        $expectedResult = 0.0007517411401782989;
//        $this->assertEquals($expectedResult, $this->experiment->calculateDifferenceOfControlAndTreatmentConversionRates(), 'Difference of conversion rates should be returned');
//    }
//
//    public function testGetUnitsSoldAsinControl(): void
//    {
//        $expectedResult = 8452.4199999999;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldAsinControl(), 'Units sold asin control should be returned');
//    }
//
//    public function testGetUnitsSoldAsinTreatment(): void
//    {
//        $expectedResult = 9992.2599999999;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldAsinTreatment(), 'Units sold asin treatment should be returned');
//    }
//
//    public function testGetDifferenceOfUnitsSoldAsin(): void
//    {
//        $expectedResult = 1539.8400000000001;
//        $this->assertEquals($expectedResult, $this->experiment->calculateDifferenceOfUnitsSoldAsinControlAndTreatment(), 'Difference of units sold asin should be returned');
//    }
//
//    public function testGetUnitsSoldFromSearchAsinControl(): void
//    {
//        $expectedResult = 468.0;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldFromSearchAsinControl(), 'Units sold from search asin control should be returned');
//    }
//
//    public function testGetUnitsSoldFromSearchAsinTreatment(): void
//    {
//        $expectedResult = 553.0;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldFromSearchAsinTreatment(), 'Units sold from search asin treatment should be returned');
//    }
//
//    public function testGetDifferenceOfUnitsSoldFromSearchAsin(): void
//    {
//        $expectedResult = 85.0;
//        $this->assertEquals($expectedResult, $this->experiment->calculateDifferenceOfUnitsSoldFromSearchAsinControlAndTreatment(), 'Difference of units sold from search asin should be returned');
//    }
//
//    public function testGetUnitsSoldFromSearchAsinControlPercentage(): void
//    {
//        $expectedResult = 0.4583741429970617;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldFromSearchAsinControlPercentage(), 'Units sold from search asin control percentage should be returned');
//    }
//
//    public function testGetUnitsSoldFromSearchAsinTreatmentPercentage(): void
//    {
//        $expectedResult = 0.5416258570029383;
//        $this->assertEquals($expectedResult, $this->experiment->calculateUnitsSoldFromSearchAsinTreatmentPercentage(), 'Units sold from search asin treatment percentage should be returned');
//    }
//
//    public function testGetSalesAsinControl(): void
//    {
//        $expectedResult = 26557.030000001;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesAsinControl(), 'Sales asin control should be returned');
//    }
//
//    public function testGetSalesAsinTreatment(): void
//    {
//        $expectedResult = 28007.780000001;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesAsinTreatment(), 'Sales asin treatment should be returned');
//    }
//
//    public function testGetSalesFromSearchAsinControl(): void
//    {
//        $expectedResult = 1433.0;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesFromSearchAsinControl(), 'Sales from search asin control should be returned');
//    }
//
//    public function testGetSalesFromSearchAsinTreatment(): void
//    {
//        $expectedResult = 1522.0;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesFromSearchAsinTreatment(), 'Sales from search asin treatment should be returned');
//    }
//
//    public function testGetSalesFromSearchAsinControlPercentage(): void
//    {
//        $expectedResult = 0.48494077834179355;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesFromSearchAsinControlPercentage(), 'Sales from search asin control percentage should be returned');
//    }
//
//    public function testGetSalesFromSearchAsinTreatmentPercentage(): void
//    {
//        $expectedResult = 0.5150592216582064;
//        $this->assertEquals($expectedResult, $this->experiment->calculateSalesFromSearchAsinTreatmentPercentage(), 'Sales from search asin treatment percentage should be returned');
//    }
//
//    public function testGetBestCaseProjectedAnnualIncrementalUnits(): void
//    {
//        $expectedResult = -717480.5105049454;
//        $this->assertEquals($expectedResult, $this->experiment->calculateBestCaseProjectedAnnualIncrementalUnits(), 'Best case projected annual incremental units should be returned');
//    }
//
//    public function testGetMostLikelyProjectedAnnualIncrementalUnits(): void
//    {
//        $expectedResult = -4034433.4956518393;
//        $this->assertEquals($expectedResult, $this->experiment->calculateMostLikelyProjectedAnnualIncrementalUnits(), 'Most likely projected annual incremental units should be returned');
//    }
//
//    public function testGetWorstCaseProjectedAnnualIncrementalUnits(): void
//    {
//        $expectedResult = -7351386.480798895;
//        $this->assertEquals($expectedResult, $this->experiment->calculateWorstCaseProjectedAnnualIncrementalUnits(), 'Worst case projected annual incremental units should be returned');
//    }
//
//    public function testGetBestCaseProjectedAnnualIncrementalSales(): void
//    {
//        $expectedResult = 654497.2990442575;
//        $this->assertEquals($expectedResult, $this->experiment->calculateBestCaseProjectedAnnualIncrementalSales(), 'Best case projected annual incremental sales should be returned');
//    }
//
//    public function testGetMostLikelyProjectedAnnualIncrementalSales(): void
//    {
//        $expectedResult = -1236980.3742719656;
//        $this->assertEquals($expectedResult, $this->experiment->calculateMostLikelyProjectedAnnualIncrementalSales(), 'Most likely projected annual incremental sales should be returned');
//    }
//
//    public function testGetWorstCaseProjectedAnnualIncrementalSales(): void
//    {
//        $expectedResult = -3128458.047588135;
//        $this->assertEquals($expectedResult, $this->experiment->calculateWorstCaseProjectedAnnualIncrementalSales(), 'Worst case projected annual incremental sales should be returned');
//    }
//
//    public function testGetLatestResultData(): void
//    {
//        $resultDataArray = $this->experiment->getLatestResultData();
//        $this->assertIsArray($resultDataArray, 'getLatestResultData should return an array');
//
//        if (!empty($resultDataArray)) {
//            foreach ($resultDataArray as $resultData) {
//                $this->assertInstanceOf(ResultData::class, $resultData, 'Array elements should be instances of ResultData');
//            }
//        }
//    }
//
//    public function testGetLatestAsinUnitResultData(): void
//    {
//        $expectedResult = $this->experiment->getResultData()->filter(function ($resultData) {
//            return $resultData->getDimensions() == 'ASIN' && $resultData->getResultMetricType() == 'PAID_UNITS';
//        })->first();
//        $this->assertEquals($expectedResult, $this->experiment->getLatestAsinUnitResultData(), 'Latest asin unit result data should be returned');
//    }
//
//    public function testGetLatestAsinSalesResultData(): void
//    {
//        $expectedResult = $this->experiment->getResultData()->filter(function ($resultData) {
//            return $resultData->getDimensions() == 'ASIN' && $resultData->getResultMetricType() == 'TOTAL_SALES';
//        })->first();
//        $this->assertEquals($expectedResult, $this->experiment->getLatestAsinSalesResultData(), 'Latest asin sales result data should be returned');
//    }
//
//    public function testGetLatestSearchAsinUnitResultData(): void
//    {
//        $expectedResult = $this->experiment->getResultData()->filter(function ($resultData) {
//            return $resultData->getDimensions() == 'ASIN,FIRST_DISCOVERY_SEARCH' && $resultData->getResultMetricType() == 'PAID_UNITS';
//        })->first();
//        $this->assertEquals($expectedResult, $this->experiment->getLatestSearchAsinUnitResultData(), 'Latest search asin unit result data should be returned');
//    }
//
//    public function testGetLatestSearchAsinSalesResultData(): void
//    {
//        $expectedResult = $this->experiment->getResultData()->filter(function ($resultData) {
//            return $resultData->getDimensions() == 'ASIN,FIRST_DISCOVERY_SEARCH' && $resultData->getResultMetricType() == 'TOTAL_SALES';
//        })->first();
//        $this->assertEquals($expectedResult, $this->experiment->getLatestSearchAsinSalesResultData(), 'Latest search asin sales result data should be returned');
//    }
//
//    public function testCalculateAverage(): void
//    {
//        $resultData = new ResultData();
//        $resultData
//            ->setControlSamples(10)
//            ->setControlIndicatorCount(20)       // 2x samples
//            ->setControlSum(7)                          // 70% of samples
//            ->setTreatmentSamples(100)
//            ->setTreatmentIndicatorCount(300)  // 3x samples
//            ->setTreatmentSum(75)                     // 75% of samples
//        ;
//        $experiment = new Experiment();
//        $experiment->addResultData($resultData);
//
//        $this->assertEquals(2, $experiment->calculateConversionRateOfControl(), 'Control Conversion rate is indicators/samples');
//        $this->assertEquals(0.7, $experiment->calculateAveragePerVisitorControl(), 'Control Average is sum/samples');
//        $this->assertEquals(3, $experiment->calculateConversionRateOfTreatment(), 'Treatment Conversion rate is indicators/samples');
//        $this->assertEquals(0.75, $experiment->calculateAveragePerVisitorTreatment(), 'Treatment Average is sum/samples');
//
//        // if samples is 0, average should be 0 (instead of DIV0 error)
//        $resultData->setControlSamples(0);
//        $this->assertEquals(0, $experiment->calculateConversionRateOfControl(), 'Control Conversion rate is indicators/samples');
//        $this->assertEquals(0, $experiment->calculateAveragePerVisitorControl(), 'Control Average is sum/samples');
//        $this->assertEquals(3, $experiment->calculateConversionRateOfTreatment(), 'Treatment Conversion rate is indicators/samples');
//        $this->assertEquals(0.75, $experiment->calculateAveragePerVisitorTreatment(), 'Treatment Average is sum/samples');
//
//        $resultData->setTreatmentSamples(0);
//        $this->assertEquals(0, $experiment->calculateConversionRateOfTreatment(), 'Treatment Conversion rate is indicators/samples');
//        $this->assertEquals(0, $experiment->calculateAveragePerVisitorTreatment(), 'Treatment Average is sum/samples');
//    }
}