{% extends '@SonataAdmin/CRUD/base_list_field.html.twig' %}

{% block field %}
    {% apply spaceless %}
        {% set asinPreview = [] %}

        {% if value is not empty %}
            {# Filter ASINs and store previews #}
            {#   AscCreatorConnectionsCampaign.filterMetricsAndBuildChildrenMetricsArray()      #}
            {% set campaignMetrics = object.filterMetricsAndBuildChildrenMetricsArray(value) %}
            {% set asinPreview = campaignMetrics|map(m => m['asin'])|slice(0, 5) %}

            <div class="metrics-container">
                <span class="asin-preview">
                    {% for asin in asinPreview %}
                        <a href="https://www.amazon.com/dp/{{ asin|trim }}" target="_blank" rel="noopener noreferrer">
                            {{ asin|trim }}
                        </a>{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </span>
                <div class="metrics-content" style="display: none;">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>ASIN</th>
                            <th>Clicks</th>
                            <th>Orders</th>
                            <th>Sales</th>
                            <th>Spend</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for metric in campaignMetrics %}
                            <tr>
                                <td><a href="https://www.amazon.com/dp/{{ metric['asin']|trim }}" target="_blank" rel="noopener noreferrer">{{ metric['asin'] }}</a></td>
                                <td>{{ metric['clicks'] }}</td>
                                <td>{{ metric['orders'] }}</td>
                                <td>{{ metric['sales']|number_format(2) }}</td>
                                <td>{{ metric['spend']|number_format(2) }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if asinPreview|length > 0 %}
                    <span class="bg-blue text-center text-bold fa fa-plus campaign-toggle toggle-metrics" data-toggle></span>
                {% endif %}
            </div>
        {% endif %}
    {% endapply %}
{% endblock %}
