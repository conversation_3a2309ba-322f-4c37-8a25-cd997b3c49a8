<script>
    $(document).ready(function() {
        function addShipmentButton() {
            if (
                $('form[action^="/controlroom/supplierOrder/"]').length > 0 &&
                $('form[action*="/edit?"]').length > 0
            ) {
                const shipmentButtonHtml = `
                    <button type="button" id="submitShipmentForm" class="btn btn-success btn-sm sonata-ba-action">
                        <i class="fas fa-truck"></i> Create Onward Shipment
                    </button>
                    <form id="create-shipment-form" method="POST" action="/controlroom/supplierOrder/batch" target="_blank" style="display: none;">
                        <input type="hidden" name="_sonata_csrf_token" value="{{ csrf_token('sonata.batch') }}">
                        <input type="hidden" name="idx[]" value="{{ admin.getSubject().id }}">
                        <input type="hidden" name="action" value="createShipmentFromOrderAvailable">
                    </form>
                `;

                const itemsContainer = $('div[id*="_items"]');
                let buttonAdded = false;

                if (itemsContainer.length > 0) {
                    const formField = itemsContainer.find('span[id*="field_actions_"]');

                    if (formField.length > 0 && $('#submitShipmentForm').length === 0) {
                        formField.append(shipmentButtonHtml);
                        buttonAdded = true;
                    }
                }

                if (!buttonAdded && $('#submitShipmentForm').length === 0) {
                    const $onwardShipmentContainer = $('div[id*="_onwardShipment"]');
                    const $anyOnwardShipmentContainer = $onwardShipmentContainer.length > 0 ?
                        $onwardShipmentContainer : $('div[id*="_onwardShipment"].form-group');
                    if ($anyOnwardShipmentContainer.length > 0) {
                        $anyOnwardShipmentContainer.html(shipmentButtonHtml);
                        buttonAdded = true;
                    }
                }

                if (buttonAdded) {
                    $('#submitShipmentForm').click(function() {
                        $('#create-shipment-form').submit();
                    });
                }

                const $onwardShipmentOriginalContainer = $('div[id*="_onwardShipment"]');
                if (!buttonAdded && $onwardShipmentOriginalContainer.length > 0) {
                    $onwardShipmentOriginalContainer.empty();
                }
            }
        }

        addShipmentButton();
    });
</script>